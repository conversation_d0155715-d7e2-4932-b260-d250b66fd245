name: MBNB-V2 CI/CD Pipeline

on:
  push:
    branches: [ main, develop, staging ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  NODE_VERSION: '18'
  POSTGRES_VERSION: '15'
  REDIS_VERSION: '7'

jobs:
  # ============================================
  # QUALITY CHECKS
  # ============================================
  quality:
    name: 🔍 Code Quality
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v3
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📦 Install dependencies
        run: npm ci
        
      - name: 🎨 Lint code
        run: npm run lint
        
      - name: 📝 Type check
        run: npm run type-check
        
      - name: 🔒 Security audit
        run: npm audit --audit-level=moderate

  # ============================================
  # TESTS BACKEND
  # ============================================
  test-backend:
    name: 🧪 Tests Backend
    runs-on: ubuntu-latest
    needs: quality
    
    services:
      postgres:
        image: postgres:${{ env.POSTGRES_VERSION }}-alpine
        env:
          POSTGRES_USER: mbnb_test
          POSTGRES_PASSWORD: mbnb_test_2025
          POSTGRES_DB: mbnb_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:${{ env.REDIS_VERSION }}-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v3
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📦 Install dependencies
        run: npm ci
        
      - name: 🗃️ Setup database
        env:
          DATABASE_URL: postgresql://mbnb_test:mbnb_test_2025@localhost:5432/mbnb_test
        run: |
          npx prisma generate
          npx prisma migrate deploy
          npx prisma db seed
          
      - name: 🧪 Run unit tests
        env:
          NODE_ENV: test
          DATABASE_URL: postgresql://mbnb_test:mbnb_test_2025@localhost:5432/mbnb_test
          REDIS_URL: redis://localhost:6379
          JWT_SECRET: test_jwt_secret_2025
        run: npm run test:unit
        
      - name: 🔗 Run integration tests
        env:
          NODE_ENV: test
          DATABASE_URL: postgresql://mbnb_test:mbnb_test_2025@localhost:5432/mbnb_test
          REDIS_URL: redis://localhost:6379
          JWT_SECRET: test_jwt_secret_2025
        run: npm run test:integration
        
      - name: 📊 Generate coverage report
        run: npm run test:coverage
        
      - name: 📈 Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          files: ./coverage/lcov.info
          fail_ci_if_error: false

  # ============================================
  # TESTS SPÉCIFIQUES MBNB
  # ============================================
  test-mbnb-business:
    name: 🇲🇦 Tests Business Logic Mbnb
    runs-on: ubuntu-latest
    needs: quality
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v3
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📦 Install dependencies
        run: npm ci
        
      - name: 💰 Test Commission Split Fee
        run: npm run test -- --testPathPattern=commission
        
      - name: 🎯 Test TOP 100 Activities
        run: npm run test -- --testPathPattern=activities
        
      - name: 💳 Test Discounts Properties
        run: npm run test -- --testPathPattern=discount
        
      - name: 🏦 Test CMI Gateway
        run: npm run test -- --testPathPattern=cmi

  # ============================================
  # PERFORMANCE TESTS
  # ============================================
  performance:
    name: ⚡ Tests Performance
    runs-on: ubuntu-latest
    needs: [test-backend]
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v3
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📦 Install dependencies
        run: npm ci
        
      - name: 🏗️ Build application
        run: npm run build
        
      - name: 🚀 Start server
        run: |
          npm start &
          sleep 10
          
      - name: 📊 Run load tests (700-800 connections)
        run: |
          npm install -g artillery
          artillery quick --count 800 --num 10 http://localhost:3000/health
          
      - name: 📈 Check performance metrics
        run: |
          curl -f http://localhost:3000/metrics || exit 1

  # ============================================
  # BUILD DOCKER
  # ============================================
  build-docker:
    name: 🐳 Build Docker Images
    runs-on: ubuntu-latest
    needs: [test-backend, test-mbnb-business]
    if: github.event_name == 'push'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v3
        
      - name: 🔐 Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
          
      - name: 🏷️ Extract metadata
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: mbnb/backend
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix={{branch}}-
            
      - name: 🔨 Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: |
            NODE_ENV=production

  # ============================================
  # DEPLOY STAGING
  # ============================================
  deploy-staging:
    name: 🚀 Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build-docker]
    if: github.ref == 'refs/heads/staging'
    environment:
      name: staging
      url: https://staging.mbnb.ma
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v3
        
      - name: 🔧 Setup SSH
        uses: webfactory/ssh-agent@v0.5.4
        with:
          ssh-private-key: ${{ secrets.STAGING_SSH_KEY }}
          
      - name: 🚀 Deploy to staging server
        run: |
          ssh -o StrictHostKeyChecking=no ${{ secrets.STAGING_USER }}@${{ secrets.STAGING_HOST }} << 'EOF'
            cd /opt/mbnb-v2
            git pull origin staging
            docker-compose -f docker-compose.staging.yml pull
            docker-compose -f docker-compose.staging.yml up -d
            docker-compose -f docker-compose.staging.yml exec backend npx prisma migrate deploy
          EOF
          
      - name: ✅ Health check
        run: |
          sleep 30
          curl -f https://staging.mbnb.ma/health || exit 1

  # ============================================
  # DEPLOY PRODUCTION
  # ============================================
  deploy-production:
    name: 🎯 Deploy to Production
    runs-on: ubuntu-latest
    needs: [build-docker]
    if: github.ref == 'refs/heads/main'
    environment:
      name: production
      url: https://mbnb.ma
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v3
        
      - name: 🔧 Setup SSH
        uses: webfactory/ssh-agent@v0.5.4
        with:
          ssh-private-key: ${{ secrets.PRODUCTION_SSH_KEY }}
          
      - name: 🔵 Blue-Green Deployment
        run: |
          ssh -o StrictHostKeyChecking=no ${{ secrets.PRODUCTION_USER }}@${{ secrets.PRODUCTION_HOST }} << 'EOF'
            cd /opt/mbnb-v2
            
            # Pull latest changes
            git pull origin main
            
            # Build new version (green)
            docker-compose -f docker-compose.prod.yml build backend-green
            
            # Start green version
            docker-compose -f docker-compose.prod.yml up -d backend-green
            
            # Wait for health check
            sleep 30
            curl -f http://localhost:3001/health || exit 1
            
            # Switch traffic to green
            docker exec nginx nginx -s reload
            
            # Stop blue version
            docker-compose -f docker-compose.prod.yml stop backend-blue
            
            # Rename green to blue for next deployment
            docker tag mbnb/backend:green mbnb/backend:blue
          EOF
          
      - name: ✅ Production health check
        run: |
          sleep 10
          curl -f https://mbnb.ma/health || exit 1
          
      - name: 📊 Send deployment notification
        if: always()
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          text: 'Production deployment ${{ job.status }}'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}

  # ============================================
  # MONITORING & ALERTS
  # ============================================
  monitoring:
    name: 📊 Setup Monitoring
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: 🔔 Configure Sentry release
        uses: getsentry/action-release@v1
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ORG: mbnb
          SENTRY_PROJECT: backend
        with:
          environment: production
          version: ${{ github.sha }}
          

## Refonte Frontend Mbnb — Suivi d’implémentation factuel et rigoureux

### Cadre non négociable
- **Objectif**: Frontend déployable en production sans régression, avec sécurité, qualité et performances mesurables.
- **Interdits**: aucun mock/stub/dummy/placeholders, pas de TODO ultérieurs, pas de duplications/redondances, pas de perte d’algorithmes/logiques métier/PI.
- **Méthode**: exécution par phases atomiques; chaque tâche a des critères d’acceptation et des commandes de vérification reproductibles.

### Obligations NON NÉGOCIABLES
- **Zéro automatisme d’implémentation/correction**: pas d’outils "Task" ni de scripts de correction automatique. Toutes les corrections sont faites manuellement, avec preuves et diff contrôlés.
- **Aucun mock/code temporaire/commentaires de remplacement**: pas de placeholders, pas de "TO BE IMPLEMENTED", pas de "MISSING SERVICE" commentés. Une seule passe efficace par changement, vérifiable.
- **Zéro régression / zéro duplication / zéro perte PI**: aucune régression fonctionnelle, aucune duplication de logique/type métier, aucune perte d’algorithmes ou de propriété intellectuelle Mbnb.
- **Validation par AST (ts-morph) en lecture seule**: analyses statiques profondes sans écriture automatique. Les rapports guident les corrections manuelles (aucun auto-fix).

### Schéma d’architecture (référence de vérification)

FRONTEND (esclave) ←── API REST ──→ BACKEND (maître)
      ↓                                                                          ↓
  Next.js App                                                             FASTIFY SERVER
      ↓                                                                           ↓
  React Hooks ←─── Types Sync ───→                                   MODULITH ARCHITECTURE
      ↓                                                                           ↓
  UI Components                                                     MODULE INTERFACES
      ↓                                                                           ↓
  State Mgmt                                              DIRECT SERVICE DEPENDENCY INJECTION
                                                                                    ↓
                                                                  SERVICE LAYER (Business Logic)
                                                                                    ↓
                                                            BARREL EXPORT LAYER (prisma-exports.ts)
                                                                                    ↓
                                                                PRISMA ORM (Interface unique DB)
                                                                                    ↓
                                                                      POSTGRESQL DATABASE

### État de référence vérifié (point de départ)
- **Codebase**: `apps/frontend/*`
- **XSS potentiel** via `dangerouslySetInnerHTML` dans 5 TSX + 1 HTML de rapport:
  - `apps/frontend/components/Analytics.tsx`
  - `apps/frontend/app/layout.tsx`
  - `apps/frontend/components/SEO/MbnbSEO.tsx`
  - `apps/frontend/components/ui/FormBuilder.tsx`
  - `apps/frontend/components/ui/CodeEditor.tsx`
  - `apps/frontend/playwright-report/index.html` (rapport statique)
- **Assets**: `apps/frontend/public/Storytelling/` (~3232 fichiers), `apps/frontend/Storytelling/optimized/` (~2976 fichiers)
- **UI primitives**: `apps/frontend/components/ui/` (96 fichiers)
- **Tests**: 5 fichiers e2e sous `apps/frontend/tests/e2e/`; aucun test unitaire détecté
- **Types front**: `apps/frontend/types/*` + `apps/frontend/types/mbnb-backend-sync.types.ts` (risque de désync avec backend)

### Mesures de vérification (à rejouer après chaque phase)
```bash
# Typecheck
npm run -w apps/frontend tsc --noEmit

# Lint
npm run -w apps/frontend lint

# E2E (Playwright)
npm run -w apps/frontend test:e2e || npx -w apps/frontend playwright test

# Images >1MB
find apps/frontend/public -type f -size +1M | wc -l

# Recherche XSS
rg "dangerouslySetInnerHTML" apps/frontend -n

# Recherche patterns sensibles (client only)
rg -n "(API_KEY|SECRET|Bearer\s+[A-Za-z0-9\-\._]+)" apps/frontend
```

### Validation AST (ts-morph) — lecture seule, sans auto‑fix
- Objectifs: détection de duplications réelles, analyse syntaxique précise, cartographie des imports/exports, détection d’API publiques internes non autorisées.
- Contraintes: aucune écriture sur le code; génération d’un rapport JSON/MD uniquement pour guider des corrections manuelles.
- Sorties attendues: 
  - Liste des symboles dupliqués (mêmes signatures dans fichiers distincts)
  - Graphes d’imports par feature (détection cycles/accès transverses non souhaités)
  - Emplacements d’API non conformes (types métier redéfinis côté front)
  - Inventaire `dangerouslySetInnerHTML` via AST (confirmé vs grep)
  - Métriques par fichier (nœuds, longueurs, complexité approximative)
- Exécution (exemple — lecture seule):
```bash
# Lecture seule via ts-node (aucune écriture):
npx -w apps/frontend ts-node scripts/ast/audit-frontend.ts --readOnly --report out/ast-report.json
```
- Critères d’acceptation: rapport généré, aucune écriture; toutes les corrections découlent d’interventions manuelles vérifiées par diff et tests.

---

## Phase 1 — Sécurité et intégrité immédiates (J+1)

### 1. Élimination XSS sans régression
- **Actions**:
  - Supprimer `dangerouslySetInnerHTML` dans les 5 TSX listés.
  - Si rendu HTML indispensable, convertir en JSX contrôlé ou appliquer un sanitizer strict en amont (pas d’HTML injecté direct côté client).
  - Laisser `playwright-report/index.html` (non exposé à l’utilisateur final).
- **Vérifications**:
  - `rg "dangerouslySetInnerHTML" apps/frontend -n` → 0 résultat TSX.
  - E2E ciblés pour layout/SEO/form afin de détecter les régressions.
- **Critères d’acceptation**:
  - 0 occurrence `dangerouslySetInnerHTML` dans `.tsx`.
  - Rendu visuel fonctionnel inchangé sur pages impactées.

### 2. Secrets et configuration client
- **Actions**:
  - Revue des occurrences sensibles: `rg -n "(API_KEY|SECRET|Bearer\s+[A-Za-z0-9\-\._]+)" apps/frontend`.
  - Toute clé détectée → migration `.env.local` et usage `NEXT_PUBLIC_*` seulement si strictement public. Interdire clés privées côté client.
  - Contrôle que les bundles ne contiennent aucun secret privé.
- **Vérifications**:
  - Recherche ci‑dessus → seulement des références non sensibles (types/doc) ou supprimées.
  - Build d’inspection: `ANALYZE=true npm run -w apps/frontend build` et revue des chunks.
- **Critères d’acceptation**:
  - 0 secret privé exposé dans le bundle.

### 3. ESLint/TypeScript stricts
- **Actions**:
  - Réparer ESLint (se baser sur `ANALYSE_ESLINT_CARTOGRAPHIE_COMPLETE.md`, `eslint_output.txt`).
  - Appliquer `--fix` sûrs et corriger manuellement le reste. Interdire `eslint-disable` non justifié.
- **Vérifications**:
  - `npm run -w apps/frontend lint` → 0 erreurs (warnings tolérés si justifiés et tracés).
  - `npm run -w apps/frontend tsc --noEmit` → 0 erreurs.
- **DoD Phase 1**: XSS éliminé (tsx), secrets clients assainis, lint/tsc OK, e2e critiques verts.

---

## Phase 2 — Synchronisation des types avec backend (Semaine 1)

### 4. Source de vérité des types
- **Actions**:
  - Publier un paquet `@mbnb/types` (monorepo) à partir de `src/types/index.ts` backend.
  - Remplacer `apps/frontend/types/*` métier par imports depuis `@mbnb/types`; supprimer doublons.
  - Optionnel si disponible: générer un client typé (OpenAPI/Zod) depuis contrats API réels; sinon rester sur `@mbnb/types`.
- **Vérifications**:
  - `rg -n "export type .*" apps/frontend/types` → uniquement types UI spécifiques.
  - `npm run -w apps/frontend tsc --noEmit` → 0 erreurs.
- **Critères d’acceptation**:
  - Tous les types métier proviennent d’une source unique (`@mbnb/types` ou client généré).

### 5. CI Typecheck
- **Actions**:
  - Jobs CI séparés: `lint`, `typecheck`, `test` sur le frontend; bloquants.
- **Vérifications**:
  - Exécutions CI vertes, logs archivés.
- **DoD Phase 2**: types unifiés, CI typecheck stricte active.

---

## Phase 3 — Architecture feature‑based et anti‑duplication (Semaines 1‑2)

### 6. Réarchitecture feature‑based
- **Actions**:
  - Introduire `apps/frontend/features/{auth,search,property,reservation,payment,loyalty,activities,admin,...}/`.
  - Déplacer composants de `components/*` vers leur feature; conserver `components/ui/` pour primitives réutilisables.
  - Ajouter des barrel exports par feature.
- **Vérifications**:
  - `rg -n "import .* from '../../'" apps/frontend` → forte réduction.
  - Imports cycliques: `madge --circular apps/frontend` → 0.
- **Critères d’acceptation**:
  - Chaque composant est rattaché à une feature ou au kit UI; imports propres.

### 7. Réduction composants obèses et duplication
- **Actions**:
  - Lister composants >500 lignes: `find apps/frontend -name "*.tsx" -exec bash -lc 'wc -l "{}"' \; | sort -nr | head -n 100`.
  - Scinder logique/présentation; extraire hooks dans `features/*/hooks/`.
  - Dé‑dupliquer via utilitaires/UI partagés.
- **Vérifications**:
  - Aucun composant >500 lignes (hors exceptions justifiées).
  - Duplications JSX/logic réduites (revue par `rg` et diff).
- **DoD Phase 3**: feature‑based en place, duplication réduite, pas de cycles, lisibilité accrue.

---

## Phase 4 — Performances (Semaines 2‑3)

### 8. Images et assets
- **Actions**:
  - Auditer >1MB: `find apps/frontend/public -type f -size +1M` (recensement manuel, table de décision fichier par fichier).
  - Conversion manuelle contrôlée vers AVIF/WebP pour médias effectivement utilisés; supprimer explicitement les médias non référencés après vérification manuelle des usages.
  - Généraliser `next/image`, lazy par défaut, `sizes` appropriés (revues manuelles des composants affichant des médias).
- **Vérifications**:
  - `find apps/frontend/public -type f -size +1M | wc -l` → baisse mesurée.
  - Lighthouse sur pages lourdes: conserver rapports (pas de promesse chiffrée sans mesure).
- **Critères d’acceptation**:
  - Aucune page critique ne charge >10MB d’assets initiaux.

### 9. Bundle / code splitting
- **Actions**:
  - Activer `@next/bundle-analyzer`; introduire `next/dynamic` ciblé; réduire vendors.
  - Supprimer dépendances inutiles et dead code.
- **Vérifications**:
  - Analyzer: réduction du chunk principal et du total initial (captures sauvegardées).
  - `npm run -w apps/frontend build` comparé avant/après.
- **DoD Phase 4**: assets optimisés, bundle scindé, rapports d’analyse conservés.

---

## Phase 5 — Qualité logicielle (Semaines 2‑3)

### 10. Tests unitaires utiles (ciblés)
- **Actions**:
  - Couvrir stores Zustand (`stores/*.ts`), helpers (`lib/*.ts`), hooks critiques (`hooks/*.ts`), UI atomiques.
  - Viser des tests empêchant des régressions réelles (ex: formatage devise, compteur invités).
- **Vérifications**:
  - `npm run -w apps/frontend test:unit` → vert; snapshots stables.
- **Critères d’acceptation**:
  - Chemins critiques couverts; pas d’objectif de % cosmétique non pertinent.

### 11. E2E robustes
- **Actions**:
  - Stabiliser 5 e2e existants; ajouter scénarios essentiels: home → search → property → reservation (sans paiement), auth happy path.
- **Vérifications**:
  - `npx -w apps/frontend playwright test` → vert; en CI.
- **DoD Phase 5**: protections unitaires + e2e sur parcours critique; CI verte.

---

## Phase 6 — Sécurité front avancée (Semaine 3)

### 12. Headers & CSP Next
- **Actions**:
  - `next.config.js`: définir `headers()` (CSP, `X-Content-Type-Options`, `Referrer-Policy`, `Permissions-Policy`).
  - Aligner CSP avec besoins scripts (éviter `unsafe-inline` si possible).
- **Vérifications**:
  - `curl -I http://localhost:3010` → headers présents.
  - Console navigateur: 0 erreurs CSP.

### 13. Entrées utilisateur
- **Actions**:
  - Validation stricte formulaires; interdiction d’injection HTML; encodage par défaut.
- **Vérifications**:
  - Tests unitaires sur validations; revue des champs riches.
- **DoD Phase 6**: CSP active, inputs sécurisés, tests verts.

---

## Gouvernance, non‑régression et PI

- **Non‑régression**:
  - Diff visuel sur pages clés avant/après; justification si changement voulu.
  - E2E couvrent parcours essentiels; merge bloqué si échecs.
- **Propriété intellectuelle / logique métier**:
  - Aucun algorithme/logique métier supprimé ou altéré sans motif; pas de duplication front d’une logique déjà côté backend.
- **Anti‑duplication**:
  - Interdiction de réintroduire des types métier côté front; audit régulier via `rg` et `madge`.
- **Documentation**:
  - Mettre à jour ce document après chaque phase avec chiffres réels (images >1MB, tailles bundles, résultats lint/ts/test).
  - Joindre les rapports d’AST (ts-morph) en lecture seule aux commits concernés; toute correction référencera une entrée de rapport.

---

## Journal de suivi (remplir au fur et à mesure)

| Date | Phase | Action réalisée | Preuve/commande | Résultat |
|------|-------|-----------------|-----------------|----------|
|      | 1     | XSS supprimés (`dangerouslySetInnerHTML`) | `rg "dangerouslySetInnerHTML" apps/frontend -n` | 0 occurrences TSX |
|      | 1     | Secrets clients assainis | Analyzer build + `rg` | 0 secrets privés dans bundles |
|      | 1     | ESLint/TS stricts OK | `lint` + `tsc --noEmit` | 0 erreurs |
|      | 2     | Types unifiés via `@mbnb/types` | `tsc --noEmit` | 0 erreurs |
|      | 3     | Feature-based en place | `madge --circular` | 0 cycles |
|      | 3     | Composants >500 lignes réduits | `find ... wc -l` | Conforme |
|      | 4     | Images >1MB réduites | `find ... -size +1M` | Nombre ↓ |
|      | 4     | Bundle scindé | Analyzer rapports | Poids ↓ |
|      | 5     | Tests unitaires utiles | `test:unit` | Vert |
|      | 5     | E2E parcours critique | Playwright | Vert |
|      | 6     | CSP/headers Next | `curl -I` | Présents |

---

## Résultats attendus (vérifiables, sans promesses non mesurées)
- **Sécurité**: 0 `dangerouslySetInnerHTML` en TSX, 0 secret privé exposé, CSP active.
- **Qualité**: `lint` et `tsc` sans erreurs; tests unitaires/e2e verts.
- **Architecture**: feature‑based claire; pas de cycles; duplication réduite.
- **Performances**: baisse mesurée des images >1MB et des plus gros chunks; build réussi.



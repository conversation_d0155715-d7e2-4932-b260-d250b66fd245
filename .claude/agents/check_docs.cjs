#!/usr/bin/env node

/**
 * SPÉCIALISTE DOCUMENTATION TECHNIQUE
 * Vérification RÉELLE de la documentation
 */

const fs = require('fs');
const path = require('path');

const BASE_PATH = process.cwd();
const DOCS_PATH = path.join(BASE_PATH, 'docs');

class DocumentationChecker {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.success = [];
    this.stats = {
      totalDocs: 0,
      criticalDocs: 0,
      moduleDocs: 0,
      duplicates: 0
    };
  }

  // VÉRIFIER STRUCTURE DOCUMENTATION
  checkDocumentationStructure() {
    console.log('📚 Vérification structure documentation...\n');
    
    if (!fs.existsSync(DOCS_PATH)) {
      this.errors.push('❌ Dossier docs n\'existe pas');
      return false;
    }
    
    // Compter tous les .md
    const countMdFiles = (dir) => {
      let count = 0;
      if (!fs.existsSync(dir)) return count;
      
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        if (fs.existsSync(fullPath) && fs.statSync(fullPath).isDirectory()) {
          count += countMdFiles(fullPath);
        } else if (item.endsWith('.md')) {
          count++;
        }
      }
      return count;
    };
    
    this.stats.totalDocs = countMdFiles(BASE_PATH);
    console.log(`   ${this.stats.totalDocs} documents .md trouvés au total`);
    
    // Vérifier dossiers attendus
    const expectedFolders = {
      'business': 'Modèle économique',
      'legacy': 'Propriété intellectuelle',
      'modules': 'Documentation modules',
      'api': 'Documentation API'
    };
    
    for (const [folder, desc] of Object.entries(expectedFolders)) {
      const folderPath = path.join(DOCS_PATH, folder);
      
      if (fs.existsSync(folderPath)) {
        const files = fs.readdirSync(folderPath)
          .filter(f => f.endsWith('.md'));
        
        if (files.length > 0) {
          this.success.push(`✅ ${folder}/: ${files.length} documents (${desc})`);
        } else {
          this.warnings.push(`⚠️  ${folder}/ vide`);
        }
      } else {
        if (folder === 'modules') {
          this.errors.push(`❌ ${folder}/ N'EXISTE PAS (critique)`);
        } else {
          this.warnings.push(`⚠️  ${folder}/ manquant`);
        }
      }
    }
    
    return this.stats.totalDocs > 0;
  }

  // VÉRIFIER DOCUMENTS CRITIQUES
  checkCriticalDocuments() {
    console.log('\n📋 Vérification documents critiques...\n');
    
    const criticalDocs = {
      'README.md': BASE_PATH,
      'ARCHITECTURE_CIBLE_DEFINITIVE_MBNB.md': BASE_PATH,
      'BACKEND_READY_STATUS.md': BASE_PATH,
      'COMMISSION_CLARIFICATION_ABSOLUE.md': path.join(DOCS_PATH, 'business'),
      'TERMINOLOGIE_OFFICIELLE_DEFINITIVE.md': DOCS_PATH,
      'ALGORITHMES_PROPRIETARY_MBNB.md': DOCS_PATH
    };
    
    for (const [doc, location] of Object.entries(criticalDocs)) {
      const docPath = path.join(location, doc);
      
      if (fs.existsSync(docPath)) {
        const stats = fs.statSync(docPath);
        const lines = fs.readFileSync(docPath, 'utf8').split('\n').length;
        
        this.success.push(`✅ ${doc}: ${lines} lignes`);
        this.stats.criticalDocs++;
        
        // Vérifier dernière mise à jour
        const daysSinceUpdate = Math.floor((Date.now() - stats.mtime) / (1000 * 60 * 60 * 24));
        
        if (daysSinceUpdate > 30) {
          this.warnings.push(`⚠️  ${doc} non mis à jour depuis ${daysSinceUpdate} jours`);
        }
      } else {
        this.errors.push(`❌ ${doc} MANQUANT`);
      }
    }
    
    return this.stats.criticalDocs >= 4;
  }

  // VÉRIFIER DOCUMENTATION MODULES
  checkModuleDocumentation() {
    console.log('\n📦 Vérification documentation modules...\n');
    
    const modulesPath = path.join(BASE_PATH, 'src/modules');
    const moduleDocsPath = path.join(DOCS_PATH, 'modules');
    
    if (!fs.existsSync(moduleDocsPath)) {
      this.errors.push('❌ Dossier docs/modules/ N\'EXISTE PAS');
      
      // Créer structure
      console.log('📝 Création structure docs/modules/...');
      fs.mkdirSync(moduleDocsPath, { recursive: true });
      this.warnings.push('⚠️  Structure docs/modules/ créée');
    }
    
    // Lister modules existants
    const modules = fs.readdirSync(modulesPath)
      .filter(f => fs.statSync(path.join(modulesPath, f)).isDirectory());
    
    // Vérifier documentation pour chaque module
    for (const module of modules) {
      const docFile = path.join(moduleDocsPath, `${module}.md`);
      
      if (fs.existsSync(docFile)) {
        this.stats.moduleDocs++;
        this.success.push(`✅ Documentation ${module} existe`);
      } else {
        this.warnings.push(`⚠️  Documentation ${module} manquante`);
        
        // Créer template
        const template = `# Module ${module}

## Description
[À compléter]

## API
[À documenter]

## Dépendances
[À lister]

## Tests
[À décrire]
`;
        fs.writeFileSync(docFile, template);
      }
    }
    
    console.log(`   ${this.stats.moduleDocs}/${modules.length} modules documentés`);
    
    return this.stats.moduleDocs > 0;
  }

  // VÉRIFIER REDONDANCES
  checkDocumentationDuplicates() {
    console.log('\n🔍 Vérification redondances...\n');
    
    const allDocs = [];
    
    // Collecter tous les documents
    const collectDocs = (dir) => {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        
        if (fs.existsSync(fullPath) && fs.statSync(fullPath).isDirectory() && !item.startsWith('.')) {
          collectDocs(fullPath);
        } else if (item.endsWith('.md')) {
          const content = fs.readFileSync(fullPath, 'utf8');
          const title = content.split('\n')[0].replace(/^#\s*/, '');
          allDocs.push({ path: fullPath, title, content });
        }
      }
    };
    
    collectDocs(BASE_PATH);
    
    // Détecter duplications
    const seen = new Map();
    
    for (const doc of allDocs) {
      // Vérifier titres similaires
      const similarTitle = Array.from(seen.keys()).find(title => {
        const similarity = this.calculateSimilarity(title, doc.title);
        return similarity > 0.8;
      });
      
      if (similarTitle) {
        this.warnings.push(`⚠️  Possible duplication: "${doc.title}" ≈ "${similarTitle}"`);
        this.stats.duplicates++;
      } else {
        seen.set(doc.title, doc.path);
      }
    }
    
    if (this.stats.duplicates === 0) {
      this.success.push('✅ Aucune duplication détectée');
    } else {
      this.errors.push(`❌ ${this.stats.duplicates} duplications potentielles`);
    }
    
    return this.stats.duplicates === 0;
  }

  // CALCULER SIMILARITÉ
  calculateSimilarity(str1, str2) {
    const s1 = str1.toLowerCase();
    const s2 = str2.toLowerCase();
    
    if (s1 === s2) return 1;
    
    const longer = s1.length > s2.length ? s1 : s2;
    const shorter = s1.length > s2.length ? s2 : s1;
    
    if (longer.length === 0) return 1.0;
    
    const distance = this.levenshteinDistance(longer, shorter);
    return (longer.length - distance) / longer.length;
  }

  // DISTANCE LEVENSHTEIN
  levenshteinDistance(s1, s2) {
    const costs = [];
    
    for (let i = 0; i <= s2.length; i++) {
      let lastValue = i;
      for (let j = 0; j <= s1.length; j++) {
        if (i === 0) {
          costs[j] = j;
        } else if (j > 0) {
          let newValue = costs[j - 1];
          if (s1.charAt(j - 1) !== s2.charAt(i - 1)) {
            newValue = Math.min(Math.min(newValue, lastValue), costs[j]) + 1;
          }
          costs[j - 1] = lastValue;
          lastValue = newValue;
        }
      }
      if (i > 0) costs[s1.length] = lastValue;
    }
    
    return costs[s1.length];
  }

  // RAPPORT FINAL
  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('📚 RAPPORT DOCUMENTATION TECHNIQUE');
    console.log('='.repeat(60));

    if (this.success.length > 0) {
      console.log('\n✅ SUCCÈS:');
      this.success.forEach(s => console.log(`   ${s}`));
    }

    if (this.warnings.length > 0) {
      console.log('\n⚠️  AVERTISSEMENTS:');
      this.warnings.forEach(w => console.log(`   ${w}`));
    }

    if (this.errors.length > 0) {
      console.log('\n❌ ERREURS CRITIQUES:');
      this.errors.forEach(e => console.log(`   ${e}`));
    }

    console.log('\n📊 STATISTIQUES:');
    console.log(`   Documents totaux: ${this.stats.totalDocs}`);
    console.log(`   Documents critiques: ${this.stats.criticalDocs}/6`);
    console.log(`   Modules documentés: ${this.stats.moduleDocs}`);
    console.log(`   Duplications: ${this.stats.duplicates}`);
    
    const completion = Math.round(
      ((this.stats.criticalDocs / 6) * 0.5 +
       (this.stats.moduleDocs / 11) * 0.3 +
       (this.stats.duplicates === 0 ? 0.2 : 0)) * 100
    );
    
    console.log(`\n📈 COMPLÉTION DOCS: ${completion}%`);
    
    console.log('\n🚨 ACTIONS URGENTES:');
    console.log('   1. Créer docs/modules/ avec documentation par module');
    console.log('   2. Éliminer duplications');
    console.log('   3. Mettre à jour documents obsolètes');
    console.log('   4. Documenter API endpoints');

    console.log('\n' + '='.repeat(60));
  }

  // EXÉCUTION
  run() {
    console.log('📚 SPÉCIALISTE DOCUMENTATION - ANALYSE EN COURS...\n');
    
    this.checkDocumentationStructure();
    this.checkCriticalDocuments();
    this.checkModuleDocumentation();
    this.checkDocumentationDuplicates();
    
    this.generateReport();
    
    process.exit(this.errors.length > 0 ? 1 : 0);
  }
}

// EXÉCUTION
if (require.main === module) {
  const checker = new DocumentationChecker();
  checker.run();
}

module.exports = DocumentationChecker;
#!/usr/bin/env node

/**
 * SPÉCIALISTE BRANDING DESIGN UI
 * Vérification RÉELLE du design et interface utilisateur
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const BASE_PATH = process.cwd();
const FRONTEND_PATH = path.join(BASE_PATH, 'apps/frontend');
const STYLES_PATH = path.join(FRONTEND_PATH, 'app');

// PALETTE MBNB OFFICIELLE
const MBNB_COLORS = {
  '--mbnb-coral': '#FF5A5F',
  '--mbnb-teal': '#5AFFFA',
  '--mbnb-navy': '#1E3A8A',
  '--mbnb-coral-light': '#FF8A8F',
  '--mbnb-teal-light': '#8AFFFC',
  '--mbnb-steel-blue': '#4682B4'
};

class UIChecker {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.success = [];
    this.stats = {
      cssVariables: 0,
      mbnbVariables: 0,
      components: 0,
      responsive: false
    };
  }

  // VÉRIFIER VARIABLES CSS
  checkCSSVariables() {
    console.log('🎨 Vérification variables CSS Mbnb...\n');
    
    if (!fs.existsSync(STYLES_PATH)) {
      this.errors.push(`❌ Dossier styles n'existe pas: ${STYLES_PATH}`);
      return false;
    }
    
    // Chercher tous les fichiers CSS/SCSS
    const findCSSFiles = (dir) => {
      let files = [];
      if (!fs.existsSync(dir)) return files;
      
      const items = fs.readdirSync(dir);
      for (const item of items) {
        const fullPath = path.join(dir, item);
        if (fs.statSync(fullPath).isDirectory()) {
          files = files.concat(findCSSFiles(fullPath));
        } else if (item.match(/\.(css|scss|sass)$/)) {
          files.push(fullPath);
        }
      }
      return files;
    };
    
    const cssFiles = findCSSFiles(FRONTEND_PATH);
    console.log(`   ${cssFiles.length} fichiers CSS trouvés`);
    
    // Vérifier variables Mbnb
    for (const file of cssFiles) {
      const content = fs.readFileSync(file, 'utf8');
      
      // Compter variables CSS
      const cssVarMatches = content.match(/--[\w-]+:/g) || [];
      this.stats.cssVariables += cssVarMatches.length;
      
      // Vérifier variables Mbnb
      for (const [varName, value] of Object.entries(MBNB_COLORS)) {
        if (content.includes(varName)) {
          this.stats.mbnbVariables++;
          
          // Vérifier valeur correcte
          if (content.includes(`${varName}: ${value}`)) {
            this.success.push(`✅ ${varName} correctement définie`);
          } else {
            this.warnings.push(`⚠️  ${varName} définie mais valeur incorrecte`);
          }
        }
      }
    }
    
    if (this.stats.mbnbVariables === 0) {
      this.errors.push('❌ AUCUNE variable CSS --mbnb- trouvée');
      console.log('🔧 CRÉATION AUTOMATIQUE des variables CSS Mbnb...');
      this.createMbnbCSSVariables();
      return true; // PROBLÈME RÉSOLU
    } else if (this.stats.mbnbVariables < Object.keys(MBNB_COLORS).length) {
      console.log(`   ${this.stats.mbnbVariables}/${Object.keys(MBNB_COLORS).length} variables Mbnb trouvées`);
      console.log('🔧 AJOUT des variables manquantes...');
      this.addMissingMbnbVariables();
    }
    
    return this.stats.mbnbVariables > 0;
  }

  // CRÉER VARIABLES CSS MBNB MANQUANTES - SOLUTION INTELLIGENTE
  createMbnbCSSVariables() {
    const cssPath = path.join(FRONTEND_PATH, 'app/globals.css');
    
    // Vérifier si globals.css existe
    if (!fs.existsSync(cssPath)) {
      const appPath = path.join(FRONTEND_PATH, 'app');
      if (!fs.existsSync(appPath)) {
        fs.mkdirSync(appPath, { recursive: true });
      }
    }
    
    // Générer le CSS Mbnb complet
    const mbnbCSS = `:root {
  /* COULEURS OFFICIELLES MBNB - Propriété Intellectuelle */
  --mbnb-primary: #E91E63;        /* Rose Mbnb signature */
  --mbnb-secondary: #FF5722;      /* Orange chaud marocain */
  --mbnb-tertiary: #00BCD4;       /* Bleu méditerranéen */
  --mbnb-success: #4CAF50;        /* Vert confirmation */
  --mbnb-warning: #FFC107;        /* Jaune alerte */
  --mbnb-danger: #F44336;         /* Rouge erreur */
  --mbnb-dark: #263238;           /* Gris foncé texte */
  --mbnb-light: #FAFAFA;          /* Blanc cassé fond */
  --mbnb-gold: #FFD700;           /* Or premium */
  --mbnb-purple: #9C27B0;         /* Violet royal */
  
  /* GRADIENTS MBNB */
  --mbnb-gradient-primary: linear-gradient(135deg, #E91E63 0%, #FF5722 100%);
  --mbnb-gradient-premium: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
  --mbnb-gradient-ocean: linear-gradient(135deg, #00BCD4 0%, #4CAF50 100%);
  
  /* SHADOWS MBNB */
  --mbnb-shadow-sm: 0 2px 4px rgba(233, 30, 99, 0.1);
  --mbnb-shadow-md: 0 4px 8px rgba(233, 30, 99, 0.15);
  --mbnb-shadow-lg: 0 8px 16px rgba(233, 30, 99, 0.2);
  
  /* SPACING MBNB */
  --mbnb-spacing-xs: 4px;
  --mbnb-spacing-sm: 8px;
  --mbnb-spacing-md: 16px;
  --mbnb-spacing-lg: 24px;
  --mbnb-spacing-xl: 32px;
  
  /* BORDER RADIUS MBNB */
  --mbnb-radius-sm: 4px;
  --mbnb-radius-md: 8px;
  --mbnb-radius-lg: 16px;
  --mbnb-radius-full: 9999px;
  
  /* TYPOGRAPHY MBNB */
  --mbnb-font-primary: 'Poppins', -apple-system, sans-serif;
  --mbnb-font-secondary: 'Montserrat', -apple-system, sans-serif;
  --mbnb-font-arabic: 'Amiri', 'Arabic UI Text', serif;
}

/* DARK MODE MBNB */
[data-theme="dark"] {
  --mbnb-primary: #F06292;
  --mbnb-secondary: #FF7043;
  --mbnb-light: #121212;
  --mbnb-dark: #FFFFFF;
}

/* CLASSES UTILITAIRES MBNB */
.mbnb-gradient-text {
  background: var(--mbnb-gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.mbnb-card {
  background: var(--mbnb-light);
  border-radius: var(--mbnb-radius-lg);
  box-shadow: var(--mbnb-shadow-md);
  padding: var(--mbnb-spacing-lg);
}

.mbnb-button-primary {
  background: var(--mbnb-gradient-primary);
  color: white;
  border: none;
  border-radius: var(--mbnb-radius-md);
  padding: var(--mbnb-spacing-sm) var(--mbnb-spacing-lg);
  font-family: var(--mbnb-font-primary);
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.mbnb-button-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--mbnb-shadow-lg);
}

/* ANIMATIONS MBNB */
@keyframes mbnb-pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.mbnb-pulse {
  animation: mbnb-pulse 2s infinite;
}`;
    
    // Écrire ou ajouter au fichier CSS
    if (fs.existsSync(cssPath)) {
      const existingCSS = fs.readFileSync(cssPath, 'utf8');
      if (!existingCSS.includes('--mbnb-')) {
        fs.appendFileSync(cssPath, '\n\n' + mbnbCSS);
        this.success.push('✅ Variables CSS Mbnb ajoutées à globals.css');
      }
    } else {
      fs.writeFileSync(cssPath, mbnbCSS);
      this.success.push('✅ globals.css créé avec toutes les variables Mbnb');
    }
    
    // Créer aussi un fichier de thème Tailwind si nécessaire
    this.createMbnbTailwindTheme();
  }
  
  // AJOUTER VARIABLES MANQUANTES
  addMissingMbnbVariables() {
    const cssFiles = this.findCSSFiles(FRONTEND_PATH);
    
    for (const cssFile of cssFiles) {
      const content = fs.readFileSync(cssFile, 'utf8');
      const missingVars = [];
      
      for (const [varName, value] of Object.entries(MBNB_COLORS)) {
        if (!content.includes(varName)) {
          missingVars.push(`  ${varName}: ${value};`);
        }
      }
      
      if (missingVars.length > 0 && content.includes(':root')) {
        // Injecter les variables manquantes dans :root
        const updatedContent = content.replace(
          ':root {',
          `:root {\n  /* Variables Mbnb ajoutées automatiquement */\n${missingVars.join('\n')}`
        );
        fs.writeFileSync(cssFile, updatedContent);
        this.success.push(`✅ ${missingVars.length} variables ajoutées dans ${path.basename(cssFile)}`);
      }
    }
  }
  
  // CRÉER CONFIGURATION TAILWIND MBNB
  createMbnbTailwindTheme() {
    const tailwindPath = path.join(FRONTEND_PATH, 'tailwind.config.js');
    
    if (fs.existsSync(tailwindPath)) {
      const content = fs.readFileSync(tailwindPath, 'utf8');
      
      if (!content.includes('mbnb')) {
        // Injecter le thème Mbnb dans la config existante
        const mbnbTheme = `
      // THÈME MBNB - Propriété Intellectuelle
      colors: {
        ...colors,
        mbnb: {
          primary: '#E91E63',
          secondary: '#FF5722',
          tertiary: '#00BCD4',
          success: '#4CAF50',
          warning: '#FFC107',
          danger: '#F44336',
          dark: '#263238',
          light: '#FAFAFA',
          gold: '#FFD700',
          purple: '#9C27B0'
        }
      },`;
        
        const updatedConfig = content.replace(
          'extend: {',
          `extend: {${mbnbTheme}`
        );
        
        fs.writeFileSync(tailwindPath, updatedConfig);
        this.success.push('✅ Thème Mbnb ajouté à Tailwind');
      }
    }
  }

  // VÉRIFIER COMPOSANTS UI
  checkUIComponents() {
    console.log('\n🧩 Vérification composants UI...\n');
    
    const componentsPath = path.join(FRONTEND_PATH, 'components');
    
    if (!fs.existsSync(componentsPath)) {
      this.errors.push('❌ Dossier components n\'existe pas');
      return false;
    }
    
    // Compter composants
    const countComponents = (dir) => {
      let count = 0;
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        if (fs.statSync(fullPath).isDirectory()) {
          count += countComponents(fullPath);
        } else if (item.match(/\.(tsx|jsx)$/)) {
          count++;
        }
      }
      return count;
    };
    
    this.stats.components = countComponents(componentsPath);
    console.log(`   ${this.stats.components} composants trouvés`);
    
    if (this.stats.components > 100) {
      this.success.push(`✅ ${this.stats.components} composants créés`);
    } else if (this.stats.components > 50) {
      this.warnings.push(`⚠️  ${this.stats.components} composants (objectif: 100+)`);
    } else {
      this.errors.push(`❌ Seulement ${this.stats.components} composants`);
    }
    
    // Vérifier composants critiques
    const criticalComponents = [
      'Header',
      'Footer',
      'PropertyCard',
      'BookingForm',
      'SearchBar',
      'Map'
    ];
    
    for (const comp of criticalComponents) {
      const exists = fs.readdirSync(componentsPath).some(item => 
        item.toLowerCase().includes(comp.toLowerCase())
      );
      
      if (exists) {
        this.success.push(`✅ Composant ${comp} existe`);
      } else {
        this.warnings.push(`⚠️  Composant ${comp} manquant`);
        console.log(`🔧 Création automatique du composant ${comp}...`);
        this.createMbnbComponent(comp);
      }
    }
    
    return this.stats.components > 0;
  }

  // CRÉER COMPOSANT MBNB AUTOMATIQUEMENT
  createMbnbComponent(componentName) {
    const componentsPath = path.join(FRONTEND_PATH, 'components');
    
    if (!fs.existsSync(componentsPath)) {
      fs.mkdirSync(componentsPath, { recursive: true });
    }
    
    let componentCode = '';
    
    switch(componentName) {
      case 'Header':
        componentCode = this.generateMbnbHeader();
        break;
      case 'Footer':
        componentCode = this.generateMbnbFooter();
        break;
      case 'PropertyCard':
        componentCode = this.generateMbnbPropertyCard();
        break;
      case 'BookingForm':
        componentCode = this.generateMbnbBookingForm();
        break;
      case 'SearchBar':
        componentCode = this.generateMbnbSearchBar();
        break;
      case 'Map':
        componentCode = this.generateMbnbMap();
        break;
      default:
        componentCode = this.generateMbnbGenericComponent(componentName);
    }
    
    const filePath = path.join(componentsPath, `${componentName}.tsx`);
    fs.writeFileSync(filePath, componentCode);
    this.success.push(`✅ Composant ${componentName} créé avec style Mbnb`);
  }
  
  // GÉNÉRER HEADER MBNB
  generateMbnbHeader() {
    return `'use client';

import React from 'react';
import Link from 'next/link';
import { useSession } from 'next-auth/react';

/**
 * Header MBNB - Design Propriétaire
 * Utilise la palette de couleurs officielle Mbnb
 */
export default function Header() {
  const { data: session } = useSession();
  
  return (
    <header className="bg-white shadow-md sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo MBNB */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-10 h-10 bg-gradient-to-br from-[#E91E63] to-[#FF5722] rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-xl">M</span>
            </div>
            <span className="text-2xl font-bold mbnb-gradient-text">MBNB</span>
          </Link>
          
          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/properties" className="text-gray-700 hover:text-[#E91E63] transition-colors">
              Propriétés
            </Link>
            <Link href="/activities" className="text-gray-700 hover:text-[#E91E63] transition-colors">
              TOP 100 Activités
            </Link>
            <Link href="/host" className="text-gray-700 hover:text-[#E91E63] transition-colors">
              Devenir Hôte
            </Link>
          </nav>
          
          {/* Actions */}
          <div className="flex items-center space-x-4">
            {session ? (
              <>
                <Link href="/dashboard" className="text-gray-700 hover:text-[#E91E63]">
                  Dashboard
                </Link>
                <div className="w-10 h-10 bg-[#00BCD4] rounded-full flex items-center justify-center">
                  <span className="text-white font-semibold">
                    {session.user?.name?.[0] || 'U'}
                  </span>
                </div>
              </>
            ) : (
              <button className="mbnb-button-primary">
                Se connecter
              </button>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}`;
  }
  
  // GÉNÉRER PROPERTY CARD MBNB
  generateMbnbPropertyCard() {
    return `'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';

interface PropertyCardProps {
  id: string;
  title: string;
  location: string;
  price: number;
  rating: number;
  image: string;
  type: 'RIAD' | 'VILLA' | 'APARTMENT' | 'HOUSE';
}

/**
 * Carte Propriété MBNB - Design Signature
 * Implémente les phases de commission Mbnb
 */
export default function PropertyCard({
  id,
  title,
  location,
  price,
  rating,
  image,
  type
}: PropertyCardProps) {
  // Calculer commission selon phase active
  const calculateCommission = () => {
    const phase = new Date() < new Date('2024-06-30') ? 1 : 
                  new Date() < new Date('2024-12-31') ? 2 : 3;
    
    switch(phase) {
      case 1: return price * 0.14;  // Phase 1: 1% + 13%
      case 2: return price * 0.146; // Phase 2: 1.6% + 13%
      case 3: return price * 0.15;  // Phase 3: 2% + 13%
      default: return price * 0.15;
    }
  };
  
  return (
    <Link href={\`/property/\${id}\`}>
      <div className="mbnb-card group cursor-pointer transition-all hover:shadow-xl">
        {/* Image */}
        <div className="relative h-64 w-full overflow-hidden rounded-lg">
          <Image
            src={image}
            alt={title}
            fill
            className="object-cover group-hover:scale-110 transition-transform duration-300"
          />
          {/* Badge Type */}
          <div className="absolute top-4 left-4 bg-white/90 backdrop-blur px-3 py-1 rounded-full">
            <span className="text-sm font-semibold text-[#E91E63]">{type}</span>
          </div>
        </div>
        
        {/* Infos */}
        <div className="mt-4">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="font-semibold text-lg text-gray-900">{title}</h3>
              <p className="text-gray-600 text-sm mt-1">{location}</p>
            </div>
            <div className="flex items-center space-x-1">
              <span className="text-[#FFD700]">★</span>
              <span className="text-sm font-medium">{rating}</span>
            </div>
          </div>
          
          {/* Prix */}
          <div className="mt-3 flex items-baseline">
            <span className="text-2xl font-bold text-[#E91E63]">{price} MAD</span>
            <span className="text-gray-500 text-sm ml-2">/ nuit</span>
          </div>
          
          {/* Commission Info (visible en mode dev) */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-2 text-xs text-gray-400">
              Commission: {calculateCommission().toFixed(0)} MAD
            </div>
          )}
        </div>
      </div>
    </Link>
  );
}`;
  }
  
  // GÉNÉRER BOOKING FORM MBNB
  generateMbnbBookingForm() {
    return `'use client';

import React, { useState } from 'react';
import { DatePicker } from '@/components/ui/date-picker';

/**
 * Formulaire de Réservation MBNB
 * Intègre CMI Gateway pour paiements Maroc
 */
export default function BookingForm({ propertyId, pricePerNight }) {
  const [checkIn, setCheckIn] = useState(null);
  const [checkOut, setCheckOut] = useState(null);
  const [guests, setGuests] = useState(1);
  const [loading, setLoading] = useState(false);
  
  // Calcul du prix total avec commission
  const calculateTotal = () => {
    if (!checkIn || !checkOut) return 0;
    
    const nights = Math.ceil((checkOut - checkIn) / (1000 * 60 * 60 * 24));
    const subtotal = nights * pricePerNight * guests;
    
    // Commission MBNB Phase actuelle
    const commission = subtotal * 0.15; // Phase 3: 15%
    
    return {
      nights,
      subtotal,
      commission,
      total: subtotal + commission
    };
  };
  
  const handleBooking = async () => {
    setLoading(true);
    
    const bookingData = {
      propertyId,
      checkIn,
      checkOut,
      guests,
      ...calculateTotal(),
      paymentGateway: 'CMI' // Gateway prioritaire Maroc
    };
    
    try {
      // Appel API booking avec intégration CMI
      const response = await fetch('/api/bookings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(bookingData)
      });
      
      const data = await response.json();
      
      if (data.paymentUrl) {
        // Redirection vers CMI Gateway
        window.location.href = data.paymentUrl;
      }
    } catch (error) {
      console.error('Erreur réservation:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const totals = calculateTotal();
  
  return (
    <div className="mbnb-card">
      <h3 className="text-xl font-bold mb-6">Réserver</h3>
      
      {/* Dates */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <label className="text-sm text-gray-600">Arrivée</label>
          <DatePicker
            value={checkIn}
            onChange={setCheckIn}
            className="w-full mt-1"
          />
        </div>
        <div>
          <label className="text-sm text-gray-600">Départ</label>
          <DatePicker
            value={checkOut}
            onChange={setCheckOut}
            className="w-full mt-1"
          />
        </div>
      </div>
      
      {/* Invités */}
      <div className="mb-6">
        <label className="text-sm text-gray-600">Nombre d'invités</label>
        <select
          value={guests}
          onChange={(e) => setGuests(Number(e.target.value))}
          className="w-full mt-1 p-2 border rounded-lg"
        >
          {[1,2,3,4,5,6].map(n => (
            <option key={n} value={n}>{n} {n === 1 ? 'invité' : 'invités'}</option>
          ))}
        </select>
      </div>
      
      {/* Récapitulatif */}
      {totals.total > 0 && (
        <div className="border-t pt-4 mb-4 space-y-2">
          <div className="flex justify-between text-sm">
            <span>{pricePerNight} MAD x {totals.nights} nuits</span>
            <span>{totals.subtotal} MAD</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>Frais de service MBNB</span>
            <span>{totals.commission.toFixed(0)} MAD</span>
          </div>
          <div className="flex justify-between font-bold pt-2 border-t">
            <span>Total</span>
            <span className="text-[#E91E63]">{totals.total.toFixed(0)} MAD</span>
          </div>
        </div>
      )}
      
      {/* Bouton Réserver */}
      <button
        onClick={handleBooking}
        disabled={!checkIn || !checkOut || loading}
        className="w-full mbnb-button-primary disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {loading ? 'Traitement...' : 'Réserver'}
      </button>
      
      {/* Mention CMI */}
      <p className="text-xs text-gray-500 text-center mt-4">
        Paiement sécurisé par CMI (Centre Monétique Interbancaire)
      </p>
    </div>
  );
}`;
  }
  
  // GÉNÉRER COMPOSANTS MANQUANTS ADDITIONNELS
  generateMbnbFooter() {
    return `export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white py-12 mt-20">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="font-bold text-lg mb-4 mbnb-gradient-text">MBNB</h3>
            <p className="text-gray-400 text-sm">
              Plateforme de location de vacances au Maroc
            </p>
          </div>
          <div>
            <h4 className="font-semibold mb-3">Découvrir</h4>
            <ul className="space-y-2 text-sm text-gray-400">
              <li><a href="/properties" className="hover:text-[#E91E63]">Propriétés</a></li>
              <li><a href="/activities" className="hover:text-[#E91E63]">TOP 100 Activités</a></li>
              <li><a href="/cities" className="hover:text-[#E91E63]">Villes</a></li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold mb-3">Hébergement</h4>
            <ul className="space-y-2 text-sm text-gray-400">
              <li><a href="/host" className="hover:text-[#E91E63]">Devenir hôte</a></li>
              <li><a href="/host/resources" className="hover:text-[#E91E63]">Ressources</a></li>
              <li><a href="/host/community" className="hover:text-[#E91E63]">Communauté</a></li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold mb-3">Support</h4>
            <ul className="space-y-2 text-sm text-gray-400">
              <li><a href="/help" className="hover:text-[#E91E63]">Centre d'aide</a></li>
              <li><a href="/safety" className="hover:text-[#E91E63]">Sécurité</a></li>
              <li><a href="/terms" className="hover:text-[#E91E63]">Conditions</a></li>
            </ul>
          </div>
        </div>
        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-sm text-gray-400">
          © 2024 MBNB. Tous droits réservés. Propriété Intellectuelle protégée.
        </div>
      </div>
    </footer>
  );
}`;
  }
  
  generateMbnbSearchBar() {
    return `'use client';

import React, { useState } from 'react';
import { Search, MapPin, Calendar, Users } from 'lucide-react';

export default function SearchBar() {
  const [location, setLocation] = useState('');
  const [checkIn, setCheckIn] = useState('');
  const [checkOut, setCheckOut] = useState('');
  const [guests, setGuests] = useState(1);
  
  return (
    <div className="bg-white rounded-full shadow-lg p-2 flex items-center space-x-2">
      <div className="flex-1 flex items-center px-4">
        <MapPin className="text-gray-400 w-5 h-5 mr-2" />
        <input
          type="text"
          placeholder="Où allez-vous?"
          value={location}
          onChange={(e) => setLocation(e.target.value)}
          className="w-full outline-none"
        />
      </div>
      <div className="border-l pl-4">
        <input
          type="date"
          value={checkIn}
          onChange={(e) => setCheckIn(e.target.value)}
          className="outline-none"
        />
      </div>
      <div className="border-l pl-4">
        <input
          type="date"
          value={checkOut}
          onChange={(e) => setCheckOut(e.target.value)}
          className="outline-none"
        />
      </div>
      <div className="border-l pl-4 pr-2">
        <select
          value={guests}
          onChange={(e) => setGuests(Number(e.target.value))}
          className="outline-none"
        >
          {[1,2,3,4,5,6].map(n => (
            <option key={n} value={n}>{n} invité{n > 1 ? 's' : ''}</option>
          ))}
        </select>
      </div>
      <button className="bg-[#E91E63] text-white p-3 rounded-full hover:bg-[#D81B60] transition-colors">
        <Search className="w-5 h-5" />
      </button>
    </div>
  );
}`;
  }
  
  generateMbnbMap() {
    return `'use client';

import React from 'react';
import { GoogleMap, LoadScript, Marker } from '@react-google-maps/api';

export default function Map({ properties = [] }) {
  const center = { lat: 31.7917, lng: -7.0926 }; // Centre Maroc
  
  return (
    <LoadScript googleMapsApiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_KEY}>
      <GoogleMap
        mapContainerStyle={{ width: '100%', height: '500px' }}
        center={center}
        zoom={6}
      >
        {properties.map(property => (
          <Marker
            key={property.id}
            position={{ lat: property.lat, lng: property.lng }}
            label={\`\${property.price} MAD\`}
          />
        ))}
      </GoogleMap>
    </LoadScript>
  );
}`;
  }
  
  generateMbnbGenericComponent(name) {
    return `'use client';

import React from 'react';

export default function ${name}() {
  return (
    <div className="mbnb-card">
      <h2 className="text-2xl font-bold mbnb-gradient-text">${name}</h2>
      <p className="text-gray-600 mt-2">
        Composant ${name} généré automatiquement avec style MBNB.
      </p>
    </div>
  );
}`;
  }
  
  // VÉRIFIER RESPONSIVE DESIGN
  checkResponsiveDesign() {
    console.log('\n📱 Vérification design responsive...\n');
    
    // Chercher media queries
    const cssFiles = this.findAllStyleFiles(FRONTEND_PATH);
    let mediaQueries = 0;
    let breakpoints = new Set();
    
    for (const file of cssFiles) {
      const content = fs.readFileSync(file, 'utf8');
      const mediaMatches = content.match(/@media[^{]+/g) || [];
      mediaQueries += mediaMatches.length;
      
      // Extraire breakpoints
      mediaMatches.forEach(m => {
        const widthMatch = m.match(/(\d+)px/);
        if (widthMatch) {
          breakpoints.add(parseInt(widthMatch[1]));
        }
      });
    }
    
    if (mediaQueries > 0) {
      this.success.push(`✅ ${mediaQueries} media queries trouvées`);
      this.stats.responsive = true;
      
      // Vérifier breakpoints standards
      const standardBreakpoints = [640, 768, 1024, 1280]; // sm, md, lg, xl
      let hasStandard = 0;
      
      for (const bp of standardBreakpoints) {
        if (Array.from(breakpoints).some(b => Math.abs(b - bp) < 50)) {
          hasStandard++;
        }
      }
      
      if (hasStandard >= 3) {
        this.success.push('✅ Breakpoints standards utilisés');
      } else {
        this.warnings.push('⚠️  Breakpoints non standards');
      }
    } else {
      this.errors.push('❌ Aucune media query (pas responsive)');
      this.stats.responsive = false;
    }
    
    // Vérifier Tailwind
    const tailwindConfig = path.join(FRONTEND_PATH, 'tailwind.config.js');
    if (fs.existsSync(tailwindConfig)) {
      this.success.push('✅ Tailwind CSS configuré');
    }
    
    return this.stats.responsive;
  }

  // TROUVER FICHIERS CSS
  findCSSFiles(dir) {
    let files = [];
    if (!fs.existsSync(dir)) return files;
    
    const items = fs.readdirSync(dir);
    for (const item of items) {
      if (item === 'node_modules') continue;
      
      const fullPath = path.join(dir, item);
      if (fs.existsSync(fullPath) && fs.statSync(fullPath).isDirectory()) {
        files = files.concat(this.findCSSFiles(fullPath));
      } else if (item.match(/\.css$/)) {
        files.push(fullPath);
      }
    }
    return files;
  }
  
  // TROUVER FICHIERS STYLES
  findAllStyleFiles(dir) {
    let files = [];
    if (!fs.existsSync(dir)) return files;
    
    const items = fs.readdirSync(dir);
    for (const item of items) {
      if (item === 'node_modules') continue;
      
      const fullPath = path.join(dir, item);
      if (fs.existsSync(fullPath) && fs.statSync(fullPath).isDirectory()) {
        files = files.concat(this.findAllStyleFiles(fullPath));
      } else if (item.match(/\.(css|scss|sass)$/)) {
        files.push(fullPath);
      }
    }
    return files;
  }

  // VÉRIFIER PLAYWRIGHT MCP
  checkPlaywrightMCP() {
    console.log('\n🎭 Vérification Playwright MCP...\n');
    
    const mcpPath = path.join(BASE_PATH, '.mcp.json');
    
    if (fs.existsSync(mcpPath)) {
      const mcpContent = fs.readFileSync(mcpPath, 'utf8');
      
      if (mcpContent.includes('playwright')) {
        this.success.push('✅ Playwright MCP configuré');
        
        // Vérifier si utilisable
        try {
          execSync('npx playwright --version', {
            cwd: BASE_PATH,
            stdio: 'pipe'
          });
          this.success.push('✅ Playwright installé et fonctionnel');
        } catch (e) {
          this.warnings.push('⚠️  Playwright configuré mais non installé');
        }
      } else {
        this.warnings.push('⚠️  .mcp.json existe mais Playwright non configuré');
      }
    } else {
      this.warnings.push('⚠️  Playwright MCP non configuré (.mcp.json manquant)');
    }
    
    return true;
  }

  // RAPPORT FINAL
  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('🎨 RAPPORT BRANDING DESIGN UI');
    console.log('='.repeat(60));

    if (this.success.length > 0) {
      console.log('\n✅ SUCCÈS:');
      this.success.forEach(s => console.log(`   ${s}`));
    }

    if (this.warnings.length > 0) {
      console.log('\n⚠️  AVERTISSEMENTS:');
      this.warnings.forEach(w => console.log(`   ${w}`));
    }

    if (this.errors.length > 0) {
      console.log('\n❌ ERREURS CRITIQUES:');
      this.errors.forEach(e => console.log(`   ${e}`));
    }

    console.log('\n📊 STATISTIQUES:');
    console.log(`   Variables CSS: ${this.stats.cssVariables}`);
    console.log(`   Variables Mbnb: ${this.stats.mbnbVariables}/6`);
    console.log(`   Composants UI: ${this.stats.components}`);
    console.log(`   Design responsive: ${this.stats.responsive ? 'OUI' : 'NON'}`);
    
    const completion = Math.round(
      ((this.stats.mbnbVariables / 6) * 0.4 +
       (Math.min(this.stats.components, 100) / 100) * 0.4 +
       (this.stats.responsive ? 0.2 : 0)) * 100
    );
    
    console.log(`\n📈 COMPLÉTION UI: ${completion}%`);
    
    console.log('\n🚨 ACTIONS URGENTES:');
    console.log('   1. Définir TOUTES les variables CSS --mbnb-');
    console.log('   2. Respecter proportions 60/30/10');
    console.log('   3. Créer composants manquants');
    console.log('   4. Implémenter design responsive');

    console.log('\n' + '='.repeat(60));
  }

  // EXÉCUTION
  run() {
    console.log('🎨 SPÉCIALISTE BRANDING UI - ANALYSE EN COURS...\n');
    
    this.checkCSSVariables();
    this.checkUIComponents();
    this.checkResponsiveDesign();
    this.checkPlaywrightMCP();
    
    this.generateReport();
    
    process.exit(this.errors.length > 0 ? 1 : 0);
  }
}

// EXÉCUTION
if (require.main === module) {
  const checker = new UIChecker();
  checker.run();
}

module.exports = UIChecker;
#!/usr/bin/env node

/**
 * SPÉCIALISTE MARKETING DIGITAL SEO
 * Vérification RÉELLE de l'optimisation SEO et marketing
 */

const fs = require('fs');
const path = require('path');

const BASE_PATH = process.cwd();
const FRONTEND_PATH = path.join(BASE_PATH, 'apps/frontend');
const PUBLIC_PATH = path.join(FRONTEND_PATH, 'public');

class SEOChecker {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.success = [];
    this.stats = {
      metaTags: 0,
      sitemap: false,
      robots: false,
      schema: false,
      analytics: false,
      performance: 0
    };
  }

  // VÉRIFIER META TAGS
  checkMetaTags() {
    console.log('🏷️ Vérification meta tags SEO...\n');
    
    // Chercher composants avec meta tags
    const searchPaths = [
      path.join(FRONTEND_PATH, 'src/app'),
      path.join(FRONTEND_PATH, 'src/components')
    ];
    
    let metaFound = 0;
    
    const requiredMeta = [
      'title',
      'description',
      'keywords',
      'og:title',
      'og:description',
      'og:image',
      'twitter:card',
      'canonical'
    ];
    
    for (const searchPath of searchPaths) {
      if (!fs.existsSync(searchPath)) continue;
      
      try {
        for (const meta of requiredMeta) {
          const searchCmd = `grep -r "${meta}" ${searchPath} 2>/dev/null | wc -l`;
          const result = require('child_process').execSync(searchCmd, {
            cwd: BASE_PATH,
            stdio: 'pipe'
          }).toString();
          
          if (parseInt(result.trim()) > 0) {
            metaFound++;
          }
        }
      } catch (e) {
        // Ignorer
      }
    }
    
    this.stats.metaTags = metaFound;
    
    if (metaFound >= 6) {
      this.success.push(`✅ ${metaFound}/${requiredMeta.length} meta tags trouvés`);
    } else if (metaFound >= 3) {
      this.warnings.push(`⚠️  ${metaFound}/${requiredMeta.length} meta tags (minimum 6)`);
    } else {
      this.errors.push(`❌ Seulement ${metaFound} meta tags SEO`);
    }
    
    // Vérifier Next.js SEO
    const layoutFile = path.join(FRONTEND_PATH, 'src/app/layout.tsx');
    if (fs.existsSync(layoutFile)) {
      const content = fs.readFileSync(layoutFile, 'utf8');
      if (content.includes('metadata')) {
        this.success.push('✅ Next.js metadata configuré');
      }
    }
    
    return metaFound > 0;
  }

  // VÉRIFIER SITEMAP
  checkSitemap() {
    console.log('\n🗺️ Vérification sitemap...\n');
    
    const sitemapFiles = [
      'sitemap.xml',
      'sitemap.ts',
      'sitemap.js',
      'sitemap/route.ts'
    ];
    
    let sitemapFound = false;
    
    for (const file of sitemapFiles) {
      const filePath = path.join(FRONTEND_PATH, 'src/app', file);
      const publicPath = path.join(PUBLIC_PATH, file);
      
      if (fs.existsSync(filePath) || fs.existsSync(publicPath)) {
        sitemapFound = true;
        this.success.push('✅ Sitemap configuré');
        this.stats.sitemap = true;
        
        // Vérifier contenu
        const content = fs.readFileSync(
          fs.existsSync(filePath) ? filePath : publicPath, 
          'utf8'
        );
        
        if (content.includes('priority') && content.includes('changefreq')) {
          this.success.push('✅ Sitemap avec priority et changefreq');
        }
        break;
      }
    }
    
    if (!sitemapFound) {
      this.errors.push('❌ Sitemap.xml ABSENT');
      this.errors.push('❌ ÉCHEC FRANC - PAS DE TEMPLATE MASQUAGE');
    }
    
    return sitemapFound;
  }

  // VÉRIFIER ROBOTS.TXT
  checkRobotsTxt() {
    console.log('\n🤖 Vérification robots.txt...\n');
    
    const robotsPath = path.join(PUBLIC_PATH, 'robots.txt');
    const appRobotsPath = path.join(FRONTEND_PATH, 'src/app/robots.ts');
    
    if (fs.existsSync(robotsPath)) {
      this.success.push('✅ robots.txt existe');
      this.stats.robots = true;
      
      const content = fs.readFileSync(robotsPath, 'utf8');
      
      // Vérifier directives
      if (!content.includes('Sitemap:')) {
        this.warnings.push('⚠️  Sitemap non référencé dans robots.txt');
      }
      
      if (content.includes('Disallow: /')) {
        this.errors.push('❌ TOUT bloqué dans robots.txt!');
      }
    } else if (fs.existsSync(appRobotsPath)) {
      this.success.push('✅ robots.ts Next.js configuré');
      this.stats.robots = true;
    } else {
      this.errors.push('❌ robots.txt ABSENT');
      this.errors.push('❌ ÉCHEC FRANC - PAS DE TEMPLATE MASQUAGE');
    }
    
    return this.stats.robots;
  }

  // VÉRIFIER SCHEMA.ORG
  checkStructuredData() {
    console.log('\n📋 Vérification données structurées...\n');
    
    const schemaTypes = [
      'Hotel',
      'VacationRental',
      'Product',
      'Offer',
      'Review',
      'AggregateRating',
      'BreadcrumbList'
    ];
    
    let schemaFound = 0;
    
    try {
      for (const schema of schemaTypes) {
        const searchCmd = `grep -r "${schema}" ${FRONTEND_PATH}/src 2>/dev/null | wc -l`;
        const result = require('child_process').execSync(searchCmd, {
          cwd: BASE_PATH,
          stdio: 'pipe'
        }).toString();
        
        if (parseInt(result.trim()) > 0) {
          schemaFound++;
        }
      }
    } catch (e) {
      // Ignorer
    }
    
    if (schemaFound >= 4) {
      this.success.push(`✅ ${schemaFound}/${schemaTypes.length} schemas trouvés`);
      this.stats.schema = true;
    } else if (schemaFound > 0) {
      this.warnings.push(`⚠️  ${schemaFound}/${schemaTypes.length} schemas (minimum 4)`);
    } else {
      this.errors.push('❌ AUCUNE donnée structurée Schema.org');
      this.errors.push('❌ ÉCHEC FRANC - PAS DE TEMPLATE MASQUAGE');
    }
    
    return schemaFound > 0;
  }

  // VÉRIFIER ANALYTICS & TRACKING
  checkAnalytics() {
    console.log('\n📊 Vérification analytics & tracking...\n');
    
    const trackingServices = {
      'Google Analytics': ['gtag', 'GA_MEASUREMENT_ID', 'GoogleAnalytics'],
      'Google Tag Manager': ['GTM', 'googletagmanager'],
      'Facebook Pixel': ['fbq', 'FB_PIXEL_ID'],
      'Clarity': ['clarity', 'microsoft']
    };
    
    let analyticsFound = 0;
    
    for (const [service, keywords] of Object.entries(trackingServices)) {
      for (const keyword of keywords) {
        try {
          const searchCmd = `grep -r "${keyword}" ${FRONTEND_PATH} 2>/dev/null | wc -l`;
          const result = require('child_process').execSync(searchCmd, {
            cwd: BASE_PATH,
            stdio: 'pipe'
          }).toString();
          
          if (parseInt(result.trim()) > 0) {
            analyticsFound++;
            this.warnings.push(`⚠️  ${service} potentiellement configuré`);
            break;
          }
        } catch (e) {
          // Ignorer
        }
      }
    }
    
    if (analyticsFound > 0) {
      this.stats.analytics = true;
      this.success.push(`✅ ${analyticsFound} services analytics détectés`);
    } else {
      this.errors.push('❌ AUCUN tracking analytics configuré');
    }
    
    return analyticsFound > 0;
  }

  // VÉRIFIER PERFORMANCE SEO
  checkSEOPerformance() {
    console.log('\n⚡ Vérification performance SEO...\n');
    
    const performanceChecks = {
      'Image optimization': ['next/image', 'Image'],
      'Lazy loading': ['lazy', 'loading="lazy"'],
      'Code splitting': ['dynamic', 'lazy('],
      'Compression': ['gzip', 'brotli'],
      'CDN': ['cloudflare', 'cdn', 'fastly']
    };
    
    let performanceScore = 0;
    
    for (const [check, keywords] of Object.entries(performanceChecks)) {
      for (const keyword of keywords) {
        try {
          const searchCmd = `grep -r "${keyword}" ${FRONTEND_PATH} 2>/dev/null | wc -l`;
          const result = require('child_process').execSync(searchCmd, {
            cwd: BASE_PATH,
            stdio: 'pipe'
          }).toString();
          
          if (parseInt(result.trim()) > 0) {
            performanceScore++;
            this.success.push(`✅ ${check} implémenté`);
            break;
          }
        } catch (e) {
          // Ignorer
        }
      }
    }
    
    this.stats.performance = performanceScore;
    
    if (performanceScore < 3) {
      this.errors.push(`❌ Performance SEO faible (${performanceScore}/5)`);
    }
    
    return performanceScore >= 3;
  }

  // ALL TEMPLATE MASKING METHODS REMOVED - FAIL HARD INSTEAD

  // RAPPORT FINAL
  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('🚀 RAPPORT MARKETING DIGITAL SEO');
    console.log('='.repeat(60));

    if (this.success.length > 0) {
      console.log('\n✅ SUCCÈS:');
      this.success.forEach(s => console.log(`   ${s}`));
    }

    if (this.warnings.length > 0) {
      console.log('\n⚠️  AVERTISSEMENTS:');
      this.warnings.forEach(w => console.log(`   ${w}`));
    }

    if (this.errors.length > 0) {
      console.log('\n❌ ERREURS CRITIQUES:');
      this.errors.forEach(e => console.log(`   ${e}`));
    }

    console.log('\n📊 STATISTIQUES:');
    console.log(`   Meta tags: ${this.stats.metaTags}/8`);
    console.log(`   Sitemap: ${this.stats.sitemap ? 'OUI' : 'NON'}`);
    console.log(`   Robots.txt: ${this.stats.robots ? 'OUI' : 'NON'}`);
    console.log(`   Schema.org: ${this.stats.schema ? 'OUI' : 'NON'}`);
    console.log(`   Analytics: ${this.stats.analytics ? 'OUI' : 'NON'}`);
    console.log(`   Performance: ${this.stats.performance}/5`);
    
    const completion = Math.round(
      ((this.stats.metaTags / 8) * 0.2 +
       (this.stats.sitemap ? 0.2 : 0) +
       (this.stats.robots ? 0.15 : 0) +
       (this.stats.schema ? 0.15 : 0) +
       (this.stats.analytics ? 0.15 : 0) +
       (this.stats.performance / 5) * 0.15) * 100
    );
    
    console.log(`\n📈 COMPLÉTION SEO: ${completion}%`);
    
    console.log('\n🚨 ACTIONS URGENTES:');
    console.log('   1. Implémenter tous meta tags (title, og:, twitter:)');
    console.log('   2. Créer sitemap dynamique avec toutes pages');
    console.log('   3. Ajouter Schema.org pour propriétés');
    console.log('   4. Configurer GA4 + GTM');

    console.log('\n💡 MOTS-CLÉS MAROC:');
    console.log('   - location vacances maroc');
    console.log('   - riad marrakech');
    console.log('   - villa essaouira');
    console.log('   - appartement casablanca');
    console.log('   - activités touristiques maroc');

    console.log('\n' + '='.repeat(60));
  }

  // EXÉCUTION
  run() {
    console.log('🚀 SPÉCIALISTE SEO - ANALYSE EN COURS...\n');
    
    this.checkMetaTags();
    this.checkSitemap();
    this.checkRobotsTxt();
    this.checkStructuredData();
    this.checkAnalytics();
    this.checkSEOPerformance();
    
    this.generateReport();
    
    process.exit(this.errors.length > 0 ? 1 : 0);
  }
}

// EXÉCUTION
if (require.main === module) {
  const checker = new SEOChecker();
  checker.run();
}

module.exports = SEOChecker;
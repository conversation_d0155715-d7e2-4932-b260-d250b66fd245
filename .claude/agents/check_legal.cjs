#!/usr/bin/env node

/**
 * SPÉCIALISTE CONFORMITÉ LÉGALE MAROCAINE
 * Vérification RÉELLE de la conformité réglementaire
 */

const fs = require('fs');
const path = require('path');

const BASE_PATH = process.cwd();
const LEGAL_PATH = path.join(BASE_PATH, 'docs/legal');
const BOOKING_PATH = path.join(BASE_PATH, 'src/modules/booking');

// RÉGLEMENTATIONS MAROCAINES
const MOROCCO_REGULATIONS = {
  'CRI': 'Centre Régional d\'Investissement',
  'OMPIC': 'Office Marocain Propriété Industrielle',
  'CNDP': 'Commission Nationale Protection Données',
  'Police': 'Télédéclaration touristes obligatoire',
  'TVA': '20% standard, 10% tourisme',
  'CIN': 'Carte Identité Nationale requise',
  'Licence': 'Licence exploitation touristique'
};

class LegalChecker {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.success = [];
    this.stats = {
      compliance: 0,
      gdpr: false,
      cndp: false,
      tva: false,
      police: false
    };
  }

  // VÉRIFIER DÉCLARATION POLICE
  checkPoliceDeclaration() {
    console.log('👮 Vérification télédéclaration police...\n');
    
    // Rechercher système déclaration
    const declarationFiles = [
      'police-declaration.ts',
      'tourist-declaration.ts',
      'guest-registration.ts'
    ];
    
    let found = false;
    
    for (const file of declarationFiles) {
      const filePath = path.join(BOOKING_PATH, file);
      
      if (fs.existsSync(filePath)) {
        found = true;
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Vérifier champs obligatoires
        const requiredFields = [
          'passport',
          'nationality',
          'arrivalDate',
          'departureDate',
          'cin'
        ];
        
        let fieldsFound = 0;
        for (const field of requiredFields) {
          if (content.includes(field)) {
            fieldsFound++;
          }
        }
        
        if (fieldsFound === requiredFields.length) {
          this.success.push('✅ Tous champs police présents');
          this.stats.police = true;
        } else {
          this.warnings.push(`⚠️  ${fieldsFound}/${requiredFields.length} champs police`);
        }
        break;
      }
    }
    
    if (!found) {
      this.errors.push('❌ Système télédéclaration police ABSENT');
      this.errors.push('❌ ÉCHEC FRANC - PAS DE TEMPLATE MASQUAGE');
    }
    
    return found;
  }

  // VÉRIFIER TVA MAROC
  checkTVACompliance() {
    console.log('\n💰 Vérification conformité TVA...\n');
    
    // TVA Maroc: 20% standard, 10% tourisme
    const tvaRates = {
      standard: 0.20,
      tourism: 0.10,
      transport: 0.14,
      exempted: 0
    };
    
    // Chercher configuration TVA
    try {
      const searchCmd = 'grep -r "TVA\\|VAT\\|tax" src/ 2>/dev/null | head -20';
      const result = require('child_process').execSync(searchCmd, {
        cwd: BASE_PATH,
        stdio: 'pipe'
      }).toString();
      
      if (result.includes('0.20') || result.includes('20%')) {
        this.success.push('✅ TVA 20% configurée');
        this.stats.tva = true;
      } else if (result.includes('0.10') || result.includes('10%')) {
        this.success.push('✅ TVA tourisme 10% configurée');
        this.stats.tva = true;
      } else if (result.includes('tax')) {
        this.warnings.push('⚠️  Mentions de taxe mais taux incorrects');
      }
    } catch (e) {
      this.errors.push('❌ Configuration TVA absente');
    }
    
    if (!this.stats.tva) {
      console.log('\n💡 TAUX TVA MAROC:');
      for (const [type, rate] of Object.entries(tvaRates)) {
        console.log(`   ${type}: ${rate * 100}%`);
      }
    }
    
    return this.stats.tva;
  }

  // VÉRIFIER CNDP (RGPD MAROC)
  checkCNDPCompliance() {
    console.log('\n🔒 Vérification conformité CNDP (Loi 09-08)...\n');
    
    // CNDP = équivalent RGPD pour le Maroc
    const cndpRequirements = [
      'Déclaration traitement CNDP',
      'Consentement explicite',
      'Droit accès/rectification',
      'Sécurité des données',
      'Notification violations'
    ];
    
    // Vérifier mentions légales
    const legalFiles = [
      path.join(BASE_PATH, 'PRIVACY_POLICY.md'),
      path.join(BASE_PATH, 'docs/legal/privacy.md'),
      path.join(BASE_PATH, 'docs/legal/cndp.md')
    ];
    
    let cndpFound = false;
    
    for (const file of legalFiles) {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8');
        
        if (content.includes('CNDP') || content.includes('09-08')) {
          this.success.push('✅ Mentions CNDP trouvées');
          this.stats.cndp = true;
          cndpFound = true;
        } else if (content.includes('GDPR') || content.includes('RGPD')) {
          this.warnings.push('⚠️  RGPD européen au lieu de CNDP marocain');
          this.stats.gdpr = true;
        }
        break;
      }
    }
    
    if (!cndpFound) {
      this.errors.push('❌ Conformité CNDP (Loi 09-08) NON documentée');
      this.errors.push('❌ ÉCHEC FRANC - PAS DE TEMPLATE MASQUAGE');
    }
    
    return cndpFound;
  }

  // VÉRIFIER LICENCES TOURISTIQUES
  checkTourismLicenses() {
    console.log('\n📜 Vérification licences touristiques...\n');
    
    const requiredLicenses = {
      'Exploitation': 'Licence exploitation hébergement touristique',
      'Classement': 'Classement Ministère Tourisme',
      'RC': 'Registre Commerce',
      'Patente': 'Patente professionnelle',
      'CNSS': 'Affiliation CNSS',
      'Assurance': 'RC professionnelle'
    };
    
    let licensesFound = 0;
    
    // Chercher mentions licences
    for (const [type, desc] of Object.entries(requiredLicenses)) {
      try {
        const searchCmd = `grep -ri "${type.toLowerCase()}" ${BASE_PATH}/docs 2>/dev/null | wc -l`;
        const result = require('child_process').execSync(searchCmd, {
          cwd: BASE_PATH,
          stdio: 'pipe'
        }).toString();
        
        if (parseInt(result.trim()) > 0) {
          licensesFound++;
          this.warnings.push(`⚠️  ${desc} mentionné`);
        }
      } catch (e) {
        // Ignorer
      }
    }
    
    this.stats.compliance = licensesFound;
    
    if (licensesFound === 0) {
      this.errors.push('❌ AUCUNE licence touristique documentée');
    } else if (licensesFound < 3) {
      this.warnings.push(`⚠️  Seulement ${licensesFound}/6 licences`);
    } else {
      this.success.push(`✅ ${licensesFound}/6 licences référencées`);
    }
    
    return licensesFound > 0;
  }

  // TEMPLATE MASKING REMOVED - FAIL HARD INSTEAD

  // RAPPORT FINAL
  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('⚖️  RAPPORT CONFORMITÉ LÉGALE MAROCAINE');
    console.log('='.repeat(60));

    if (this.success.length > 0) {
      console.log('\n✅ SUCCÈS:');
      this.success.forEach(s => console.log(`   ${s}`));
    }

    if (this.warnings.length > 0) {
      console.log('\n⚠️  AVERTISSEMENTS:');
      this.warnings.forEach(w => console.log(`   ${w}`));
    }

    if (this.errors.length > 0) {
      console.log('\n❌ ERREURS CRITIQUES:');
      this.errors.forEach(e => console.log(`   ${e}`));
    }

    console.log('\n📊 STATISTIQUES:');
    console.log(`   Licences documentées: ${this.stats.compliance}/6`);
    console.log(`   Déclaration police: ${this.stats.police ? 'OUI' : 'NON'}`);
    console.log(`   TVA configurée: ${this.stats.tva ? 'OUI' : 'NON'}`);
    console.log(`   CNDP (Loi 09-08): ${this.stats.cndp ? 'OUI' : 'NON'}`);
    console.log(`   RGPD européen: ${this.stats.gdpr ? 'OUI (incorrect)' : 'NON'}`);
    
    const completion = Math.round(
      ((this.stats.compliance / 6) * 0.25 +
       (this.stats.police ? 0.25 : 0) +
       (this.stats.tva ? 0.25 : 0) +
       (this.stats.cndp ? 0.25 : 0)) * 100
    );
    
    console.log(`\n📈 COMPLÉTION LÉGALE: ${completion}%`);
    
    console.log('\n🚨 OBLIGATIONS CRITIQUES:');
    console.log('   1. TÉLÉDÉCLARATION POLICE OBLIGATOIRE (24h)');
    console.log('   2. TVA 20% standard / 10% tourisme');
    console.log('   3. Déclaration CNDP (Loi 09-08)');
    console.log('   4. Licence exploitation touristique CRI');

    console.log('\n⚠️  SANCTIONS:');
    console.log('   Non-déclaration police: 500-5000 MAD/touriste');
    console.log('   Non-conformité CNDP: 50,000-500,000 MAD');
    console.log('   TVA frauduleuse: Fermeture + pénal');

    console.log('\n' + '='.repeat(60));
  }

  // EXÉCUTION
  run() {
    console.log('⚖️  SPÉCIALISTE LÉGAL MAROC - ANALYSE EN COURS...\n');
    
    this.checkPoliceDeclaration();
    this.checkTVACompliance();
    this.checkCNDPCompliance();
    this.checkTourismLicenses();
    
    this.generateReport();
    
    process.exit(this.errors.length > 0 ? 1 : 0);
  }
}

// EXÉCUTION
if (require.main === module) {
  const checker = new LegalChecker();
  checker.run();
}

module.exports = LegalChecker;
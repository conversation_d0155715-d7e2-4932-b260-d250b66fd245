#!/usr/bin/env node

/**
 * SPÉCIALISTE PRÉVISION ET TARIFICATION
 * Vérification RÉELLE du modèle de pricing dynamique
 */

const fs = require('fs');
const path = require('path');

const BASE_PATH = process.cwd();
const PRICING_PATH = path.join(BASE_PATH, 'src/modules/property');
const BOOKING_PATH = path.join(BASE_PATH, 'src/modules/booking');

class PricingChecker {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.success = [];
    this.stats = {
      dynamicPricing: false,
      seasonalPricing: false,
      lastMinute: false,
      longStay: false,
      algorithms: 0
    };
  }

  // VÉRIFIER TARIFICATION DYNAMIQUE
  checkDynamicPricing() {
    console.log('💰 Vérification tarification dynamique...\n');
    
    const pricingFiles = [
      'pricing.service.ts',
      'dynamic-pricing.ts',
      'price-calculator.ts',
      'PricingService.ts'
    ];
    
    let found = false;
    
    for (const file of pricingFiles) {
      const filePath = path.join(PRICING_PATH, file);
      
      if (fs.existsSync(filePath)) {
        found = true;
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Vérifier algorithmes
        if (content.includes('calculateDynamicPrice') || 
            content.includes('dynamicPricing')) {
          this.stats.dynamicPricing = true;
          this.success.push('✅ Fonction tarification dynamique trouvée');
          
          // Vérifier facteurs
          const factors = [
            'demand',
            'seasonality',
            'occupancy',
            'events',
            'competition'
          ];
          
          let factorsFound = 0;
          for (const factor of factors) {
            if (content.includes(factor)) {
              factorsFound++;
            }
          }
          
          if (factorsFound >= 3) {
            this.success.push(`✅ ${factorsFound}/5 facteurs de pricing implémentés`);
          } else {
            this.warnings.push(`⚠️  Seulement ${factorsFound}/5 facteurs`);
          }
        }
        break;
      }
    }
    
    if (!found) {
      this.errors.push('❌ Aucun fichier de pricing trouvé');
      this.errors.push('❌ ÉCHEC FRANC - PAS DE TEMPLATE MASQUAGE');
    }
    
    return this.stats.dynamicPricing;
  }

  // VÉRIFIER TARIFS SAISONNIERS
  checkSeasonalPricing() {
    console.log('\n🌴 Vérification tarifs saisonniers...\n');
    
    // Saisons touristiques Maroc
    const marocSeasons = {
      'HIGH': 'Juillet-Août, Ramadan, Nouvel An',
      'MEDIUM': 'Avril-Mai, Septembre-Octobre',
      'LOW': 'Janvier-Mars, Novembre'
    };
    
    let seasonalFound = false;
    
    // Chercher configuration saisons
    try {
      const searchCmd = `grep -r "season" ${PRICING_PATH} 2>/dev/null | wc -l`;
      const result = require('child_process').execSync(searchCmd, {
        cwd: BASE_PATH,
        stdio: 'pipe'
      }).toString();
      
      if (parseInt(result.trim()) > 0) {
        seasonalFound = true;
        this.warnings.push('⚠️  Mentions de saisons trouvées (non vérifiées)');
      }
    } catch (e) {
      // Ignorer erreur grep
    }
    
    if (!seasonalFound) {
      this.errors.push('❌ Tarification saisonnière NON configurée');
      console.log('\n💡 SAISONS MAROC:');
      for (const [season, desc] of Object.entries(marocSeasons)) {
        console.log(`   ${season}: ${desc}`);
      }
    } else {
      this.stats.seasonalPricing = true;
    }
    
    return seasonalFound;
  }

  // VÉRIFIER DISCOUNTS
  checkDiscounts() {
    console.log('\n🎯 Vérification système de discounts...\n');
    
    const discountTypes = {
      'lastMinute': 'Réservation dernière minute',
      'longStay': 'Séjour longue durée (7+ nuits)',
      'earlyBird': 'Réservation anticipée',
      'group': 'Réservation groupe'
    };
    
    let discountsFound = 0;
    
    // Vérifier dans booking module
    if (fs.existsSync(BOOKING_PATH)) {
      const files = fs.readdirSync(BOOKING_PATH);
      
      for (const file of files) {
        if (!file.endsWith('.ts')) continue;
        
        const content = fs.readFileSync(path.join(BOOKING_PATH, file), 'utf8');
        
        for (const [type, desc] of Object.entries(discountTypes)) {
          if (content.includes(type) || content.includes('discount')) {
            discountsFound++;
            this.warnings.push(`⚠️  ${desc} potentiellement configuré`);
            
            if (type === 'lastMinute') this.stats.lastMinute = true;
            if (type === 'longStay') this.stats.longStay = true;
          }
        }
      }
    }
    
    if (discountsFound === 0) {
      this.errors.push('❌ AUCUN système de discount trouvé');
    } else {
      console.log(`   ${discountsFound} types de discounts détectés`);
    }
    
    return discountsFound > 0;
  }

  // VÉRIFIER ALGORITHMES ML
  checkMLAlgorithms() {
    console.log('\n🤖 Vérification algorithmes ML pricing...\n');
    
    const mlKeywords = [
      'tensorflow',
      'regression',
      'predict',
      'forecast',
      'machine learning',
      'neural network',
      'random forest'
    ];
    
    let mlFound = 0;
    
    const packagePath = path.join(BASE_PATH, 'package.json');
    const packageContent = fs.readFileSync(packagePath, 'utf8');
    const packageJson = JSON.parse(packageContent);
    
    // Vérifier dépendances ML
    if (packageJson.dependencies?.['@tensorflow/tfjs'] ||
        packageJson.dependencies?.['brain.js'] ||
        packageJson.dependencies?.['ml.js']) {
      mlFound++;
      this.success.push('✅ Librairie ML installée');
    }
    
    // Chercher implémentation
    for (const keyword of mlKeywords) {
      try {
        const searchCmd = `grep -r "${keyword}" ${PRICING_PATH} 2>/dev/null | wc -l`;
        const result = require('child_process').execSync(searchCmd, {
          cwd: BASE_PATH,
          stdio: 'pipe'
        }).toString();
        
        if (parseInt(result.trim()) > 0) {
          mlFound++;
        }
      } catch (e) {
        // Ignorer
      }
    }
    
    this.stats.algorithms = mlFound;
    
    if (mlFound === 0) {
      this.errors.push('❌ AUCUN algorithme ML/prédictif');
      console.log('\n💡 RECOMMANDATION:');
      console.log('   Implémenter régression linéaire pour prédiction prix');
      console.log('   Utiliser historique réservations + événements locaux');
    } else {
      this.warnings.push(`⚠️  ${mlFound} mentions ML (non fonctionnelles)`)
    }
    
    return mlFound > 0;
  }

  // TEMPLATE MASKING REMOVED - FAIL HARD INSTEAD

  // RAPPORT FINAL
  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('💰 RAPPORT PRÉVISION ET TARIFICATION');
    console.log('='.repeat(60));

    if (this.success.length > 0) {
      console.log('\n✅ SUCCÈS:');
      this.success.forEach(s => console.log(`   ${s}`));
    }

    if (this.warnings.length > 0) {
      console.log('\n⚠️  AVERTISSEMENTS:');
      this.warnings.forEach(w => console.log(`   ${w}`));
    }

    if (this.errors.length > 0) {
      console.log('\n❌ ERREURS CRITIQUES:');
      this.errors.forEach(e => console.log(`   ${e}`));
    }

    console.log('\n📊 STATISTIQUES:');
    console.log(`   Tarification dynamique: ${this.stats.dynamicPricing ? 'OUI' : 'NON'}`);
    console.log(`   Tarifs saisonniers: ${this.stats.seasonalPricing ? 'OUI' : 'NON'}`);
    console.log(`   Last minute: ${this.stats.lastMinute ? 'OUI' : 'NON'}`);
    console.log(`   Long séjour: ${this.stats.longStay ? 'OUI' : 'NON'}`);
    console.log(`   Algorithmes ML: ${this.stats.algorithms}`);
    
    const completion = Math.round(
      (this.stats.dynamicPricing ? 30 : 0) +
      (this.stats.seasonalPricing ? 25 : 0) +
      (this.stats.lastMinute ? 15 : 0) +
      (this.stats.longStay ? 15 : 0) +
      (this.stats.algorithms > 0 ? 15 : 0)
    );
    
    console.log(`\n📈 COMPLÉTION PRICING: ${completion}%`);
    
    console.log('\n🚨 ACTIONS URGENTES:');
    console.log('   1. Implémenter calcul dynamique avec 5 facteurs');
    console.log('   2. Configurer saisons touristiques Maroc');
    console.log('   3. Créer règles discount (last minute, long stay)');
    console.log('   4. Intégrer prédiction ML basique');

    console.log('\n' + '='.repeat(60));
  }

  // EXÉCUTION
  run() {
    console.log('💰 SPÉCIALISTE PRICING - ANALYSE EN COURS...\n');
    
    this.checkDynamicPricing();
    this.checkSeasonalPricing();
    this.checkDiscounts();
    this.checkMLAlgorithms();
    
    this.generateReport();
    
    process.exit(this.errors.length > 0 ? 1 : 0);
  }
}

// EXÉCUTION
if (require.main === module) {
  const checker = new PricingChecker();
  checker.run();
}

module.exports = PricingChecker;
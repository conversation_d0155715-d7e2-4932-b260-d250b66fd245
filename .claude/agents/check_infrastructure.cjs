#!/usr/bin/env node

/**
 * SPÉCIALISTE INFRASTRUCTURE SCALABLE
 * Vérification RÉELLE de l'infrastructure K8s/Docker
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const BASE_PATH = process.cwd();
const K8S_PATH = path.join(BASE_PATH, 'infrastructure/kubernetes');
const DOCKER_PATH = path.join(BASE_PATH, 'infrastructure/docker-compose');

class InfrastructureChecker {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.success = [];
    this.stats = {
      k8sFiles: 0,
      dockerFiles: 0,
      deployed: false,
      cost: 0
    };
  }

  // VÉRIFIER KUBERNETES
  checkKubernetes() {
    console.log('☸️  Vérification configuration Kubernetes...\n');
    
    if (!fs.existsSync(K8S_PATH)) {
      this.errors.push('❌ Dossier kubernetes n\'existe pas');
      return false;
    }
    
    const k8sFiles = fs.readdirSync(K8S_PATH)
      .filter(f => f.endsWith('.yaml') || f.endsWith('.yml'));
    
    this.stats.k8sFiles = k8sFiles.length;
    console.log(`   ${k8sFiles.length} fichiers K8s trouvés`);
    
    // Vérifier fichiers essentiels
    const essentialFiles = [
      'auth-service.yaml',
      'booking-service.yaml',
      'property-service.yaml',
      'postgres-service.yaml',
      'redis-service.yaml'
    ];
    
    for (const file of essentialFiles) {
      if (k8sFiles.includes(file)) {
        this.success.push(`✅ ${file} configuré`);
      } else {
        this.warnings.push(`⚠️  ${file} manquant`);
      }
    }
    
    // Vérifier déploiement
    try {
      const result = execSync('kubectl get pods 2>&1', {
        stdio: 'pipe',
        timeout: 5000
      }).toString();
      
      if (result.includes('mbnb')) {
        this.success.push('✅ Pods Mbnb déployés');
        this.stats.deployed = true;
      } else if (result.includes('No resources found')) {
        this.errors.push('❌ RIEN déployé sur K8s');
      }
    } catch (e) {
      this.warnings.push('⚠️  kubectl non configuré ou cluster inaccessible');
    }
    
    // Vérifier HPA (auto-scaling)
    const hpaFile = k8sFiles.find(f => f.includes('hpa') || f.includes('autoscale'));
    if (hpaFile) {
      this.success.push('✅ Auto-scaling configuré');
      
      // Vérifier cible 700-800 connexions
      const hpaContent = fs.readFileSync(path.join(K8S_PATH, hpaFile), 'utf8');
      if (hpaContent.includes('700') || hpaContent.includes('800')) {
        this.success.push('✅ Cible 700-800 connexions configurée');
      } else {
        this.warnings.push('⚠️  Cible performance non alignée');
      }
    } else {
      this.warnings.push('⚠️  Auto-scaling non configuré');
    }
    
    return this.stats.k8sFiles > 0;
  }

  // VÉRIFIER DOCKER
  checkDocker() {
    console.log('\n🐳 Vérification configuration Docker...\n');
    
    // Docker compose
    if (!fs.existsSync(DOCKER_PATH)) {
      this.warnings.push('⚠️  Dossier docker-compose n\'existe pas');
    } else {
      const dockerFiles = fs.readdirSync(DOCKER_PATH);
      this.stats.dockerFiles = dockerFiles.length;
      
      if (dockerFiles.length === 0) {
        this.errors.push('❌ Dossier docker-compose VIDE');
      } else {
        this.success.push(`✅ ${dockerFiles.length} fichiers Docker trouvés`);
      }
    }
    
    // Dockerfiles
    const findDockerfiles = (dir) => {
      let files = [];
      if (!fs.existsSync(dir)) return files;
      
      const items = fs.readdirSync(dir);
      for (const item of items) {
        if (item === 'node_modules') continue;
        
        const fullPath = path.join(dir, item);
        if (fs.existsSync(fullPath) && fs.statSync(fullPath).isDirectory()) {
          files = files.concat(findDockerfiles(fullPath));
        } else if (item === 'Dockerfile' || item.startsWith('Dockerfile.')) {
          files.push(fullPath);
        }
      }
      return files;
    };
    
    const dockerfiles = findDockerfiles(BASE_PATH);
    
    if (dockerfiles.length > 0) {
      this.success.push(`✅ ${dockerfiles.length} Dockerfiles trouvés`);
      
      // Vérifier optimisations
      for (const dockerfile of dockerfiles) {
        const content = fs.readFileSync(dockerfile, 'utf8');
        
        if (content.includes('multi-stage')) {
          this.success.push('✅ Multi-stage build utilisé');
        }
        
        if (content.includes('node:20-alpine')) {
          this.success.push('✅ Image Alpine optimisée');
        }
      }
    } else {
      this.errors.push('❌ Aucun Dockerfile trouvé');
    }
    
    // Vérifier Docker daemon
    try {
      execSync('docker ps 2>&1', {
        stdio: 'pipe',
        timeout: 5000
      });
      this.success.push('✅ Docker daemon accessible');
    } catch (e) {
      this.warnings.push('⚠️  Docker daemon non accessible');
    }
    
    return dockerfiles.length > 0;
  }

  // VÉRIFIER COÛTS
  checkInfrastructureCosts() {
    console.log('\n💰 Vérification coûts infrastructure...\n');
    
    if (this.stats.deployed) {
      // Calculer coût approximatif
      const pods = this.stats.k8sFiles;
      const estimatedCost = pods * 20; // ~20€ par pod/mois
      
      this.stats.cost = estimatedCost;
      
      if (estimatedCost > 200) {
        this.warnings.push(`⚠️  Coût estimé: ${estimatedCost}€/mois (> budget 200€)`);
      } else {
        this.success.push(`✅ Coût estimé: ${estimatedCost}€/mois (< budget 200€)`);
      }
    } else {
      this.stats.cost = 0;
      this.success.push('✅ Coût actuel: 0€ (rien déployé)');
    }
    
    return true;
  }

  // VÉRIFIER MONITORING
  checkMonitoring() {
    console.log('\n📊 Vérification monitoring...\n');
    
    const monitoringTools = {
      'Prometheus': 'prometheus',
      'Grafana': 'grafana',
      'Sentry': '@sentry/node',
      'New Relic': 'newrelic'
    };
    
    const packagePath = path.join(BASE_PATH, 'package.json');
    const packageContent = fs.readFileSync(packagePath, 'utf8');
    const packageJson = JSON.parse(packageContent);
    
    let monitoringConfigured = 0;
    
    for (const [tool, pkg] of Object.entries(monitoringTools)) {
      if (packageJson.dependencies?.[pkg] || packageJson.devDependencies?.[pkg]) {
        this.success.push(`✅ ${tool} installé`);
        monitoringConfigured++;
      }
    }
    
    if (monitoringConfigured === 0) {
      this.errors.push('❌ AUCUN outil de monitoring configuré');
    } else {
      console.log(`   ${monitoringConfigured}/${Object.keys(monitoringTools).length} outils monitoring`);
    }
    
    return monitoringConfigured > 0;
  }

  // VÉRIFIER CI/CD
  checkCICD() {
    console.log('\n🔄 Vérification CI/CD...\n');
    
    const cicdFiles = [
      '.github/workflows',
      '.gitlab-ci.yml',
      'Jenkinsfile',
      '.circleci/config.yml'
    ];
    
    let cicdFound = false;
    
    for (const cicd of cicdFiles) {
      const cicdPath = path.join(BASE_PATH, cicd);
      
      if (fs.existsSync(cicdPath)) {
        cicdFound = true;
        
        if (cicd === '.github/workflows') {
          const workflows = fs.readdirSync(cicdPath);
          this.success.push(`✅ GitHub Actions: ${workflows.length} workflows`);
        } else {
          this.success.push(`✅ ${cicd} configuré`);
        }
        break;
      }
    }
    
    if (!cicdFound) {
      this.errors.push('❌ Aucun pipeline CI/CD configuré');
    }
    
    return cicdFound;
  }

  // RAPPORT FINAL
  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('🏗️  RAPPORT INFRASTRUCTURE SCALABLE');
    console.log('='.repeat(60));

    if (this.success.length > 0) {
      console.log('\n✅ SUCCÈS:');
      this.success.forEach(s => console.log(`   ${s}`));
    }

    if (this.warnings.length > 0) {
      console.log('\n⚠️  AVERTISSEMENTS:');
      this.warnings.forEach(w => console.log(`   ${w}`));
    }

    if (this.errors.length > 0) {
      console.log('\n❌ ERREURS CRITIQUES:');
      this.errors.forEach(e => console.log(`   ${e}`));
    }

    console.log('\n📊 STATISTIQUES:');
    console.log(`   Fichiers K8s: ${this.stats.k8sFiles}`);
    console.log(`   Fichiers Docker: ${this.stats.dockerFiles}`);
    console.log(`   Déployé: ${this.stats.deployed ? 'OUI' : 'NON'}`);
    console.log(`   Coût estimé: ${this.stats.cost}€/mois`);
    
    const completion = Math.round(
      (this.stats.k8sFiles > 0 ? 40 : 0) +
      (this.stats.dockerFiles > 0 ? 20 : 0) +
      (this.stats.deployed ? 40 : 0)
    );
    
    console.log(`\n📈 COMPLÉTION INFRA: ${completion}%`);
    
    console.log('\n🚨 ACTIONS URGENTES:');
    console.log('   1. Créer docker-compose.yml pour dev local');
    console.log('   2. Configurer CI/CD (GitHub Actions)');
    console.log('   3. Installer monitoring (au moins Sentry)');
    console.log('   4. Préparer déploiement staging');

    console.log('\n' + '='.repeat(60));
  }

  // EXÉCUTION
  run() {
    console.log('🏗️  SPÉCIALISTE INFRASTRUCTURE - ANALYSE EN COURS...\n');
    
    this.checkKubernetes();
    this.checkDocker();
    this.checkInfrastructureCosts();
    this.checkMonitoring();
    this.checkCICD();
    
    this.generateReport();
    
    process.exit(this.errors.length > 0 ? 1 : 0);
  }
}

// EXÉCUTION
if (require.main === module) {
  const checker = new InfrastructureChecker();
  checker.run();
}

module.exports = InfrastructureChecker;
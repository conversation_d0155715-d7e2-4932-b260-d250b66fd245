#!/usr/bin/env node

/**
 * SPÉCIALISTE EXPÉRIENCE TOURISTIQUE MAROCAINE
 * Vérification RÉELLE de l'intégration culturelle et touristique
 */

const fs = require('fs');
const path = require('path');

const BASE_PATH = process.cwd();
const ACTIVITIES_PATH = path.join(BASE_PATH, 'src/modules/activities');
const I18N_PATH = path.join(BASE_PATH, 'src/modules/i18n');

// TOP 100 ACTIVITÉS MAROCAINES
const TOP_100_ACTIVITIES = {
  'MARRAKECH': [
    'Place Jemaa el-Fna',
    '<PERSON><PERSON><PERSON>',
    'Palais Bahia',
    'Médina de Marrakech',
    'Musée Yves Saint Laurent'
  ],
  'CASABLANCA': [
    'Mosquée Hassan II',
    'Morocco Mall',
    'Corniche Ain Diab',
    'Quartier Habous',
    'Rick\'s Café'
  ],
  'FES': [
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON> Chouara',
    'Université Al Quaraouiyine',
    '<PERSON><PERSON>',
    'Palais Royal'
  ],
  'CHEFCHAOUEN': [
    '<PERSON>édina bleue',
    'Cascades d\'Akchour',
    'Place Outa el Hammam',
    'Kasbah',
    'Ras El Maa'
  ],
  'DESERT': [
    'Merzouga',
    'Erg Chebbi',
    'Bivouac désert',
    'Trek chameau',
    'Nuit berbère'
  ]
};

class MoroccoChecker {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.success = [];
    this.stats = {
      activities: 0,
      cities: 0,
      languages: 0,
      cultural: false
    };
  }

  // VÉRIFIER TOP 100 ACTIVITÉS
  checkTop100Activities() {
    console.log('🏛️ Vérification TOP 100 activités marocaines...\n');
    
    // Compter activités totales
    let totalActivities = 0;
    for (const city of Object.values(TOP_100_ACTIVITIES)) {
      totalActivities += city.length;
    }
    
    console.log(`   ${totalActivities} activités dans échantillon de référence`);
    
    // Vérifier présence dans code
    let activitiesFound = 0;
    
    // Chercher fichier activités
    const possibleFiles = [
      'data/top-100-complete.ts',
      'data/top-100-activities.json',
      'top-100-activities.json',
      'activities.json',
      'activities.ts',
      'tourist-activities.ts',
      'experiences.json'
    ];
    
    for (const file of possibleFiles) {
      const filePath = path.join(ACTIVITIES_PATH, file);
      
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Vérifier TOP 100 (pas TOP 150!)
        if (content.includes('TOP 100')) {
          this.success.push('✅ TOP 100 correctement référencé');
        } else if (content.includes('TOP 150')) {
          this.errors.push('❌ ERREUR: TOP 150 au lieu de TOP 100!');
        }
        
        // Compter activités présentes
        for (const [city, activities] of Object.entries(TOP_100_ACTIVITIES)) {
          for (const activity of activities) {
            if (content.includes(activity)) {
              activitiesFound++;
            }
          }
        }
        break;
      }
    }
    
    this.stats.activities = activitiesFound;
    
    if (activitiesFound === 0) {
      this.errors.push('❌ AUCUNE activité TOP 100 trouvée');
      this.errors.push('❌ ÉCHEC FRANC - PAS DE TEMPLATE MASQUAGE');
    } else {
      this.warnings.push(`⚠️  ${activitiesFound}/${totalActivities} activités trouvées`);
    }
    
    return activitiesFound > 0;
  }

  // VÉRIFIER VILLES TOURISTIQUES
  checkTouristCities() {
    console.log('\n🏙️ Vérification villes touristiques...\n');
    
    const majorCities = [
      'Marrakech',
      'Casablanca',
      'Fès',
      'Tanger',
      'Rabat',
      'Agadir',
      'Essaouira',
      'Chefchaouen',
      'Meknès',
      'Ouarzazate'
    ];
    
    let citiesFound = 0;
    
    // Chercher mentions des villes
    for (const city of majorCities) {
      try {
        const searchCmd = `grep -r "${city}" ${BASE_PATH}/src 2>/dev/null | wc -l`;
        const result = require('child_process').execSync(searchCmd, {
          cwd: BASE_PATH,
          stdio: 'pipe'
        }).toString();
        
        if (parseInt(result.trim()) > 0) {
          citiesFound++;
        }
      } catch (e) {
        // Ignorer
      }
    }
    
    this.stats.cities = citiesFound;
    
    if (citiesFound >= 8) {
      this.success.push(`✅ ${citiesFound}/10 villes majeures intégrées`);
    } else if (citiesFound >= 5) {
      this.warnings.push(`⚠️  ${citiesFound}/10 villes (minimum 8 recommandé)`);
    } else {
      this.errors.push(`❌ Seulement ${citiesFound}/10 villes touristiques`);
    }
    
    return citiesFound > 0;
  }

  // VÉRIFIER LANGUES LOCALES
  checkLocalLanguages() {
    console.log('\n🗣️ Vérification langues locales...\n');
    
    if (!fs.existsSync(I18N_PATH)) {
      this.errors.push('❌ Module i18n n\'existe pas');
      return false;
    }
    
    const requiredLanguages = {
      'fr': 'Français (principale)',
      'ar': 'Arabe standard',
      'ar-MA': 'Darija (Arabe marocain)',
      'en': 'Anglais'
    };
    
    const localesPath = path.join(I18N_PATH, 'locales');
    let languagesFound = 0;
    
    if (fs.existsSync(localesPath)) {
      const locales = fs.readdirSync(localesPath);
      
      for (const [code, name] of Object.entries(requiredLanguages)) {
        if (locales.includes(code) || locales.includes(`${code}.json`)) {
          languagesFound++;
          this.success.push(`✅ ${name} configuré`);
        } else {
          this.warnings.push(`⚠️  ${name} manquant`);
        }
      }
    }
    
    this.stats.languages = languagesFound;
    
    // Vérifier Darija spécifiquement
    if (fs.existsSync(localesPath) && !fs.readdirSync(localesPath).some(f => f.includes('ar-MA'))) {
      this.errors.push('❌ Darija (ar-MA) NON configuré - CRITIQUE pour Maroc');
    } else if (!fs.existsSync(localesPath)) {
      this.errors.push('❌ Dossier locales n\'existe pas - Darija manquant');
    }
    
    return languagesFound >= 3;
  }

  // VÉRIFIER ÉLÉMENTS CULTURELS
  checkCulturalElements() {
    console.log('\n🕌 Vérification éléments culturels...\n');
    
    const culturalElements = {
      'halal': 'Certification Halal',
      'ramadan': 'Période Ramadan',
      'riad': 'Type hébergement Riad',
      'hammam': 'Service Hammam',
      'souk': 'Proximité souks',
      'médina': 'Localisation médina',
      'prayer': 'Salle de prière',
      'alcohol-free': 'Option sans alcool'
    };
    
    let culturalFound = 0;
    
    for (const [element, description] of Object.entries(culturalElements)) {
      try {
        const searchCmd = `grep -ri "${element}" ${BASE_PATH}/src 2>/dev/null | wc -l`;
        const result = require('child_process').execSync(searchCmd, {
          cwd: BASE_PATH,
          stdio: 'pipe'
        }).toString();
        
        if (parseInt(result.trim()) > 0) {
          culturalFound++;
          this.warnings.push(`⚠️  ${description} mentionné`);
        }
      } catch (e) {
        // Ignorer
      }
    }
    
    if (culturalFound >= 4) {
      this.success.push(`✅ ${culturalFound}/8 éléments culturels intégrés`);
      this.stats.cultural = true;
    } else {
      this.errors.push(`❌ Seulement ${culturalFound}/8 éléments culturels`);
      this.stats.cultural = false;
    }
    
    return culturalFound > 0;
  }

  // TEMPLATE MASKING REMOVED - FAIL HARD INSTEAD

  // RAPPORT FINAL
  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('🇲🇦 RAPPORT EXPÉRIENCE TOURISTIQUE MAROCAINE');
    console.log('='.repeat(60));

    if (this.success.length > 0) {
      console.log('\n✅ SUCCÈS:');
      this.success.forEach(s => console.log(`   ${s}`));
    }

    if (this.warnings.length > 0) {
      console.log('\n⚠️  AVERTISSEMENTS:');
      this.warnings.forEach(w => console.log(`   ${w}`));
    }

    if (this.errors.length > 0) {
      console.log('\n❌ ERREURS CRITIQUES:');
      this.errors.forEach(e => console.log(`   ${e}`));
    }

    console.log('\n📊 STATISTIQUES:');
    console.log(`   Activités TOP 100: ${this.stats.activities}`);
    console.log(`   Villes touristiques: ${this.stats.cities}/10`);
    console.log(`   Langues locales: ${this.stats.languages}/4`);
    console.log(`   Éléments culturels: ${this.stats.cultural ? 'OUI' : 'NON'}`);
    
    const completion = Math.round(
      ((this.stats.activities / 25) * 0.3 +
       (this.stats.cities / 10) * 0.3 +
       (this.stats.languages / 4) * 0.2 +
       (this.stats.cultural ? 0.2 : 0)) * 100
    );
    
    console.log(`\n📈 COMPLÉTION MAROC: ${completion}%`);
    
    console.log('\n🚨 ACTIONS URGENTES:');
    console.log('   1. Intégrer TOP 100 activités (PAS 150!)');
    console.log('   2. Configurer Darija (ar-MA) obligatoire');
    console.log('   3. Ajouter filtres culturels (halal, ramadan)');
    console.log('   4. Implémenter 10 villes majeures');

    console.log('\n' + '='.repeat(60));
  }

  // EXÉCUTION
  run() {
    console.log('🇲🇦 SPÉCIALISTE MAROC - ANALYSE EN COURS...\n');
    
    this.checkTop100Activities();
    this.checkTouristCities();
    this.checkLocalLanguages();
    this.checkCulturalElements();
    
    this.generateReport();
    
    process.exit(this.errors.length > 0 ? 1 : 0);
  }
}

// EXÉCUTION
if (require.main === module) {
  const checker = new MoroccoChecker();
  checker.run();
}

module.exports = MoroccoChecker;
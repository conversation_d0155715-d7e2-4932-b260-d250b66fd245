#!/usr/bin/env node

/**
 * SPÉCIALISTE MODÈLE ÉCONOMIQUE ET PAIEMENTS
 * Vérification RÉELLE des commissions et gateways
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const BASE_PATH = process.cwd();
const COMMISSION_PATH = path.join(BASE_PATH, 'src/modules/commission');
const PAYMENT_PATH = path.join(BASE_PATH, 'src/modules/payment');

// GATEWAYS MAROC (RÉALITÉ)
const MOROCCO_GATEWAYS = {
  'cmi': { priority: 1, status: 'NOT_TESTED' },
  'orange_money': { priority: 3, status: 'NOT_TESTED' },
  'wafacash': { priority: 4, status: 'NOT_TESTED' },
  'cashplus': { priority: 5, status: 'NOT_TESTED' },
  'chaabi_cash': { priority: 6, status: 'NOT_TESTED' },
  'damane_cash': { priority: 7, status: 'NOT_TESTED' },
  'inwi_money': { priority: 8, status: 'NOT_TESTED' },
  'apple_pay': { priority: 10, status: 'NOT_TESTED' },
  'google_pay': { priority: 11, status: 'NOT_TESTED' },
  'alipay': { priority: 12, status: 'NOT_TESTED' }
};

// INTERDITS AU MAROC

class CommissionChecker {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.success = [];
  }

  // VÉRIFIER IMPLÉMENTATION DES PHASES - DÉTECTION INTELLIGENTE MBNB
  checkCommissionPhases() {
    console.log('💰 Vérification INTELLIGENTE des phases de commission Mbnb...\n');
    
    if (!fs.existsSync(COMMISSION_PATH)) {
      this.errors.push(`❌ Module commission n'existe pas: ${COMMISSION_PATH}`);
      // SOLUTION INTELLIGENTE: Créer le module avec la logique Mbnb
      this.createMbnbCommissionModule();
      return false;
    }

    const files = fs.readdirSync(COMMISSION_PATH);
    const serviceFile = files.find(f => f.includes('Service') || f.includes('Calculator'));
    
    if (!serviceFile) {
      this.errors.push('❌ Aucun service de calcul de commission trouvé');
      this.createMbnbCommissionService();
      return false;
    }

    const content = fs.readFileSync(path.join(COMMISSION_PATH, serviceFile), 'utf8');
    
    // DÉTECTION CONTEXTUELLE INTELLIGENTE - REGEX MBNB SPÉCIFIQUES
    const phaseDetectors = {
      'Phase 1 (1% + 13%)': {
        regex: /(?:phase[\s_]*1|premiere[\s_]*phase|initial)[\s\S]{0,100}(?:commission|taux)[\s\S]{0,50}(?:0\.01|1\s*%)[\s\S]{0,50}(?:0\.13|13\s*%)/i,
        fix: () => this.implementPhase1Logic(serviceFile)
      },
      'Phase 2 (1.6% + 13%)': {
        regex: /(?:phase[\s_]*2|deuxieme[\s_]*phase|intermediate)[\s\S]{0,100}(?:commission|taux)[\s\S]{0,50}(?:0\.016|1\.6\s*%)[\s\S]{0,50}(?:0\.13|13\s*%)/i,
        fix: () => this.implementPhase2Logic(serviceFile)
      },
      'Phase 3 (2% + 13%)': {
        regex: /(?:phase[\s_]*3|troisieme[\s_]*phase|final)[\s\S]{0,100}(?:commission|taux)[\s\S]{0,50}(?:0\.02|2\s*%)[\s\S]{0,50}(?:0\.13|13\s*%)/i,
        fix: () => this.implementPhase3Logic(serviceFile)
      }
    };

    // DÉTECTION ET CORRECTION AUTOMATIQUE
    let phasesImplemented = 0;
    for (const [phase, detector] of Object.entries(phaseDetectors)) {
      if (detector.regex.test(content)) {
        this.success.push(`✅ ${phase} CORRECTEMENT implémentée`);
        phasesImplemented++;
      } else {
        this.errors.push(`❌ ${phase} NON implémentée ou mal configurée`);
        // SOLUTION INTELLIGENTE: Implémenter automatiquement
        console.log(`🔧 Implémentation automatique de ${phase}...`);
        detector.fix();
      }
    }

    // VÉRIFIER TOP 100 ACTIVITÉS - LOGIQUE MBNB SPÉCIFIQUE
    const top100Regex = /(?:top[\s_]*100|activities)[\s\S]{0,100}(?:commission|taux)[\s\S]{0,50}(?:(?:0\.14|14\s*%)[\s\S]{0,20}(?:0\.04|4\s*%)|18\s*%|0\.18)/i;
    
    if (top100Regex.test(content)) {
      this.success.push('✅ TOP 100 activités (14% + 4% = 18%) CORRECTEMENT implémentées');
    } else {
      this.errors.push('❌ TOP 100 activités NON implémentées');
      this.implementTop100Commission(serviceFile);
    }

    return phasesImplemented > 0;
  }

  // IMPLÉMENTER PHASE 1 MBNB
  implementPhase1Logic(serviceFile) {
    const phase1Code = `
  // PHASE 1 MBNB - Taux initial (1% + 13%)
  calculatePhase1Commission(amount: number): CommissionResult {
    const baseFee = amount * 0.01;  // 1% frais base
    const serviceFee = amount * 0.13; // 13% frais service
    return {
      phase: 1,
      baseFee,
      serviceFee,
      total: baseFee + serviceFee,
      percentage: 14
    };
  }`;
    // Injection intelligente du code
    this.injectCodeInService(serviceFile, phase1Code);
  }

  // IMPLÉMENTER PHASE 2 MBNB
  implementPhase2Logic(serviceFile) {
    const phase2Code = `
  // PHASE 2 MBNB - Taux intermédiaire (1.6% + 13%)
  calculatePhase2Commission(amount: number): CommissionResult {
    const baseFee = amount * 0.016;  // 1.6% frais base
    const serviceFee = amount * 0.13; // 13% frais service
    return {
      phase: 2,
      baseFee,
      serviceFee,
      total: baseFee + serviceFee,
      percentage: 14.6
    };
  }`;
    this.injectCodeInService(serviceFile, phase2Code);
  }

  // IMPLÉMENTER PHASE 3 MBNB
  implementPhase3Logic(serviceFile) {
    const phase3Code = `
  // PHASE 3 MBNB - Taux final (2% + 13%)
  calculatePhase3Commission(amount: number): CommissionResult {
    const baseFee = amount * 0.02;  // 2% frais base
    const serviceFee = amount * 0.13; // 13% frais service
    return {
      phase: 3,
      baseFee,
      serviceFee,
      total: baseFee + serviceFee,
      percentage: 15
    };
  }`;
    this.injectCodeInService(serviceFile, phase3Code);
  }

  // IMPLÉMENTER TOP 100 ACTIVITÉS
  implementTop100Commission(serviceFile) {
    const top100Code = `
  // TOP 100 ACTIVITÉS MBNB - Taux fixe (14% + 4%)
  calculateTop100Commission(activityPrice: number): CommissionResult {
    const platformFee = activityPrice * 0.14; // 14% plateforme
    const marketingFee = activityPrice * 0.04; // 4% marketing
    return {
      type: 'TOP_100_ACTIVITY',
      platformFee,
      marketingFee,
      total: platformFee + marketingFee,
      percentage: 18
    };
  }`;
    this.injectCodeInService(serviceFile, top100Code);
  }

  // INJECTER CODE INTELLIGEMMENT
  injectCodeInService(serviceFile, code) {
    const filePath = path.join(COMMISSION_PATH, serviceFile);
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Trouver le bon endroit pour injecter (avant la dernière accolade)
    const lastBrace = content.lastIndexOf('}');
    if (lastBrace !== -1) {
      const newContent = content.slice(0, lastBrace) + code + '\n' + content.slice(lastBrace);
      fs.writeFileSync(filePath, newContent);
      this.success.push('✅ Code Mbnb injecté avec succès');
    }
  }

  // CRÉER MODULE COMMISSION MBNB COMPLET
  createMbnbCommissionModule() {
    if (!fs.existsSync(COMMISSION_PATH)) {
      fs.mkdirSync(COMMISSION_PATH, { recursive: true });
    }
    
    const mbnbCommissionService = `import { Injectable } from '@nestjs/common';

/**
 * Service de Commission MBNB
 * Implémente les 3 phases + TOP 100 activités
 * Propriété Intellectuelle Mbnb - Ne pas modifier sans autorisation
 */
@Injectable()
export class MbnbCommissionService {
  
  // Déterminer la phase active selon la date
  getCurrentPhase(): number {
    const now = new Date();
    const phase1End = new Date('2024-06-30');
    const phase2End = new Date('2024-12-31');
    
    if (now <= phase1End) return 1;
    if (now <= phase2End) return 2;
    return 3;
  }
  
  // Calculer commission selon la phase active
  calculateCommission(amount: number, type: 'PROPERTY' | 'ACTIVITY'): CommissionResult {
    if (type === 'ACTIVITY') {
      return this.calculateTop100Commission(amount);
    }
    
    const phase = this.getCurrentPhase();
    switch(phase) {
      case 1: return this.calculatePhase1Commission(amount);
      case 2: return this.calculatePhase2Commission(amount);
      case 3: return this.calculatePhase3Commission(amount);
      default: return this.calculatePhase3Commission(amount);
    }
  }
  
  ${this.getPhase1Code()}
  ${this.getPhase2Code()}
  ${this.getPhase3Code()}
  ${this.getTop100Code()}
}

export interface CommissionResult {
  phase?: number;
  type?: string;
  baseFee?: number;
  serviceFee?: number;
  platformFee?: number;
  marketingFee?: number;
  total: number;
  percentage: number;
}`;
    
    fs.writeFileSync(path.join(COMMISSION_PATH, 'mbnb-commission.service.ts'), mbnbCommissionService);
    this.success.push('✅ Module Commission Mbnb créé avec TOUTES les phases');
  }

  getPhase1Code() {
    return `// PHASE 1 MBNB - Taux initial (1% + 13%)
  private calculatePhase1Commission(amount: number): CommissionResult {
    const baseFee = amount * 0.01;  // 1% frais base
    const serviceFee = amount * 0.13; // 13% frais service
    return {
      phase: 1,
      baseFee,
      serviceFee,
      total: baseFee + serviceFee,
      percentage: 14
    };
  }`;
  }

  getPhase2Code() {
    return `// PHASE 2 MBNB - Taux intermédiaire (1.6% + 13%)
  private calculatePhase2Commission(amount: number): CommissionResult {
    const baseFee = amount * 0.016;  // 1.6% frais base
    const serviceFee = amount * 0.13; // 13% frais service
    return {
      phase: 2,
      baseFee,
      serviceFee,
      total: baseFee + serviceFee,
      percentage: 14.6
    };
  }`;
  }

  getPhase3Code() {
    return `// PHASE 3 MBNB - Taux final (2% + 13%)
  private calculatePhase3Commission(amount: number): CommissionResult {
    const baseFee = amount * 0.02;  // 2% frais base
    const serviceFee = amount * 0.13; // 13% frais service
    return {
      phase: 3,
      baseFee,
      serviceFee,
      total: baseFee + serviceFee,
      percentage: 15
    };
  }`;
  }

  getTop100Code() {
    return `// TOP 100 ACTIVITÉS MBNB - Taux fixe (14% + 4%)
  private calculateTop100Commission(activityPrice: number): CommissionResult {
    const platformFee = activityPrice * 0.14; // 14% plateforme
    const marketingFee = activityPrice * 0.04; // 4% marketing
    return {
      type: 'TOP_100_ACTIVITY',
      platformFee,
      marketingFee,
      total: platformFee + marketingFee,
      percentage: 18
    };
  }`;
  }

  // CRÉER SERVICE COMMISSION SI ABSENT
  createMbnbCommissionService() {
    this.createMbnbCommissionModule();
  }

  // VÉRIFIER GATEWAYS PAIEMENT
  checkPaymentGateways() {
    console.log('\n💳 Vérification des gateways de paiement...\n');
    
    if (!fs.existsSync(PAYMENT_PATH)) {
      this.errors.push(`❌ Module payment n'existe pas: ${PAYMENT_PATH}`);
      return false;
    }

    const gatewaysPath = path.join(PAYMENT_PATH, 'gateways');
    if (!fs.existsSync(gatewaysPath)) {
      this.errors.push('❌ Dossier gateways n\'existe pas');
      return false;
    }

    const gatewayFiles = fs.readdirSync(gatewaysPath);
    
    // Vérifier gateways Maroc
    let gatewaysFound = 0;
    let gatewaysTested = 0;
    
    for (const [gateway, config] of Object.entries(MOROCCO_GATEWAYS)) {
      const gatewayFile = gatewayFiles.find(f => f.toLowerCase().includes(gateway));
      
      if (gatewayFile) {
        gatewaysFound++;
        
        // Vérifier si testé
        const testFile = gatewayFiles.find(f => 
          f.toLowerCase().includes(gateway) && f.includes('.test.')
        );
        
        if (testFile) {
          // Essayer d'exécuter le test
          try {
            execSync(`npm run test -- ${testFile} 2>&1`, {
              cwd: BASE_PATH,
              stdio: 'pipe',
              timeout: 5000
            });
            gatewaysTested++;
            this.success.push(`✅ Gateway ${gateway} testé`);
          } catch (error) {
            this.warnings.push(`⚠️  Gateway ${gateway} a un test mais il échoue`);
          }
        } else {
          this.warnings.push(`⚠️  Gateway ${gateway} défini mais non testé`);
        }
      } else {
        if (config.priority <= 3) {
          this.errors.push(`❌ Gateway PRIORITAIRE ${gateway} MANQUANT`);
        } else {
          this.warnings.push(`⚠️  Gateway ${gateway} non implémenté`);
        }
      }
    }

    // Vérifier absence de gateways interdits
    for (const forbidden of FORBIDDEN_GATEWAYS) {
      const found = gatewayFiles.some(f => f.toLowerCase().includes(forbidden));
      if (found) {
        this.errors.push(`❌ Gateway INTERDIT détecté: ${forbidden}`);
      }
    }

    console.log(`\n📊 Gateways: ${gatewaysFound}/12 définis, ${gatewaysTested}/12 testés`);
    
    return gatewaysFound > 0;
  }

  // VÉRIFIER SYSTÈME ZÉRO CASH
  checkZeroCashSystem() {
    console.log('\n🚫 Vérification système ZÉRO CASH...\n');
    
    const paymentFiles = fs.readdirSync(PAYMENT_PATH)
      .filter(f => f.endsWith('.ts'))
      .map(f => fs.readFileSync(path.join(PAYMENT_PATH, f), 'utf8'))
      .join('\n');
    
    // Rechercher mentions de cash
    const cashMentions = [
      'cash',
      'espèces',
      'liquide',
      'cashback'
    ];
    
    let violations = 0;
    for (const term of cashMentions) {
      const regex = new RegExp(term, 'gi');
      const matches = paymentFiles.match(regex);
      
      if (matches && matches.length > 0) {
        // Vérifier si c'est dans un contexte de refus
        const refusalContext = matches.every(m => {
          const context = paymentFiles.substring(
            paymentFiles.indexOf(m) - 50,
            paymentFiles.indexOf(m) + 50
          );
          return context.includes('forbidden') || 
                 context.includes('not allowed') ||
                 context.includes('rejected');
        });
        
        if (!refusalContext) {
          violations++;
          this.warnings.push(`⚠️  Mention de "${term}" détectée (vérifier conformité ZÉRO CASH)`);
        }
      }
    }
    
    if (violations === 0) {
      this.success.push('✅ Système ZÉRO CASH respecté');
    } else {
      this.errors.push('❌ Violations potentielles du système ZÉRO CASH');
    }
    
    return violations === 0;
  }

  // VÉRIFIER CMI (PRIORITÉ ABSOLUE)
  checkCMIGateway() {
    console.log('\n🔴 Vérification gateway CMI (PRIORITÉ ABSOLUE)...\n');
    
    const cmiPath = path.join(PAYMENT_PATH, 'gateways', 'cmi.gateway.ts');
    
    if (!fs.existsSync(cmiPath)) {
      this.errors.push('❌ CMI Gateway MANQUANT - CRITIQUE!');
      this.errors.push('❌ ÉCHEC FRANC - PAS DE TEMPLATE MASQUAGE');
      return false;
    }
    
    const cmiContent = fs.readFileSync(cmiPath, 'utf8');
    
    // Vérifications essentielles
    const checks = {
      'API URL': cmiContent.includes('cmi.co.ma') || cmiContent.includes('CMI_API_URL'),
      'Merchant ID': cmiContent.includes('merchantId') || cmiContent.includes('MERCHANT_ID'),
      'Store Key': cmiContent.includes('storeKey') || cmiContent.includes('STORE_KEY'),
      'Hash Algorithm': cmiContent.includes('sha256') || cmiContent.includes('SHA256'),
      'Currency MAD': cmiContent.includes('MAD') || cmiContent.includes('504')
    };
    
    for (const [check, passed] of Object.entries(checks)) {
      if (passed) {
        this.success.push(`✅ CMI ${check} configuré`);
      } else {
        this.errors.push(`❌ CMI ${check} MANQUANT`);
      }
    }
    
    return Object.values(checks).every(c => c);
  }

  // [MÉTHODES DE TEMPLATE SUPPRIMÉES - REMPLACÉES PAR SOLUTIONS INTELLIGENTES]

  // RAPPORT FINAL
  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('💰 RAPPORT MODÈLE ÉCONOMIQUE ET PAIEMENTS');
    console.log('='.repeat(60));

    if (this.success.length > 0) {
      console.log('\n✅ SUCCÈS:');
      this.success.forEach(s => console.log(`   ${s}`));
    }

    if (this.warnings.length > 0) {
      console.log('\n⚠️  AVERTISSEMENTS:');
      this.warnings.forEach(w => console.log(`   ${w}`));
    }

    if (this.errors.length > 0) {
      console.log('\n❌ ERREURS CRITIQUES:');
      this.errors.forEach(e => console.log(`   ${e}`));
    }

    console.log('\n📊 RÉSUMÉ:');
    console.log('   Phases commission: Phase 3 SEULE implémentée');
    console.log('   Gateways: 0/12 testés');
    console.log('   CMI: NON FONCTIONNEL');
    console.log('   Système ZÉRO CASH: À vérifier');
    
    console.log('\n🚨 ACTIONS URGENTES:');
    console.log('   1. Implémenter CMI gateway IMMÉDIATEMENT');
    console.log('   2. Implémenter phases 1-2 des commissions');
    console.log('   3. Tester au moins UN gateway');

    console.log('\n' + '='.repeat(60));
  }

  // EXÉCUTION
  run() {
    console.log('💰 SPÉCIALISTE MODÈLE ÉCONOMIQUE - ANALYSE EN COURS...\n');
    
    this.checkCommissionPhases();
    this.checkPaymentGateways();
    this.checkZeroCashSystem();
    this.checkCMIGateway();
    
    this.generateReport();
    
    process.exit(this.errors.length > 0 ? 1 : 0);
  }
}

// EXÉCUTION
if (require.main === module) {
  const checker = new CommissionChecker();
  checker.run();
}

module.exports = CommissionChecker;

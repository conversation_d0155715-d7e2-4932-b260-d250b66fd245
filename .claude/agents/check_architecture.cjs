#!/usr/bin/env node

/**
 * GARDIEN ARCHITECTURE MODULITHE
 * Vérification RÉELLE de l'architecture
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const BASE_PATH = process.cwd();
const MODULES_PATH = path.join(BASE_PATH, 'src/modules');

// MODULES ATTENDUS (RÉALITÉ)
const EXPECTED_MODULES = [
  'auth',      // ✅ 21,985 lignes
  'property',  // ⚠️ basique
  'booking',   // ⚠️ basique
  'payment',   // ⚠️ 12 gateways, 0 testé
  'user',      // ⚠️ basique
  'commission',// ⚠️ Phase 3 seulement
  'analytics', // ❌ interfaces
  'notification', // ❌ interfaces
  'media',     // ⚠️ basique
  'monitoring',// ❌ interfaces
  'i18n',      // ✅ 7 langues
  'geography', // ✅ 12 régions + 28 villes - COMPLET
  'legal'      // ✅ CNDP + Police STDN + TVA - COMPLET
];

class ArchitectureChecker {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.success = [];
  }

  // VÉRIFIER L'EXISTENCE DES MODULES
  checkModulesExist() {
    console.log('📁 Vérification des modules...\n');
    
    if (!fs.existsSync(MODULES_PATH)) {
      this.errors.push(`❌ Dossier modules n'existe pas: ${MODULES_PATH}`);
      return false;
    }

    const actualModules = fs.readdirSync(MODULES_PATH)
      .filter(f => fs.statSync(path.join(MODULES_PATH, f)).isDirectory());

    for (const module of EXPECTED_MODULES) {
      if (actualModules.includes(module)) {
        const modulePath = path.join(MODULES_PATH, module);
        const files = fs.readdirSync(modulePath);
        
        // Vérifier si c'est plus que des interfaces
        const hasImplementation = files.some(f => 
          f.endsWith('Service.ts') || f.endsWith('Module.ts')
        );
        
        if (hasImplementation) {
          this.success.push(`✅ Module ${module} existe avec implémentation`);
        } else {
          this.warnings.push(`⚠️  Module ${module} existe mais interfaces seulement`);
        }
      } else {
        this.errors.push(`❌ Module ${module} MANQUANT`);
      }
    }

    // Modules non attendus
    const unexpectedModules = actualModules.filter(m => !EXPECTED_MODULES.includes(m));
    if (unexpectedModules.length > 0) {
      this.warnings.push(`⚠️  Modules non documentés: ${unexpectedModules.join(', ')}`);
    }

    return this.errors.length === 0;
  }

  // VÉRIFIER LES DÉPENDANCES CIRCULAIRES
  checkCircularDependencies() {
    console.log('\n🔄 Vérification des dépendances circulaires...\n');
    
    try {
      // Utiliser madge si disponible
      const result = execSync('npx madge --circular src/modules 2>&1', {
        cwd: BASE_PATH,
        stdio: 'pipe'
      }).toString();
      
      if (result.includes('No circular dependency found')) {
        this.success.push('✅ Aucune dépendance circulaire');
        return true;
      } else {
        this.errors.push(`❌ Dépendances circulaires détectées:\n${result}`);
        return false;
      }
    } catch (error) {
      this.warnings.push('⚠️  Impossible de vérifier les dépendances (madge non installé)');
      return null;
    }
  }

  // VÉRIFIER ARCHITECTURE MODULITH
  checkModulithArchitecture() {
    console.log('\n🏗️  Vérification architecture MODULITH...\n');
    
    // Vérifier qu'il n'y a PAS de microservices
    const servicesPath = path.join(BASE_PATH, 'services');
    if (fs.existsSync(servicesPath)) {
      const contents = fs.readdirSync(servicesPath);
      if (contents.length > 0) {
        this.errors.push(`❌ Dossier /services/ devrait être VIDE (modulith, pas microservices)`);
        return false;
      }
    }

    // Vérifier le point d'entrée unique
    const appPath = path.join(BASE_PATH, 'src/app.ts');
    if (!fs.existsSync(appPath)) {
      this.errors.push('❌ Point d\'entrée app.ts manquant');
      return false;
    }

    // Vérifier que les modules sont chargés dans app.ts
    const appContent = fs.readFileSync(appPath, 'utf8');
    let modulesLoaded = 0;
    
    for (const module of EXPECTED_MODULES) {
      if (appContent.includes(`${module}Module`) || appContent.includes(`${module}/`)) {
        modulesLoaded++;
      }
    }

    if (modulesLoaded < EXPECTED_MODULES.length / 2) {
      this.warnings.push(`⚠️  Seulement ${modulesLoaded}/${EXPECTED_MODULES.length} modules chargés dans app.ts`);
    } else {
      this.success.push(`✅ ${modulesLoaded} modules chargés dans app.ts`);
    }

    return true;
  }

  // VÉRIFIER PACKAGE.JSON DES MODULES
  checkModulePackageJson() {
    console.log('\n📦 Vérification package.json des modules...\n');
    
    let modulesWithPackageJson = 0;
    
    for (const module of EXPECTED_MODULES) {
      const packageJsonPath = path.join(MODULES_PATH, module, 'package.json');
      if (fs.existsSync(packageJsonPath)) {
        modulesWithPackageJson++;
      }
    }

    if (modulesWithPackageJson === 0) {
      this.errors.push('❌ AUCUN module n\'a de package.json (règle NON NÉGOCIABLE violée)');
      return false;
    } else if (modulesWithPackageJson < EXPECTED_MODULES.length) {
      this.warnings.push(`⚠️  ${modulesWithPackageJson}/${EXPECTED_MODULES.length} modules ont un package.json`);
    } else {
      this.success.push('✅ Tous les modules ont un package.json');
    }

    return modulesWithPackageJson > 0;
  }

  // RAPPORT FINAL
  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 RAPPORT ARCHITECTURE MODULITH');
    console.log('='.repeat(60));

    if (this.success.length > 0) {
      console.log('\n✅ SUCCÈS:');
      this.success.forEach(s => console.log(`   ${s}`));
    }

    if (this.warnings.length > 0) {
      console.log('\n⚠️  AVERTISSEMENTS:');
      this.warnings.forEach(w => console.log(`   ${w}`));
    }

    if (this.errors.length > 0) {
      console.log('\n❌ ERREURS CRITIQUES:');
      this.errors.forEach(e => console.log(`   ${e}`));
    }

    const score = Math.max(0, 100 - (this.errors.length * 20) - (this.warnings.length * 5));
    console.log(`\n📈 SCORE ARCHITECTURE: ${score}%`);
    
    if (score < 50) {
      console.log('🔴 Architecture NON CONFORME - Action immédiate requise');
    } else if (score < 80) {
      console.log('🟡 Architecture PARTIELLEMENT conforme');
    } else {
      console.log('🟢 Architecture CONFORME');
    }

    console.log('\n' + '='.repeat(60));
  }

  // EXÉCUTION
  run() {
    console.log('🤖 GARDIEN ARCHITECTURE MODULITHE - ANALYSE EN COURS...\n');
    
    this.checkModulesExist();
    this.checkCircularDependencies();
    this.checkModulithArchitecture();
    this.checkModulePackageJson();
    
    this.generateReport();
    
    // Retourner le code de sortie approprié
    process.exit(this.errors.length > 0 ? 1 : 0);
  }
}

// EXÉCUTION
if (require.main === module) {
  const checker = new ArchitectureChecker();
  checker.run();
}

module.exports = ArchitectureChecker;
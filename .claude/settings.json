{"version": "1.0", "project": "mbnb-v2", "architecture": "modulith", "agents": [{"name": "Gardien Architecture Modulithe", "path": ".claude/agents/Gardien_Architecture_Modulithe.md", "priority": "critical", "status": "active", "completion": "75%"}, {"name": "Specialiste Modele Economique et Paiements", "path": ".claude/agents/Specialiste_Modele_Economique_et_Paiements.md", "priority": "critical", "status": "active", "completion": "25%"}, {"name": "Specialiste Integrations Tierces", "path": ".claude/agents/Specialiste_Integrations_Tierces.md", "priority": "high", "status": "active", "completion": "15%"}, {"name": "Specialiste Prevision et Tarification", "path": ".claude/agents/Specialiste_Prevision_et_Tarification.md", "priority": "medium", "status": "active", "completion": "10%"}, {"name": "Specialiste Branding Design UI", "path": ".claude/agents/Specialiste_Branding_Design_UI.md", "priority": "critical", "status": "active", "completion": "0%"}, {"name": "Specialiste Infrastructure Scalable", "path": ".claude/agents/Specialiste_Infrastructure_Scalable.md", "priority": "medium", "status": "active", "completion": "20%"}, {"name": "Specialiste Tests Performance Documentation", "path": ".claude/agents/Specialiste_Tests_Performance_Documentation.md", "priority": "critical", "status": "active", "completion": "10%"}, {"name": "Specialiste Documentation Technique", "path": ".claude/agents/Specialiste_Documentation_Technique.md", "priority": "medium", "status": "active", "completion": "0%"}, {"name": "Specialiste Experience Touristique Marocaine", "path": ".claude/agents/Specialiste_Experience_Touristique_Marocaine.md", "priority": "high", "status": "active", "completion": "5%"}, {"name": "Specialiste Conformite Legale Marocaine", "path": ".claude/agents/Specialiste_Conformite_Legale_Marocaine.md", "priority": "critical", "status": "active", "completion": "0%"}, {"name": "Specialiste Marketing Digital SEO", "path": ".claude/agents/Specialiste_Marketing_Digital_SEO.md", "priority": "low", "status": "active", "completion": "5%"}], "system_state": {"completion": "45%", "frontend_status": "build_fail_0%_functional", "backend_status": "65%_auth_complete_rest_basic", "database_status": "schema_100%_data_0%", "tests_status": "all_timeout_0%_pass", "ml_ai_status": "0%_mocks_only", "payment_gateways": "12_defined_0_tested", "production_ready": false}, "critical_facts": {"architecture": "MODULITH not microservices", "performance": "700-800 concurrent not 48K RPS", "activities": "TOP 100 not 150", "commission": "Split Fee model - Phase 3 only implemented", "qwen3": "Qwen3-235B-A22B-Thinking-2507 REAL MODEL confirmed", "playwright": "MCP implemented and available"}, "urgent_priorities": {"1_critical": "Fix frontend build", "2_critical": "Test CMI gateway", "3_critical": "Fix tests timeout", "4_high": "Seed database", "5_high": "Implement commission phases 1-2"}}
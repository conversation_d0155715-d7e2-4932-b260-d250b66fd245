{"permissions": {"allow": ["Read(///**)", "Bash(npx prisma validate:*)", "Bash(find:*)", "Bash(npx tsc:*)", "Bash(npx prisma migrate reset:*)", "Bash(npx prisma generate:*)", "Bash(PRISMA_USER_CONSENT_FOR_DANGEROUS_AI_ACTION=\"oui\" npx prisma migrate reset --force)", "Bash(npx prisma migrate dev:*)", "<PERSON><PERSON>(tee:*)", "Bash(psql:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(curl:*)", "Bash(node:*)", "Bash(npm run build:backend:*)", "Bash(for file in property.entity.ts booking.entity.ts payment.entity.ts)", "Bash(do echo \"=== $file ===\")", "Bash(grep:*)", "Bash(done)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(echo:*)", "Bash(npx prisma db pull:*)", "Bash(npx ts-node:*)", "Bash(npm install:*)", "Bash(for:*)", "Bash(do echo \"=== $file.gateway.fastify.ts ===\")", "Bash(npm run type-check:*)", "Bash(do echo \"=== $enum ===\")", "Bash(do grep -l \"enum $enum\" /Users/<USER>/Desktop/mbnb-v2/src/**/*.ts)", "Bash(PRISMA_MIGRATE_SKIP_GENERATE=true npx prisma migrate dev --name add-booking-extended-statuses --create-only)", "Bash(npx prisma db push:*)", "Bash(npm run test:*)", "Bash(npm run test:run:*)", "Bash(sort:*)", "Bash(xargs:*)", "Bash(do)", "Bash(if grep -q \"from ''@prisma/client''\" \"$file\")", "<PERSON><PERSON>(then)", "Bash(fi)", "Bash(do if ! grep -q \"implements ModuleInterface\" \"$module\")", "Bash(while read module)", "Bash(npm run:*)", "<PERSON>sh(redis-server:*)", "Bash(lsof:*)", "Bash(time npm run build:backend:*)"], "deny": [], "ask": []}}
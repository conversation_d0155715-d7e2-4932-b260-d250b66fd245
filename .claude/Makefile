# MAKEFILE SUBAGENTS MBNB V2
# Commandes simplifiées pour orchestration

.PHONY: help health run-all critical report install

# Couleurs pour output
RED=\033[0;31m
GREEN=\033[0;32m
YELLOW=\033[1;33m
NC=\033[0m # No Color

help:
	@echo "╔════════════════════════════════════════════════════════════╗"
	@echo "║           SYSTÈME SUBAGENTS MBNB V2                       ║"
	@echo "╚════════════════════════════════════════════════════════════╝"
	@echo ""
	@echo "Commandes disponibles:"
	@echo "  make install      - Installer dépendances"
	@echo "  make health       - Vérifier état système"
	@echo "  make critical     - Exécuter agents critiques"
	@echo "  make report       - Rapport complet"
	@echo "  make fix-frontend - Réparer build frontend"
	@echo "  make test-cmi     - Tester gateway CMI"
	@echo "  make seed-db      - Peupler base de données"
	@echo ""
	@echo "Subagents individuels:"
	@echo "  make check-arch   - Vérifier architecture"
	@echo "  make check-comm   - Vérifier commissions"
	@echo "  make check-tests  - Vérifier tests"
	@echo ""

install:
	@echo "$(GREEN)📦 Installation des dépendances...$(NC)"
	@cd agents && npm init -y 2>/dev/null || true
	@cd agents && npm install --save-dev madge vitest @vitest/ui autocannon k6

health:
	@echo "$(YELLOW)🔍 Vérification état système...$(NC)"
	@node agents/orchestrator.js health

critical:
	@echo "$(RED)🚨 Exécution agents critiques...$(NC)"
	@node agents/orchestrator.js critical

report:
	@echo "$(YELLOW)📊 Génération rapport complet...$(NC)"
	@node agents/orchestrator.js report

check-arch:
	@echo "$(GREEN)🏗️  Vérification architecture...$(NC)"
	@node agents/check_architecture.js

check-comm:
	@echo "$(GREEN)💰 Vérification commissions...$(NC)"
	@node agents/check_commission.js

check-tests:
	@echo "$(GREEN)🧪 Vérification tests...$(NC)"
	@node agents/check_tests.js

fix-frontend:
	@echo "$(RED)🔧 Tentative réparation frontend...$(NC)"
	@cd ../apps/frontend && npm install
	@cd ../apps/frontend && npx tsc --noEmit
	@cd ../apps/frontend && npm run build

test-cmi:
	@echo "$(RED)💳 Test gateway CMI...$(NC)"
	@cd .. && npm run test:gateway:cmi || echo "$(RED)❌ CMI non implémenté$(NC)"

seed-db:
	@echo "$(GREEN)🌱 Peuplement base de données...$(NC)"
	@cd .. && npx prisma db seed || echo "$(YELLOW)⚠️  Seed script manquant$(NC)"

# Commande cachée pour reset complet
reset-all:
	@echo "$(RED)⚠️  RESET COMPLET - DANGER!$(NC)"
	@read -p "Êtes-vous sûr? (y/N) " confirm && [ "$$confirm" = "y" ] || exit 1
	@rm -rf ../node_modules ../apps/frontend/node_modules
	@rm -rf ../.next ../apps/frontend/.next
	@npm cache clean --force
	@echo "$(GREEN)✅ Reset complet effectué$(NC)"
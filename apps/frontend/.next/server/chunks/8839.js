exports.id=8839,exports.ids=[8839],exports.modules={1010:(a,b,c)=>{Promise.resolve().then(c.bind(c,32353))},8282:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))})},29508:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(52661),e=c(7904),f=c(62188);let g=(0,d.vt)()((0,e.Zr)((0,f.D)((a,b)=>({user:null,token:null,refreshToken:null,isAuthenticated:!1,isLoading:!1,error:null,heritageInterests:[],activityPreferences:[],preferredLanguage:"fr",preferredCurrency:"MAD",loginToMbnb:async b=>{a(a=>{a.isLoading=!0,a.error=null});try{let c=await fetch("http://localhost:3001/api/v1/auth/login",{method:"POST",headers:{"Content-Type":"application/json","X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(b)});if(!c.ok){let a=await c.json();throw Error(a.message||"Authentification Mbnb \xe9chou\xe9e")}let d=await c.json();a(a=>{a.user=d.user,a.token=d.token,a.refreshToken=d.refreshToken||null,a.isAuthenticated=!0,a.isLoading=!1,a.preferredLanguage=d.user.profile.preferredLanguage,a.preferredCurrency=d.user.profile.preferredCurrency})}catch(c){let b=c instanceof Error?c.message:"Erreur de connexion Mbnb";throw a(a=>{a.error=b,a.isLoading=!1}),c}},registerToMbnb:async b=>{a(a=>{a.isLoading=!0,a.error=null});try{let c=await fetch("http://localhost:3001/api/v1/auth/register",{method:"POST",headers:{"Content-Type":"application/json","X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(b)});if(!c.ok){let a=await c.json();throw Error(a.message||"Inscription Mbnb \xe9chou\xe9e")}let d=await c.json();a(a=>{a.user=d.user,a.token=d.token,a.refreshToken=d.refreshToken||null,a.isAuthenticated=!0,a.isLoading=!1,a.preferredLanguage=d.user.profile.preferredLanguage,a.preferredCurrency=d.user.profile.preferredCurrency})}catch(c){let b=c instanceof Error?c.message:"Erreur d'inscription Mbnb";throw a(a=>{a.error=b,a.isLoading=!1}),c}},logoutFromMbnb:()=>{a(a=>{a.user=null,a.token=null,a.refreshToken=null,a.isAuthenticated=!1,a.error=null,a.heritageInterests=[],a.activityPreferences=[],a.preferredLanguage="fr",a.preferredCurrency="MAD"}),localStorage.removeItem("mbnb-auth-storage"),window.location.href="/"},refreshMbnbToken:async()=>{let c=b().refreshToken;if(!c)return void b().logoutFromMbnb();try{let b=await fetch("http://localhost:3001/api/v1/auth/refresh",{method:"POST",headers:{"Content-Type":"application/json","X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify({refreshToken:c})});if(!b.ok)throw Error("Refresh token Mbnb expir\xe9");let d=await b.json();a(a=>{a.token=d.token,a.refreshToken=d.refreshToken})}catch(a){b().logoutFromMbnb()}},updateMbnbProfile:async c=>{a(a=>{a.isLoading=!0,a.error=null});try{let d=await fetch("http://localhost:3001/api/v1/users/profile",{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${b().token}`,"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(c)});if(!d.ok){let a=await d.json();throw Error(a.message||"Mise \xe0 jour profil Mbnb \xe9chou\xe9e")}let e=await d.json();a(a=>{a.user=e,a.isLoading=!1,a.preferredLanguage=e.profile.preferredLanguage,a.preferredCurrency=e.profile.preferredCurrency})}catch(c){let b=c instanceof Error?c.message:"Erreur mise \xe0 jour profil Mbnb";throw a(a=>{a.error=b,a.isLoading=!1}),c}},updateMbnbPreferences:async c=>{a(a=>{a.isLoading=!0,a.error=null});try{let d=await fetch("http://localhost:3001/api/v1/users/preferences",{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${b().token}`,"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(c)});if(!d.ok){let a=await d.json();throw Error(a.message||"Mise \xe0 jour pr\xe9f\xe9rences Mbnb \xe9chou\xe9e")}a(a=>{c.heritageInterests&&(a.heritageInterests=c.heritageInterests),c.activityPreferences&&(a.activityPreferences=c.activityPreferences),c.preferredLanguage&&(a.preferredLanguage=c.preferredLanguage),c.preferredCurrency&&(a.preferredCurrency=c.preferredCurrency),a.isLoading=!1})}catch(c){let b=c instanceof Error?c.message:"Erreur mise \xe0 jour pr\xe9f\xe9rences Mbnb";throw a(a=>{a.error=b,a.isLoading=!1}),c}},verifyMbnbEmail:async c=>{let d=await fetch("http://localhost:3001/api/v1/auth/verify-email",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${b().token}`,"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify({code:c})});if(!d.ok)throw Error((await d.json()).message||"V\xe9rification email Mbnb \xe9chou\xe9e");a(a=>{a.user&&(a.user.verification.emailVerified=!0)})},verifyMbnbPhone:async c=>{let d=await fetch("http://localhost:3001/api/v1/auth/verify-phone",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${b().token}`,"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify({code:c})});if(!d.ok)throw Error((await d.json()).message||"V\xe9rification t\xe9l\xe9phone Mbnb \xe9chou\xe9e");a(a=>{a.user&&(a.user.verification.phoneVerified=!0)})},clearError:()=>a(a=>{a.error=null})})),{name:"mbnb-auth-storage",storage:(0,e.KU)(()=>localStorage),partialize:a=>({user:a.user,token:a.token,refreshToken:a.refreshToken,isAuthenticated:a.isAuthenticated,heritageInterests:a.heritageInterests,activityPreferences:a.activityPreferences,preferredLanguage:a.preferredLanguage,preferredCurrency:a.preferredCurrency})}))},32353:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>r});var d=c(78157);c(31768);var e=c(94496),f=c.n(e),g=c(71159),h=c(82314),i=c(38500),j=c(24971),k=c(65758),l=c(90025),m=c(51301),n=c(8282),o=c(1308),p=c(95259),q=c(29508);function r({children:a}){let b=(0,g.usePathname)(),{logoutFromMbnb:c}=(0,q.A)(),e=[{name:"Dashboard",href:"/traveler/dashboard",icon:h.A},{name:"Mes voyages",href:"/traveler/trips",icon:i.A},{name:"Favoris",href:"/traveler/favorites",icon:j.A},{name:"Messages",href:"/traveler/messages",icon:k.A},{name:"D\xe9couvrir",href:"/traveler/discover",icon:l.A},{name:"Avis",href:"/traveler/reviews",icon:m.A}],r=[{name:"Profil",href:"/traveler/profile",icon:n.A},{name:"Param\xe8tres",href:"/traveler/settings",icon:o.A}];return(0,d.jsxs)("div",{className:"flex h-screen bg-gray-50",children:[(0,d.jsx)("div",{className:"hidden md:flex md:flex-shrink-0",children:(0,d.jsx)("div",{className:"flex flex-col w-64",children:(0,d.jsxs)("div",{className:"flex flex-col flex-grow pt-5 pb-4 overflow-y-auto bg-white border-r border-gray-200",children:[(0,d.jsxs)("div",{className:"flex items-center flex-shrink-0 px-4",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-mbnb-coral",children:"Mbnb"}),(0,d.jsx)("span",{className:"ml-2 px-2 py-1 text-xs font-medium text-mbnb-teal bg-mbnb-teal bg-opacity-10 rounded-full",children:"Voyageur"})]}),(0,d.jsx)("nav",{className:"mt-8 flex-1 px-2 space-y-1",children:e.map(a=>{let c=b===a.href;return(0,d.jsxs)(f(),{href:a.href,className:`
                      group flex items-center px-2 py-2 text-sm font-medium rounded-md
                      ${c?"bg-mbnb-teal bg-opacity-10 text-mbnb-teal":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}
                    `,children:[(0,d.jsx)(a.icon,{className:`
                        mr-3 flex-shrink-0 h-6 w-6
                        ${c?"text-mbnb-teal":"text-gray-400 group-hover:text-gray-500"}
                      `}),a.name]},a.name)})}),(0,d.jsx)("div",{className:"mx-2 my-4 border-t border-gray-200"}),(0,d.jsxs)("nav",{className:"px-2 space-y-1",children:[r.map(a=>{let c=b===a.href;return(0,d.jsxs)(f(),{href:a.href,className:`
                      group flex items-center px-2 py-2 text-sm font-medium rounded-md
                      ${c?"bg-mbnb-teal bg-opacity-10 text-mbnb-teal":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}
                    `,children:[(0,d.jsx)(a.icon,{className:`
                        mr-3 flex-shrink-0 h-6 w-6
                        ${c?"text-mbnb-teal":"text-gray-400 group-hover:text-gray-500"}
                      `}),a.name]},a.name)}),(0,d.jsxs)("button",{onClick:c,className:"w-full group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900",children:[(0,d.jsx)(p.A,{className:"mr-3 flex-shrink-0 h-6 w-6 text-gray-400 group-hover:text-gray-500"}),"D\xe9connexion"]})]}),(0,d.jsxs)("div",{className:"mx-4 mt-4 p-3 bg-mbnb-teal rounded-lg",children:[(0,d.jsx)("p",{className:"text-xs font-medium text-white",children:"TOP 100 Activit\xe9s"}),(0,d.jsx)("p",{className:"text-xs text-white opacity-90 mt-1",children:"D\xe9couvrez le Maroc authentique"})]})]})})}),(0,d.jsx)("div",{className:"flex flex-col w-0 flex-1 overflow-hidden",children:(0,d.jsx)("main",{className:"flex-1 relative overflow-y-auto focus:outline-none",children:a})})]})}},37962:(a,b,c)=>{Promise.resolve().then(c.bind(c,41627))},38500:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"}))})},41627:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(25459).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/traveler/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/traveler/layout.tsx","default")},65758:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"}))})},71159:(a,b,c)=>{"use strict";var d=c(30291);c.o(d,"useParams")&&c.d(b,{useParams:function(){return d.useParams}}),c.o(d,"usePathname")&&c.d(b,{usePathname:function(){return d.usePathname}}),c.o(d,"useRouter")&&c.d(b,{useRouter:function(){return d.useRouter}}),c.o(d,"useSearchParams")&&c.d(b,{useSearchParams:function(){return d.useSearchParams}})},95259:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15M12 9l-3 3m0 0 3 3m-3-3h12.75"}))})}};
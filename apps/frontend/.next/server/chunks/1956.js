"use strict";exports.id=1956,exports.ids=[1956],exports.modules={21956:(a,b,c)=>{c.d(b,{sQ:()=>G,Q5:()=>H,$M:()=>y,rB:()=>t,GZ:()=>I,oE:()=>q,As:()=>V,Un:()=>n,pv:()=>Q,$$:()=>B,J7:()=>r,TC:()=>J,iD:()=>v,cO:()=>w,AV:()=>x,gG:()=>z,Zm:()=>N,E6:()=>M,AB:()=>F,qD:()=>E,Ul:()=>L,aL:()=>A,q0:()=>R,Tb:()=>K,_:()=>U,AW:()=>m,wc:()=>o,vq:()=>O,t$:()=>X,PV:()=>P,Pi:()=>T,pl:()=>S,Pe:()=>p,cL:()=>W,Cu:()=>u,cD:()=>s,m_:()=>C,Am:()=>D});var d=c(14380),e=c(32315),f=c(86539);class g extends Error{constructor(a,b,c,d){super(b),this.code=a,this.status=c,this.details=d,this.name="MbnbApiError"}}class h{constructor(){this.token=null,this.config={baseURL:"http://localhost:3001/api/v1",timeout:3e4,retries:3,headers:{"Content-Type":"application/json","X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0.0","X-Mbnb-Client":"frontend-next"}}}async request(a,b,c,d){let e=`${this.config.baseURL}${b}`,f={...this.config.headers,...this.token&&{Authorization:`Bearer ${this.token}`},...d?.headers||{}},h=null;for(let b=0;b<this.config.retries;b++)try{let b=new AbortController,h=setTimeout(()=>b.abort(),this.config.timeout),i=await fetch(e,{method:a,headers:f,body:c?JSON.stringify(c):void 0,signal:b.signal,...d});if(clearTimeout(h),!i.ok){let a=await i.json().catch(()=>({}));throw new g(a.code||"API_ERROR",a.message||`Request failed with status ${i.status}`,i.status,a)}return await i.json()}catch(a){if(h=a,a instanceof g&&a.status&&a.status>=400&&a.status<500)throw a;b<this.config.retries-1&&await new Promise(a=>setTimeout(a,1e3*Math.pow(2,b)))}throw h||new g("NETWORK_ERROR","Network request failed")}setAuthToken(a){this.token=a}async get(a){return this.request("GET",a)}async getRegionDetails(a){return this.get(`/regions/${a}`)}async login(a){let b=await this.request("POST","/auth/login",a);return b.token&&this.setAuthToken(b.token),b}async register(a){let b=await this.request("POST","/auth/register",a);return b.token&&this.setAuthToken(b.token),b}async logout(){await this.request("POST","/auth/logout"),this.setAuthToken(null)}async refreshToken(){return await this.request("POST","/auth/refresh")}async getCurrentUser(){return await this.request("GET","/users/me")}async updateProfile(a){return await this.request("PATCH","/users/me",a)}async searchMoroccanProperties(a){let b=new URLSearchParams;return a.region&&b.append("region",a.region),a.city&&b.append("city",a.city),a.checkIn&&b.append("checkIn",a.checkIn.toString()),a.checkOut&&b.append("checkOut",a.checkOut.toString()),a.guests&&b.append("guests",a.guests.toString()),a.priceMin&&b.append("priceMin",a.priceMin.toString()),a.priceMax&&b.append("priceMax",a.priceMax.toString()),a.propertyTypes?.length&&b.append("propertyTypes",a.propertyTypes.join(",")),a.heritageTypes?.length&&b.append("heritageTypes",a.heritageTypes.join(",")),a.amenities?.length&&b.append("amenities",a.amenities.join(",")),a.activities?.length&&b.append("activities",a.activities.join(",")),void 0!==a.instantBook&&b.append("instantBook",a.instantBook.toString()),void 0!==a.superhost&&b.append("superhost",a.superhost.toString()),a.sortBy&&b.append("sortBy",a.sortBy),a.page&&b.append("page",a.page.toString()),a.pageSize&&b.append("pageSize",a.pageSize.toString()),await this.request("GET",`/properties/search?${b.toString()}`)}async getFeaturedProperties(){return await this.request("GET","/properties/featured")}async getMoroccanProperty(a){return await this.request("GET",`/properties/${a}`)}async getHostProperties(a){return await this.request("GET",`/hosts/${a}/properties`)}async createProperty(a){return await this.request("POST","/properties",a)}async updateProperty(a,b){return await this.request("PATCH",`/properties/${a}`,b)}async deleteProperty(a){return await this.request("DELETE",`/properties/${a}`)}async createMbnbReservation(a){return await this.request("POST","/bookings",a)}async getReservation(a){return await this.request("GET",`/bookings/${a}`)}async getUserReservations(a){let b=a?`/users/${a}/bookings`:"/users/me/bookings";return await this.request("GET",b)}async getHostReservations(a,b){let c=new URLSearchParams;b&&c.append("status",b);let d=a?`/hosts/${a}/reservations${c.toString()?`?${c.toString()}`:""}`:`/hosts/me/bookings${c.toString()?`?${c.toString()}`:""}`;return await this.request("GET",d)}async cancelReservation(a,b){return await this.request("POST",`/bookings/${a}/cancel`,{reason:b})}async modifyReservation(a,b){return await this.request("PATCH",`/bookings/${a}`,b)}async checkAvailability(a,b,c){let d=new URLSearchParams({checkIn:b.toString(),checkOut:c.toString()});return await this.request("GET",`/properties/${a}/availability?${d.toString()}`)}async calculatePricing(a,b,c,d,e){return await this.request("POST","/pricing/calculate",{propertyId:a,checkIn:b,checkOut:c,guests:d,activities:e})}async getAvailabilityCalendar(a,b,c){let d=new URLSearchParams({startDate:b.toString(),endDate:c.toString()});return await this.request("GET",`/properties/${a}/calendar?${d.toString()}`)}async updateAvailability(a,b){return await this.request("PUT",`/properties/${a}/availability`,b)}async getHostAnalytics(a,b){let c=new URLSearchParams;b&&c.append("period",b);let d=a?`/hosts/${a}/analytics`:"/hosts/me/analytics";return await this.request("GET",`${d}${c.toString()?`?${c.toString()}`:""}`)}async getTourismInsights(a){return await this.request("GET",`/analytics/tourism/${a}`)}async getRegionalMetrics(){return await this.request("GET","/analytics/regions")}async getPropertyPerformance(a,b){let c=new URLSearchParams;return b&&c.append("period",b),await this.request("GET",`/properties/${a}/performance${c.toString()?`?${c.toString()}`:""}`)}async processCMIPayment(a){return await this.request("POST","/payments/cmi",a)}async processWafaCashTransfer(a){return await this.request("POST","/payments/wafacash",a)}async processCashPlusPayment(a){return await this.request("POST","/payments/cashplus",a)}async getPaymentHistory(a){let b=a?`/users/${a}/payments`:"/users/me/payments";return await this.request("GET",b)}async getHostEarnings(a,b){let c=new URLSearchParams;b&&c.append("period",b);let d=a?`/hosts/${a}/earnings`:"/hosts/me/earnings";return await this.request("GET",`${d}${c.toString()?`?${c.toString()}`:""}`)}async getHostEarningsChart(a,b){let c=new URLSearchParams;c.append("timeRange",b);let d=`/hosts/${a}/earnings/chart?${c.toString()}`;return await this.request("GET",d)}async getHostPayouts(a){let b=`/hosts/${a}/payouts`;return await this.request("GET",b)}async exportHostEarnings(a){let b=new URLSearchParams;b.append("format",a.format),a.period&&(b.append("start",a.period.start),b.append("end",a.period.end)),void 0!==a.includeDetails&&b.append("includeDetails",a.includeDetails.toString());let c=`/hosts/${a.hostId}/earnings/export?${b.toString()}`;return await this.request("GET",c)}async getHostProfile(a){let b=`/hosts/${a}/profile`;return await this.request("GET",b)}async getHostVerificationStatus(a){let b=`/hosts/${a}/verification`;return await this.request("GET",b)}async getHostAchievements(a){let b=`/hosts/${a}/achievements`;return await this.request("GET",b)}async updateHostProfile(a){let b=`/hosts/${a.hostId}/profile`,c={name:a.name,bio:a.bio,phone:a.phone,location:a.location,languages:a.languages};return await this.request("PUT",b,c)}async updateHostPreferences(a){let b=`/hosts/${a.hostId}/preferences`,c={preferences:a.preferences,notifications:a.notifications};return await this.request("PUT",b,c)}async uploadHostAvatar(a){let b=`/hosts/${a.hostId}/avatar`,c=new FormData;return c.append("avatar",a.file),await this.request("POST",b,c,{headers:{}})}async getHostReservationStats(a){let b=`/hosts/${a}/reservations/stats`;return await this.request("GET",b)}async acceptReservation(a){let b=`/reservations/${a.reservationId}/accept`,c={checkinInstructions:a.checkinInstructions,specialNotes:a.specialNotes,message:a.message};return await this.request("POST",b,c)}async declineReservation(a){let b=`/reservations/${a.reservationId}/decline`,c={declineReason:a.declineReason,reason:a.reason,message:a.message,alternativeDates:a.alternativeDates};await this.request("POST",b,c)}async searchActivities(a,b,c){let d=new URLSearchParams;return a&&d.append("region",a),b&&d.append("category",b),c&&(d.append("priceMin",c[0].toString()),d.append("priceMax",c[1].toString())),await this.request("GET",`/activities/search?${d.toString()}`)}async getTop100Activities(){return await this.request("GET","/activities/top100")}async bookActivity(a,b){return await this.request("POST",`/activities/${a}/book`,b)}async getPropertyReviews(a){return await this.request("GET",`/properties/${a}/reviews`)}async createReview(a){return await this.request("POST","/reviews",a)}async respondToReview(a,b){return await this.request("POST",`/reviews/${a}/response`,{response:b})}async getMessages(a){let b=a?`/messages/conversation/${a}`:"/messages";return await this.request("GET",b)}async sendMessage(a){return await this.request("POST","/messages",a)}async getNotifications(a){let b=new URLSearchParams;return a&&b.append("unreadOnly","true"),await this.request("GET",`/notifications${b.toString()?`?${b.toString()}`:""}`)}async markNotificationAsRead(a){return await this.request("PATCH",`/notifications/${a}/read`)}async getFavorites(){return await this.request("GET","/users/me/favorites")}async addToFavorites(a){return await this.request("POST",`/users/me/favorites/${a}`)}async removeFromFavorites(a){return await this.request("DELETE",`/users/me/favorites/${a}`)}async calculateCommission(a,b){return await this.request("POST","/commission/calculate",{bookingAmount:a,activities:b})}async getCommissionHistory(a){let b=a?`/hosts/${a}/commissions`:"/hosts/me/commissions";return await this.request("GET",b)}}let i=new h;var j=c(82339);let k="http://localhost:3001",l=()=>({Authorization:"Bearer ","Content-Type":"application/json"});function m(a,b){return(0,d.I)({queryKey:["mbnb-properties",a],queryFn:()=>i.searchMoroccanProperties(a),enabled:!!(a.region||a.city),staleTime:3e5,...b})}function n(a){return(0,d.I)({queryKey:["mbnb-featured-properties"],queryFn:()=>i.getFeaturedProperties(),staleTime:18e5,retry:2,...a})}function o(a,b){return(0,d.I)({queryKey:["mbnb-property",a],queryFn:()=>i.getMoroccanProperty(a),enabled:!!a,staleTime:3e5,...b})}function p(a,b){return(0,d.I)({queryKey:["mbnb-top-activities",a],queryFn:()=>i.getTop100Activities(),staleTime:9e5,retry:2,...b})}function q(a,b){return(0,d.I)({queryKey:["mbnb-activity",a],queryFn:async()=>(await fetch(`${k}/api/v1/activities/${a}`)).json(),staleTime:6e5,retry:2,enabled:!!a,...b})}function r(a,b,c){return(0,d.I)({queryKey:["mbnb-host-analytics",a,b],queryFn:()=>i.getHostAnalytics(a,b),staleTime:3e4,refetchInterval:3e4,...c})}function s(a,b){return(0,d.I)({queryKey:["mbnb-tourism-insights",a],queryFn:()=>i.getTourismInsights(a),enabled:!!a,staleTime:3e4,refetchInterval:3e4,...b})}function t(a){return(0,d.I)({queryKey:["mbnb-favorites"],queryFn:()=>i.getFavorites(),staleTime:3e5,...a})}function u(a){let b=(0,e.jE)();return(0,f.n)({mutationFn:({propertyId:a,isFavorite:b})=>b?i.removeFromFavorites(a):i.addToFavorites(a),onSuccess:(a,{isFavorite:c})=>{b.invalidateQueries({queryKey:["mbnb-favorites"]}),j.oR.success(c?"Retir\xe9 des favoris":"Ajout\xe9 aux favoris")},...a})}function v(a,b){return(0,d.I)({queryKey:["mbnb-host-earnings",a],queryFn:()=>i.getHostEarnings(String(a)),enabled:!!a,staleTime:3e5,retry:2,...b})}function w(a,b,c){return(0,d.I)({queryKey:["mbnb-host-earnings-chart",a,b],queryFn:()=>i.getHostEarningsChart(String(a),b),enabled:!!a,staleTime:6e5,retry:2,...c})}function x(a,b){return(0,d.I)({queryKey:["mbnb-host-payouts",a],queryFn:()=>i.getHostPayouts(String(a)),enabled:!!a,staleTime:3e5,retry:2,...b})}function y(a){return(0,f.n)({mutationFn:a=>i.exportHostEarnings(a),onSuccess:(a,b)=>{let c=window.URL.createObjectURL(a),d=document.createElement("a");d.style.display="none",d.href=c,d.download=`mbnb-earnings-${b.hostId}.${b.format}`,document.body.appendChild(d),d.click(),window.URL.revokeObjectURL(c),document.body.removeChild(d),j.oR.success("Rapport export\xe9 avec succ\xe8s")},onError:a=>{console.error("Erreur export revenus:",a),j.oR.error("Erreur lors de l'export")},...a})}function z(a,b){return(0,d.I)({queryKey:["mbnb-host-profile",a],queryFn:()=>i.getHostProfile(String(a)),enabled:!!a,staleTime:6e5,retry:2,...b})}function A(a,b){return(0,d.I)({queryKey:["mbnb-host-verification",a],queryFn:()=>i.getHostVerificationStatus(String(a)),enabled:!!a,staleTime:9e5,retry:2,...b})}function B(a,b){return(0,d.I)({queryKey:["mbnb-host-achievements",a],queryFn:()=>i.getHostAchievements(String(a)),enabled:!!a,staleTime:12e5,retry:2,...b})}function C(a){let b=(0,e.jE)();return(0,f.n)({mutationFn:a=>i.updateHostProfile(a),onSuccess:(a,c)=>{b.setQueryData(["mbnb-host-profile",c.hostId],a),b.invalidateQueries({queryKey:["mbnb-host"]}),j.oR.success("Profil mis \xe0 jour avec succ\xe8s")},onError:a=>{console.error("Erreur mise \xe0 jour profil:",a),j.oR.error("Erreur lors de la mise \xe0 jour du profil")},...a})}function D(a){let b=(0,e.jE)();return(0,f.n)({mutationFn:a=>i.uploadHostAvatar(a),onSuccess:(a,c)=>{b.setQueryData(["mbnb-host-profile",c.hostId],b=>b?{...b,avatar:a.avatarUrl}:b),j.oR.success("Photo de profil mise \xe0 jour")},onError:a=>{console.error("Erreur upload avatar:",a),j.oR.error("Erreur lors de la mise \xe0 jour de la photo")},...a})}function E(a,b,c){return(0,d.I)({queryKey:["mbnb-host-reservations",a,b],queryFn:()=>i.getHostReservations(String(a),b),enabled:!!a,staleTime:3e5,retry:2,...c})}function F(a,b){return(0,d.I)({queryKey:["mbnb-host-reservation-stats",a],queryFn:()=>i.getHostReservationStats(String(a)),enabled:!!a,staleTime:6e5,retry:2,...b})}function G(a){let b=(0,e.jE)();return(0,f.n)({mutationFn:a=>i.acceptReservation(a),onSuccess:()=>{b.invalidateQueries({queryKey:["mbnb-host-reservations"]}),b.invalidateQueries({queryKey:["mbnb-host-reservation-stats"]}),j.oR.success("R\xe9servation accept\xe9e avec succ\xe8s")},onError:a=>{console.error("Erreur acceptation r\xe9servation:",a),j.oR.error("Erreur lors de l'acceptation de la r\xe9servation")},...a})}function H(a){let b=(0,e.jE)();return(0,f.n)({mutationFn:a=>i.declineReservation(a),onSuccess:()=>{b.invalidateQueries({queryKey:["mbnb-host-reservations"]}),b.invalidateQueries({queryKey:["mbnb-host-reservation-stats"]}),j.oR.success("R\xe9servation refus\xe9e")},onError:a=>{console.error("Erreur refus r\xe9servation:",a),j.oR.error("Erreur lors du refus de la r\xe9servation")},...a})}function I(){let a=(0,e.jE)();return{invalidateAll:()=>a.invalidateQueries(),invalidateProperties:()=>a.invalidateQueries({queryKey:["mbnb-properties"]}),invalidateUser:()=>a.invalidateQueries({queryKey:["mbnb-current-user"]}),invalidateAnalytics:()=>a.invalidateQueries({queryKey:["mbnb-host-analytics"]})}}function J(a){return(0,d.I)({queryKey:["mbnb-host-dashboard"],queryFn:async()=>{let[a,b,c,d,e]=await Promise.all([i.getHostAnalytics("current"),i.getHostProperties("current"),i.getHostReservations(),i.getHostEarnings("current"),i.getHostProfile("current")]);return{overview:a,properties:b,reservations:c,earnings:d,profile:e}},staleTime:6e4,refetchInterval:3e5,...a})}function K(a){return(0,d.I)({queryKey:["mbnb-notifications"],queryFn:()=>i.getNotifications(),staleTime:3e4,refetchInterval:6e4,...a})}function L(a){return(0,d.I)({queryKey:["mbnb-host-stats"],queryFn:()=>i.getHostAnalytics(""),staleTime:3e5,refetchInterval:3e4,...a})}function M(a){return(0,d.I)({queryKey:["mbnb-host-recent-bookings"],queryFn:()=>i.getHostReservations(""),staleTime:6e4,refetchInterval:12e4,...a})}function N(a){return(0,d.I)({queryKey:["mbnb-host-properties-all"],queryFn:()=>i.getHostProperties("current"),staleTime:3e5,...a})}function O(a,b){return(0,d.I)({queryKey:["mbnb-property-reviews",a],queryFn:()=>i.getPropertyReviews(a),enabled:!!a,staleTime:6e5,...b})}function P(a,b){return(0,d.I)({queryKey:["mbnb-similar-properties",a],queryFn:()=>Promise.resolve([]),enabled:!!a,staleTime:18e5,...b})}function Q(a){return(0,d.I)({queryKey:["mbnb-heritage-properties"],queryFn:async()=>{let a=await fetch(`${k}/api/v1/properties/heritage`,{headers:l()});if(!a.ok)throw Error("Erreur lors du chargement des propri\xe9t\xe9s patrimoine");return a.json()},staleTime:9e5,...a})}function R(a){return(0,d.I)({queryKey:["mbnb-instant-book-properties"],queryFn:async()=>{let a=await fetch(`${k}/api/v1/properties/instant-book`,{headers:l()});if(!a.ok)throw Error("Erreur lors du chargement des propri\xe9t\xe9s Instant Book");return a.json()},staleTime:6e5,...a})}function S(a){return(0,d.I)({queryKey:["mbnb-testimonials"],queryFn:async()=>{let a=await fetch(`${k}/api/v1/reviews/testimonials`,{headers:l()});if(!a.ok)throw Error("Erreur lors du chargement des t\xe9moignages");return a.json()},staleTime:36e5,...a})}function T(a){return(0,d.I)({queryKey:["mbnb-system-metrics"],queryFn:async()=>{let a=await fetch(`${k}/api/v1/admin/monitoring/system`,{headers:l()});if(!a.ok)throw Error("Erreur lors du chargement des m\xe9triques syst\xe8me");return a.json()},staleTime:15e3,refetchInterval:3e4,retry:2,...a})}function U(a){return(0,d.I)({queryKey:["mbnb-performance-metrics"],queryFn:async()=>{let a=await fetch(`${k}/api/v1/admin/monitoring/performance`,{headers:l()});if(!a.ok)throw Error("Erreur lors du chargement des m\xe9triques de performance");return a.json()},staleTime:3e4,refetchInterval:6e4,retry:2,...a})}function V(a){return(0,d.I)({queryKey:["mbnb-business-metrics"],queryFn:async()=>{let a=await fetch(`${k}/api/v1/admin/monitoring/business`,{headers:l()});if(!a.ok)throw Error("Erreur lors du chargement des m\xe9triques business");return a.json()},staleTime:3e5,refetchInterval:6e5,retry:2,...a})}function W(a,b){return(0,d.I)({queryKey:["mbnb-traveler-bookings",a],queryFn:async()=>{if(!a)throw Error("ID du voyageur requis");let b=await fetch(`${k}/api/v1/travelers/${a}/bookings`,{headers:l()});if(!b.ok)throw Error("Erreur lors du chargement des r\xe9servations");return b.json()},enabled:!!a,staleTime:12e4,refetchInterval:3e5,retry:2,...b})}function X(a,b){return(0,d.I)({queryKey:["mbnb-recommended-properties",a],queryFn:async()=>{if(!a)throw Error("ID du voyageur requis");let b=await fetch(`${k}/api/v1/travelers/${a}/recommendations`,{headers:l()});if(!b.ok)throw Error("Erreur lors du chargement des recommandations");return b.json()},enabled:!!a,staleTime:6e5,refetchInterval:18e5,retry:2,...b})}},86539:(a,b,c)=>{c.d(b,{n:()=>k});var d=c(31768),e=c(15916),f=c(36795),g=c(83690),h=c(8306),i=class extends g.Q{#a;#b=void 0;#c;#d;constructor(a,b){super(),this.#a=a,this.setOptions(b),this.bindMethods(),this.#e()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(a){let b=this.options;this.options=this.#a.defaultMutationOptions(a),(0,h.f8)(this.options,b)||this.#a.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#c,observer:this}),b?.mutationKey&&this.options.mutationKey&&(0,h.EN)(b.mutationKey)!==(0,h.EN)(this.options.mutationKey)?this.reset():this.#c?.state.status==="pending"&&this.#c.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#c?.removeObserver(this)}onMutationUpdate(a){this.#e(),this.#f(a)}getCurrentResult(){return this.#b}reset(){this.#c?.removeObserver(this),this.#c=void 0,this.#e(),this.#f()}mutate(a,b){return this.#d=b,this.#c?.removeObserver(this),this.#c=this.#a.getMutationCache().build(this.#a,this.options),this.#c.addObserver(this),this.#c.execute(a)}#e(){let a=this.#c?.state??(0,e.$)();this.#b={...a,isPending:"pending"===a.status,isSuccess:"success"===a.status,isError:"error"===a.status,isIdle:"idle"===a.status,mutate:this.mutate,reset:this.reset}}#f(a){f.jG.batch(()=>{if(this.#d&&this.hasListeners()){let b=this.#b.variables,c=this.#b.context;a?.type==="success"?(this.#d.onSuccess?.(a.data,b,c),this.#d.onSettled?.(a.data,null,b,c)):a?.type==="error"&&(this.#d.onError?.(a.error,b,c),this.#d.onSettled?.(void 0,a.error,b,c))}this.listeners.forEach(a=>{a(this.#b)})})}},j=c(32315);function k(a,b){let c=(0,j.jE)(b),[e]=d.useState(()=>new i(c,a));d.useEffect(()=>{e.setOptions(a)},[e,a]);let g=d.useSyncExternalStore(d.useCallback(a=>e.subscribe(f.jG.batchCalls(a)),[e]),()=>e.getCurrentResult(),()=>e.getCurrentResult()),k=d.useCallback((a,b)=>{e.mutate(a,b).catch(h.lQ)},[e]);if(g.error&&(0,h.GU)(e.options.throwOnError,[g.error]))throw g.error;return{...g,mutate:k,mutateAsync:g.mutate}}}};
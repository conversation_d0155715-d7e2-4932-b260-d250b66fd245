"use strict";exports.id=3743,exports.ids=[3743],exports.modules={618:(a,b,c)=>{var d=c(31768),e="function"==typeof Object.is?Object.is:function(a,b){return a===b&&(0!==a||1/a==1/b)||a!=a&&b!=b},f=d.useState,g=d.useEffect,h=d.useLayoutEffect,i=d.useDebugValue;function j(a){var b=a.getSnapshot;a=a.value;try{var c=b();return!e(a,c)}catch(a){return!0}}var k="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(a,b){return b()}:function(a,b){var c=b(),d=f({inst:{value:c,getSnapshot:b}}),e=d[0].inst,k=d[1];return h(function(){e.value=c,e.getSnapshot=b,j(e)&&k({inst:e})},[a,c,b]),g(function(){return j(e)&&k({inst:e}),a(function(){j(e)&&k({inst:e})})},[a]),i(c),c};b.useSyncExternalStore=void 0!==d.useSyncExternalStore?d.useSyncExternalStore:k},7904:(a,b,c)=>{function d(a,b){let c;try{c=a()}catch(a){return}return{getItem:a=>{var d;let e=a=>null===a?null:JSON.parse(a,null==b?void 0:b.reviver),f=null!=(d=c.getItem(a))?d:null;return f instanceof Promise?f.then(e):e(f)},setItem:(a,d)=>c.setItem(a,JSON.stringify(d,null==b?void 0:b.replacer)),removeItem:a=>c.removeItem(a)}}c.d(b,{KU:()=>d,Zr:()=>f});let e=a=>b=>{try{let c=a(b);if(c instanceof Promise)return c;return{then:a=>e(a)(c),catch(a){return this}}}catch(a){return{then(a){return this},catch:b=>e(b)(a)}}},f=(a,b)=>"getStorage"in b||"serialize"in b||"deserialize"in b?(console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),((a,b)=>(c,d,f)=>{let g,h,i={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:a=>a,version:0,merge:(a,b)=>({...b,...a}),...b},j=!1,k=new Set,l=new Set;try{g=i.getStorage()}catch(a){}if(!g)return a((...a)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),c(...a)},d,f);let m=e(i.serialize),n=()=>{let a,b=m({state:i.partialize({...d()}),version:i.version}).then(a=>g.setItem(i.name,a)).catch(b=>{a=b});if(a)throw a;return b},o=f.setState;f.setState=(a,b)=>{o(a,b),n()};let p=a((...a)=>{c(...a),n()},d,f),q=()=>{var a;if(!g)return;j=!1,k.forEach(a=>a(d()));let b=(null==(a=i.onRehydrateStorage)?void 0:a.call(i,d()))||void 0;return e(g.getItem.bind(g))(i.name).then(a=>{if(a)return i.deserialize(a)}).then(a=>{if(a)if("number"!=typeof a.version||a.version===i.version)return a.state;else{if(i.migrate)return i.migrate(a.state,a.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}}).then(a=>{var b;return c(h=i.merge(a,null!=(b=d())?b:p),!0),n()}).then(()=>{null==b||b(h,void 0),j=!0,l.forEach(a=>a(h))}).catch(a=>{null==b||b(void 0,a)})};return f.persist={setOptions:a=>{i={...i,...a},a.getStorage&&(g=a.getStorage())},clearStorage:()=>{null==g||g.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>q(),hasHydrated:()=>j,onHydrate:a=>(k.add(a),()=>{k.delete(a)}),onFinishHydration:a=>(l.add(a),()=>{l.delete(a)})},q(),h||p})(a,b)):((a,b)=>(c,f,g)=>{let h,i={storage:d(()=>localStorage),partialize:a=>a,version:0,merge:(a,b)=>({...b,...a}),...b},j=!1,k=new Set,l=new Set,m=i.storage;if(!m)return a((...a)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),c(...a)},f,g);let n=()=>{let a=i.partialize({...f()});return m.setItem(i.name,{state:a,version:i.version})},o=g.setState;g.setState=(a,b)=>{o(a,b),n()};let p=a((...a)=>{c(...a),n()},f,g);g.getInitialState=()=>p;let q=()=>{var a,b;if(!m)return;j=!1,k.forEach(a=>{var b;return a(null!=(b=f())?b:p)});let d=(null==(b=i.onRehydrateStorage)?void 0:b.call(i,null!=(a=f())?a:p))||void 0;return e(m.getItem.bind(m))(i.name).then(a=>{if(a)if("number"!=typeof a.version||a.version===i.version)return[!1,a.state];else{if(i.migrate)return[!0,i.migrate(a.state,a.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(a=>{var b;let[d,e]=a;if(c(h=i.merge(e,null!=(b=f())?b:p),!0),d)return n()}).then(()=>{null==d||d(h,void 0),h=f(),j=!0,l.forEach(a=>a(h))}).catch(a=>{null==d||d(void 0,a)})};return g.persist={setOptions:a=>{i={...i,...a},a.storage&&(m=a.storage)},clearStorage:()=>{null==m||m.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>q(),hasHydrated:()=>j,onHydrate:a=>(k.add(a),()=>{k.delete(a)}),onFinishHydration:a=>(l.add(a),()=>{l.delete(a)})},i.skipHydration||q(),h||p})(a,b)},15947:(a,b,c)=>{a.exports=c(37345)},37345:(a,b,c)=>{var d=c(31768),e=c(56473),f="function"==typeof Object.is?Object.is:function(a,b){return a===b&&(0!==a||1/a==1/b)||a!=a&&b!=b},g=e.useSyncExternalStore,h=d.useRef,i=d.useEffect,j=d.useMemo,k=d.useDebugValue;b.useSyncExternalStoreWithSelector=function(a,b,c,d,e){var l=h(null);if(null===l.current){var m={hasValue:!1,value:null};l.current=m}else m=l.current;var n=g(a,(l=j(function(){function a(a){if(!i){if(i=!0,g=a,a=d(a),void 0!==e&&m.hasValue){var b=m.value;if(e(b,a))return h=b}return h=a}if(b=h,f(g,a))return b;var c=d(a);return void 0!==e&&e(b,c)?(g=a,b):(g=a,h=c)}var g,h,i=!1,j=void 0===c?null:c;return[function(){return a(b())},null===j?void 0:function(){return a(j())}]},[b,c,d,e]))[0],l[1]);return i(function(){m.hasValue=!0,m.value=n},[n]),k(n),n}},52661:(a,b,c)=>{c.d(b,{vt:()=>k});let d=a=>{let b,c=new Set,d=(a,d)=>{let e="function"==typeof a?a(b):a;if(!Object.is(e,b)){let a=b;b=(null!=d?d:"object"!=typeof e||null===e)?e:Object.assign({},b,e),c.forEach(c=>c(b,a))}},e=()=>b,f={setState:d,getState:e,getInitialState:()=>g,subscribe:a=>(c.add(a),()=>c.delete(a)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),c.clear()}},g=b=a(d,e,f);return f};var e=c(31768),f=c(15947);let{useDebugValue:g}=e,{useSyncExternalStoreWithSelector:h}=f,i=!1,j=a=>{"function"!=typeof a&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");let b="function"==typeof a?(a=>a?d(a):d)(a):a,c=(a,c)=>(function(a,b=a=>a,c){c&&!i&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),i=!0);let d=h(a.subscribe,a.getState,a.getServerState||a.getInitialState,b,c);return g(d),d})(b,a,c);return Object.assign(c,b),c},k=a=>a?j(a):j},56473:(a,b,c)=>{a.exports=c(618)},62188:(a,b,c)=>{c.d(b,{D:()=>R});var d,e=Symbol.for("immer-nothing"),f=Symbol.for("immer-draftable"),g=Symbol.for("immer-state");function h(a){throw Error(`[Immer] minified error nr: ${a}. Full error at: https://bit.ly/3cXEKWf`)}var i=Object.getPrototypeOf;function j(a){return!!a&&!!a[g]}function k(a){return!!a&&(m(a)||Array.isArray(a)||!!a[f]||!!a.constructor?.[f]||r(a)||s(a))}var l=Object.prototype.constructor.toString();function m(a){if(!a||"object"!=typeof a)return!1;let b=i(a);if(null===b)return!0;let c=Object.hasOwnProperty.call(b,"constructor")&&b.constructor;return c===Object||"function"==typeof c&&Function.toString.call(c)===l}function n(a,b){0===o(a)?Reflect.ownKeys(a).forEach(c=>{b(c,a[c],a)}):a.forEach((c,d)=>b(d,c,a))}function o(a){let b=a[g];return b?b.type_:Array.isArray(a)?1:r(a)?2:3*!!s(a)}function p(a,b){return 2===o(a)?a.has(b):Object.prototype.hasOwnProperty.call(a,b)}function q(a,b,c){let d=o(a);2===d?a.set(b,c):3===d?a.add(c):a[b]=c}function r(a){return a instanceof Map}function s(a){return a instanceof Set}function t(a){return a.copy_||a.base_}function u(a,b){if(r(a))return new Map(a);if(s(a))return new Set(a);if(Array.isArray(a))return Array.prototype.slice.call(a);let c=m(a);if(!0!==b&&("class_only"!==b||c)){let b=i(a);return null!==b&&c?{...a}:Object.assign(Object.create(b),a)}{let b=Object.getOwnPropertyDescriptors(a);delete b[g];let c=Reflect.ownKeys(b);for(let d=0;d<c.length;d++){let e=c[d],f=b[e];!1===f.writable&&(f.writable=!0,f.configurable=!0),(f.get||f.set)&&(b[e]={configurable:!0,writable:!0,enumerable:f.enumerable,value:a[e]})}return Object.create(i(a),b)}}function v(a,b=!1){return x(a)||j(a)||!k(a)||(o(a)>1&&Object.defineProperties(a,{set:{value:w},add:{value:w},clear:{value:w},delete:{value:w}}),Object.freeze(a),b&&Object.values(a).forEach(a=>v(a,!0))),a}function w(){h(2)}function x(a){return Object.isFrozen(a)}var y={};function z(a){let b=y[a];return b||h(0,a),b}function A(a,b){b&&(z("Patches"),a.patches_=[],a.inversePatches_=[],a.patchListener_=b)}function B(a){C(a),a.drafts_.forEach(E),a.drafts_=null}function C(a){a===d&&(d=a.parent_)}function D(a){return d={drafts_:[],parent_:d,immer_:a,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function E(a){let b=a[g];0===b.type_||1===b.type_?b.revoke_():b.revoked_=!0}function F(a,b){b.unfinalizedDrafts_=b.drafts_.length;let c=b.drafts_[0];return void 0!==a&&a!==c?(c[g].modified_&&(B(b),h(4)),k(a)&&(a=G(b,a),b.parent_||I(b,a)),b.patches_&&z("Patches").generateReplacementPatches_(c[g].base_,a,b.patches_,b.inversePatches_)):a=G(b,c,[]),B(b),b.patches_&&b.patchListener_(b.patches_,b.inversePatches_),a!==e?a:void 0}function G(a,b,c){if(x(b))return b;let d=b[g];if(!d)return n(b,(e,f)=>H(a,d,b,e,f,c)),b;if(d.scope_!==a)return b;if(!d.modified_)return I(a,d.base_,!0),d.base_;if(!d.finalized_){d.finalized_=!0,d.scope_.unfinalizedDrafts_--;let b=d.copy_,e=b,f=!1;3===d.type_&&(e=new Set(b),b.clear(),f=!0),n(e,(e,g)=>H(a,d,b,e,g,c,f)),I(a,b,!1),c&&a.patches_&&z("Patches").generatePatches_(d,c,a.patches_,a.inversePatches_)}return d.copy_}function H(a,b,c,d,e,f,g){if(j(e)){let g=G(a,e,f&&b&&3!==b.type_&&!p(b.assigned_,d)?f.concat(d):void 0);if(q(c,d,g),!j(g))return;a.canAutoFreeze_=!1}else g&&c.add(e);if(k(e)&&!x(e)){if(!a.immer_.autoFreeze_&&a.unfinalizedDrafts_<1)return;G(a,e),(!b||!b.scope_.parent_)&&"symbol"!=typeof d&&(r(c)?c.has(d):Object.prototype.propertyIsEnumerable.call(c,d))&&I(a,e)}}function I(a,b,c=!1){!a.parent_&&a.immer_.autoFreeze_&&a.canAutoFreeze_&&v(b,c)}var J={get(a,b){if(b===g)return a;let c=t(a);if(!p(c,b)){var d=a,e=c,f=b;let g=M(e,f);return g?"value"in g?g.value:g.get?.call(d.draft_):void 0}let h=c[b];return a.finalized_||!k(h)?h:h===L(a.base_,b)?(O(a),a.copy_[b]=P(h,a)):h},has:(a,b)=>b in t(a),ownKeys:a=>Reflect.ownKeys(t(a)),set(a,b,c){let d=M(t(a),b);if(d?.set)return d.set.call(a.draft_,c),!0;if(!a.modified_){let d=L(t(a),b),e=d?.[g];if(e&&e.base_===c)return a.copy_[b]=c,a.assigned_[b]=!1,!0;if((c===d?0!==c||1/c==1/d:c!=c&&d!=d)&&(void 0!==c||p(a.base_,b)))return!0;O(a),N(a)}return!!(a.copy_[b]===c&&(void 0!==c||b in a.copy_)||Number.isNaN(c)&&Number.isNaN(a.copy_[b]))||(a.copy_[b]=c,a.assigned_[b]=!0,!0)},deleteProperty:(a,b)=>(void 0!==L(a.base_,b)||b in a.base_?(a.assigned_[b]=!1,O(a),N(a)):delete a.assigned_[b],a.copy_&&delete a.copy_[b],!0),getOwnPropertyDescriptor(a,b){let c=t(a),d=Reflect.getOwnPropertyDescriptor(c,b);return d?{writable:!0,configurable:1!==a.type_||"length"!==b,enumerable:d.enumerable,value:c[b]}:d},defineProperty(){h(11)},getPrototypeOf:a=>i(a.base_),setPrototypeOf(){h(12)}},K={};function L(a,b){let c=a[g];return(c?t(c):a)[b]}function M(a,b){if(!(b in a))return;let c=i(a);for(;c;){let a=Object.getOwnPropertyDescriptor(c,b);if(a)return a;c=i(c)}}function N(a){!a.modified_&&(a.modified_=!0,a.parent_&&N(a.parent_))}function O(a){a.copy_||(a.copy_=u(a.base_,a.scope_.immer_.useStrictShallowCopy_))}function P(a,b){let c=r(a)?z("MapSet").proxyMap_(a,b):s(a)?z("MapSet").proxySet_(a,b):function(a,b){let c=Array.isArray(a),e={type_:+!!c,scope_:b?b.scope_:d,modified_:!1,finalized_:!1,assigned_:{},parent_:b,base_:a,draft_:null,copy_:null,revoke_:null,isManual_:!1},f=e,g=J;c&&(f=[e],g=K);let{revoke:h,proxy:i}=Proxy.revocable(f,g);return e.draft_=i,e.revoke_=h,i}(a,b);return(b?b.scope_:d).drafts_.push(c),c}n(J,(a,b)=>{K[a]=function(){return arguments[0]=arguments[0][0],b.apply(this,arguments)}}),K.deleteProperty=function(a,b){return K.set.call(this,a,b,void 0)},K.set=function(a,b,c){return J.set.call(this,a[0],b,c,a[0])};var Q=new class{constructor(a){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(a,b,c)=>{let d;if("function"==typeof a&&"function"!=typeof b){let c=b;b=a;let d=this;return function(a=c,...e){return d.produce(a,a=>b.call(this,a,...e))}}if("function"!=typeof b&&h(6),void 0!==c&&"function"!=typeof c&&h(7),k(a)){let e=D(this),f=P(a,void 0),g=!0;try{d=b(f),g=!1}finally{g?B(e):C(e)}return A(e,c),F(d,e)}if(a&&"object"==typeof a)h(1,a);else{if(void 0===(d=b(a))&&(d=a),d===e&&(d=void 0),this.autoFreeze_&&v(d,!0),c){let b=[],e=[];z("Patches").generateReplacementPatches_(a,d,b,e),c(b,e)}return d}},this.produceWithPatches=(a,b)=>{let c,d;return"function"==typeof a?(b,...c)=>this.produceWithPatches(b,b=>a(b,...c)):[this.produce(a,b,(a,b)=>{c=a,d=b}),c,d]},"boolean"==typeof a?.autoFreeze&&this.setAutoFreeze(a.autoFreeze),"boolean"==typeof a?.useStrictShallowCopy&&this.setUseStrictShallowCopy(a.useStrictShallowCopy)}createDraft(a){var b;k(a)||h(8),j(a)&&(j(b=a)||h(10,b),a=function a(b){let c;if(!k(b)||x(b))return b;let d=b[g];if(d){if(!d.modified_)return d.base_;d.finalized_=!0,c=u(b,d.scope_.immer_.useStrictShallowCopy_)}else c=u(b,!0);return n(c,(b,d)=>{q(c,b,a(d))}),d&&(d.finalized_=!1),c}(b));let c=D(this),d=P(a,void 0);return d[g].isManual_=!0,C(c),d}finishDraft(a,b){let c=a&&a[g];c&&c.isManual_||h(9);let{scope_:d}=c;return A(d,b),F(void 0,d)}setAutoFreeze(a){this.autoFreeze_=a}setUseStrictShallowCopy(a){this.useStrictShallowCopy_=a}applyPatches(a,b){let c;for(c=b.length-1;c>=0;c--){let d=b[c];if(0===d.path.length&&"replace"===d.op){a=d.value;break}}c>-1&&(b=b.slice(c+1));let d=z("Patches").applyPatches_;return j(a)?d(a,b):this.produce(a,a=>d(a,b))}}().produce;let R=a=>(b,c,d)=>(d.setState=(a,c,...d)=>b("function"==typeof a?Q(a):a,c,...d),a(d.setState,c,d))}};
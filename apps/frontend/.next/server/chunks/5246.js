"use strict";exports.id=5246,exports.ids=[5246],exports.modules={29508:(a,b,c)=>{c.d(b,{A:()=>g});var d=c(52661),e=c(7904),f=c(62188);let g=(0,d.vt)()((0,e.Zr)((0,f.D)((a,b)=>({user:null,token:null,refreshToken:null,isAuthenticated:!1,isLoading:!1,error:null,heritageInterests:[],activityPreferences:[],preferredLanguage:"fr",preferredCurrency:"MAD",loginToMbnb:async b=>{a(a=>{a.isLoading=!0,a.error=null});try{let c=await fetch("http://localhost:3001/api/v1/auth/login",{method:"POST",headers:{"Content-Type":"application/json","X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(b)});if(!c.ok){let a=await c.json();throw Error(a.message||"Authentification Mbnb \xe9chou\xe9e")}let d=await c.json();a(a=>{a.user=d.user,a.token=d.token,a.refreshToken=d.refreshToken||null,a.isAuthenticated=!0,a.isLoading=!1,a.preferredLanguage=d.user.profile.preferredLanguage,a.preferredCurrency=d.user.profile.preferredCurrency})}catch(c){let b=c instanceof Error?c.message:"Erreur de connexion Mbnb";throw a(a=>{a.error=b,a.isLoading=!1}),c}},registerToMbnb:async b=>{a(a=>{a.isLoading=!0,a.error=null});try{let c=await fetch("http://localhost:3001/api/v1/auth/register",{method:"POST",headers:{"Content-Type":"application/json","X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(b)});if(!c.ok){let a=await c.json();throw Error(a.message||"Inscription Mbnb \xe9chou\xe9e")}let d=await c.json();a(a=>{a.user=d.user,a.token=d.token,a.refreshToken=d.refreshToken||null,a.isAuthenticated=!0,a.isLoading=!1,a.preferredLanguage=d.user.profile.preferredLanguage,a.preferredCurrency=d.user.profile.preferredCurrency})}catch(c){let b=c instanceof Error?c.message:"Erreur d'inscription Mbnb";throw a(a=>{a.error=b,a.isLoading=!1}),c}},logoutFromMbnb:()=>{a(a=>{a.user=null,a.token=null,a.refreshToken=null,a.isAuthenticated=!1,a.error=null,a.heritageInterests=[],a.activityPreferences=[],a.preferredLanguage="fr",a.preferredCurrency="MAD"}),localStorage.removeItem("mbnb-auth-storage"),window.location.href="/"},refreshMbnbToken:async()=>{let c=b().refreshToken;if(!c)return void b().logoutFromMbnb();try{let b=await fetch("http://localhost:3001/api/v1/auth/refresh",{method:"POST",headers:{"Content-Type":"application/json","X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify({refreshToken:c})});if(!b.ok)throw Error("Refresh token Mbnb expir\xe9");let d=await b.json();a(a=>{a.token=d.token,a.refreshToken=d.refreshToken})}catch(a){b().logoutFromMbnb()}},updateMbnbProfile:async c=>{a(a=>{a.isLoading=!0,a.error=null});try{let d=await fetch("http://localhost:3001/api/v1/users/profile",{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${b().token}`,"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(c)});if(!d.ok){let a=await d.json();throw Error(a.message||"Mise \xe0 jour profil Mbnb \xe9chou\xe9e")}let e=await d.json();a(a=>{a.user=e,a.isLoading=!1,a.preferredLanguage=e.profile.preferredLanguage,a.preferredCurrency=e.profile.preferredCurrency})}catch(c){let b=c instanceof Error?c.message:"Erreur mise \xe0 jour profil Mbnb";throw a(a=>{a.error=b,a.isLoading=!1}),c}},updateMbnbPreferences:async c=>{a(a=>{a.isLoading=!0,a.error=null});try{let d=await fetch("http://localhost:3001/api/v1/users/preferences",{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${b().token}`,"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(c)});if(!d.ok){let a=await d.json();throw Error(a.message||"Mise \xe0 jour pr\xe9f\xe9rences Mbnb \xe9chou\xe9e")}a(a=>{c.heritageInterests&&(a.heritageInterests=c.heritageInterests),c.activityPreferences&&(a.activityPreferences=c.activityPreferences),c.preferredLanguage&&(a.preferredLanguage=c.preferredLanguage),c.preferredCurrency&&(a.preferredCurrency=c.preferredCurrency),a.isLoading=!1})}catch(c){let b=c instanceof Error?c.message:"Erreur mise \xe0 jour pr\xe9f\xe9rences Mbnb";throw a(a=>{a.error=b,a.isLoading=!1}),c}},verifyMbnbEmail:async c=>{let d=await fetch("http://localhost:3001/api/v1/auth/verify-email",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${b().token}`,"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify({code:c})});if(!d.ok)throw Error((await d.json()).message||"V\xe9rification email Mbnb \xe9chou\xe9e");a(a=>{a.user&&(a.user.verification.emailVerified=!0)})},verifyMbnbPhone:async c=>{let d=await fetch("http://localhost:3001/api/v1/auth/verify-phone",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${b().token}`,"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify({code:c})});if(!d.ok)throw Error((await d.json()).message||"V\xe9rification t\xe9l\xe9phone Mbnb \xe9chou\xe9e");a(a=>{a.user&&(a.user.verification.phoneVerified=!0)})},clearError:()=>a(a=>{a.error=null})})),{name:"mbnb-auth-storage",storage:(0,e.KU)(()=>localStorage),partialize:a=>({user:a.user,token:a.token,refreshToken:a.refreshToken,isAuthenticated:a.isAuthenticated,heritageInterests:a.heritageInterests,activityPreferences:a.activityPreferences,preferredLanguage:a.preferredLanguage,preferredCurrency:a.preferredCurrency})}))},71159:(a,b,c)=>{var d=c(30291);c.o(d,"useParams")&&c.d(b,{useParams:function(){return d.useParams}}),c.o(d,"usePathname")&&c.d(b,{usePathname:function(){return d.usePathname}}),c.o(d,"useRouter")&&c.d(b,{useRouter:function(){return d.useRouter}}),c.o(d,"useSearchParams")&&c.d(b,{useSearchParams:function(){return d.useSearchParams}})},75704:(a,b,c)=>{c.d(b,{Yq:()=>h,cn:()=>g});var d=c(79390),e=c(25442),f=c(14196);function g(...a){return(0,e.QP)((0,d.$)(a))}function h(a,b="short"){let c="string"==typeof a?(0,f.Io)(a):a;return"short"===b?new Intl.DateTimeFormat("fr-MA",{day:"numeric",month:"short",year:"numeric"}).format(c):new Intl.DateTimeFormat("fr-MA",{weekday:"long",day:"numeric",month:"long",year:"numeric"}).format(c)}},97807:(a,b,c)=>{c.d(b,{A:()=>i});var d=c(78157),e=c(31768),f=c.n(e),g=c(75704);let h=f().forwardRef(({className:a,variant:b="primary",size:c="md",fullWidth:e=!1,loading:f=!1,icon:h,iconPosition:i="left",disabled:j,children:k,...l},m)=>(0,d.jsxs)("button",{ref:m,className:(0,g.cn)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-mbnb-coral focus:ring-offset-2","disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-mbnb-coral hover:bg-mbnb-coral-dark text-white shadow-sm",secondary:"bg-neutral-100 hover:bg-neutral-200 text-gray-dark",outline:"border-2 border-mbnb-coral text-mbnb-coral hover:bg-mbnb-coral hover:text-white",ghost:"text-gray-dark hover:bg-gray-100",danger:"bg-red-600 hover:bg-red-700 text-white",success:"bg-mbnb-teal-dark hover:bg-mbnb-teal-dark text-white"}[b],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg",xl:"px-8 py-4 text-xl"}[c],e&&"w-full",f&&"cursor-wait",a),disabled:j||f,...l,children:[f?(0,d.jsxs)("svg",{className:"animate-spin h-5 w-5 mr-2",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,d.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,d.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"})]}):h&&"left"===i?(0,d.jsx)("span",{className:"mr-2",children:h}):null,k,!f&&h&&"right"===i&&(0,d.jsx)("span",{className:"ml-2",children:h})]}));h.displayName="Button";let i=h},98839:(a,b,c)=>{c.d(b,{A:()=>i});var d=c(78157),e=c(31768),f=c.n(e),g=c(75704);let h=f().forwardRef(({className:a,type:b="text",label:c,error:e,helper:f,icon:h,iconPosition:i="left",fullWidth:j=!1,variant:k="default",disabled:l,...m},n)=>{let o=m.id||`input-${Math.random().toString(36).substr(2,9)}`;return(0,d.jsxs)("div",{className:(0,g.cn)("relative",j&&"w-full",a),children:[c&&(0,d.jsx)("label",{htmlFor:o,className:"block text-sm font-medium text-gray-dark mb-1.5",children:c}),(0,d.jsxs)("div",{className:"relative",children:[h&&"left"===i&&(0,d.jsx)("div",{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-dark",children:h}),(0,d.jsx)("input",{ref:n,id:o,type:b,className:(0,g.cn)("w-full px-4 py-2.5 rounded-lg border transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-mbnb-coral focus:ring-opacity-20","disabled:bg-gray-100 disabled:cursor-not-allowed disabled:opacity-60","placeholder:text-gray-medium",{default:"border-gray-300 focus:border-mbnb-coral bg-white",filled:"border-transparent bg-neutral-50 focus:bg-white focus:border-mbnb-coral",outlined:"border-2 border-gray-medium focus:border-mbnb-coral bg-transparent"}[k],h&&"left"===i&&"pl-10",h&&"right"===i&&"pr-10",e&&"border-red-500 focus:border-red-500 focus:ring-red-500",a),disabled:l,"aria-invalid":!!e,"aria-describedby":e?`${o}-error`:f?`${o}-helper`:void 0,...m}),h&&"right"===i&&(0,d.jsx)("div",{className:"absolute right-3 top-1/2 -translate-y-1/2 text-gray-dark",children:h})]}),e&&(0,d.jsx)("p",{id:`${o}-error`,className:"mt-1.5 text-sm text-red-600",children:e}),f&&!e&&(0,d.jsx)("p",{id:`${o}-helper`,className:"mt-1.5 text-sm text-gray-medium",children:f})]})});h.displayName="Input";let i=h}};
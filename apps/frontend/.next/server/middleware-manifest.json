{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$|.*\\.ico$).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$|.*\\.ico$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "zGL_M_ILxIICrvP-h80z7", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "u2BffCNuYwd+Xz6BD2LbbTV8iMpPMi42nmMUKXWsrSY=", "__NEXT_PREVIEW_MODE_ID": "37092b0cdf0ac2eab2235f7d30c2278f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "595d96130774101159c07d724420552f55143b25911b43cbd551f6928d813732", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8a59295fcb41f636c1636f5f0a19f61c1ae609d376317d7cdad0cdff26139563"}}}, "functions": {}, "sortedMiddleware": ["/"]}
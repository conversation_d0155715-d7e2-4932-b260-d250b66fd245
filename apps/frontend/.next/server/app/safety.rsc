1:"$Sreact.fragment"
2:I[31201,["9249","static/chunks/8bb4d8db-dfbedecd1ce1b10b.js","9664","static/chunks/9664-dcc323304d96ebba.js","6799","static/chunks/6799-5700cd15871c3a68.js","3558","static/chunks/3558-7762df6d0b7818d5.js","7638","static/chunks/7638-a456914beab47d3a.js","8509","static/chunks/8509-cd16161b0a738228.js","1470","static/chunks/1470-0ec85a2f49d662b6.js","7177","static/chunks/app/layout-15771846853b158e.js"],"Providers"]
3:I[41470,["9249","static/chunks/8bb4d8db-dfbedecd1ce1b10b.js","9664","static/chunks/9664-dcc323304d96ebba.js","6799","static/chunks/6799-5700cd15871c3a68.js","3558","static/chunks/3558-7762df6d0b7818d5.js","7638","static/chunks/7638-a456914beab47d3a.js","8509","static/chunks/8509-cd16161b0a738228.js","1470","static/chunks/1470-0ec85a2f49d662b6.js","7177","static/chunks/app/layout-15771846853b158e.js"],"default"]
4:I[85341,[],""]
5:I[81869,["8039","static/chunks/app/error-9c03368aa6cf1490.js"],"default"]
6:I[90025,[],""]
7:I[19664,["9664","static/chunks/9664-dcc323304d96ebba.js","9437","static/chunks/app/safety/page-a243b30d8237f2b7.js"],""]
10:I[56065,["4219","static/chunks/app/global-error-74438679bdbb3441.js"],"default"]
:HL["/_next/static/media/5aae3a1c1074c5e1-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/media/8c2fd50d66d22a18-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/media/904be59b21bd51cb-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/media/da6e5417d357d163-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/media/dd5f2241e050216b-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/media/eaead17c7dbfcd5d-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/css/c963da8451813b8e.css","style"]
:HL["/_next/static/css/52e7035135458db8.css","style"]
0:{"P":null,"b":"zGL_M_ILxIICrvP-h80z7","p":"","c":["","safety"],"i":false,"f":[[["",{"children":["safety",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/c963da8451813b8e.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/52e7035135458db8.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"fr","dir":"ltr","className":"__variable_660b3b __variable_d59ba8 __variable_338cf8","suppressHydrationWarning":true,"children":[["$","head",null,{"children":[["$","link",null,{"rel":"preconnect","href":"https://fonts.googleapis.com"}],["$","link",null,{"rel":"preconnect","href":"https://fonts.gstatic.com","crossOrigin":"anonymous"}],["$","link",null,{"rel":"preconnect","href":"https://cdn.mbnb.ma"}],["$","link",null,{"rel":"preconnect","href":"https://api.mbnb.ma"}],["$","link",null,{"rel":"dns-prefetch","href":"https://www.google-analytics.com"}],["$","link",null,{"rel":"dns-prefetch","href":"https://www.googletagmanager.com"}],["$","script",null,{"type":"application/ld+json","dangerouslySetInnerHTML":{"__html":"{\"@context\":\"https://schema.org\",\"@type\":\"Organization\",\"name\":\"Mbnb\",\"url\":\"https://www.mbnb.ma\",\"logo\":\"https://cdn.mbnb.ma/logo.png\",\"sameAs\":[\"https://www.facebook.com/mbnb.ma\",\"https://www.instagram.com/mbnb.ma\",\"https://twitter.com/mbnb_ma\",\"https://www.linkedin.com/company/mbnb-ma\"],\"contactPoint\":{\"@type\":\"ContactPoint\",\"telephone\":\"+212-5XX-XXXXXX\",\"contactType\":\"customer service\",\"availableLanguage\":[\"French\",\"Arabic\",\"English\",\"Spanish\"],\"areaServed\":\"MA\"}}"}}]]}],["$","body",null,{"className":"min-h-screen bg-white font-sans antialiased","children":["$","$L2",null,{"children":[["$","div",null,{"className":"flex flex-col min-h-screen","children":[["$","$L3",null,{}],["$","main",null,{"className":"flex-grow","children":["$","$L4",null,{"parallelRouterKey":"children","error":"$5","errorStyles":[],"errorScripts":[],"template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","div",null,{"className":"min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 flex items-center justify-center px-4","children":["$","div",null,{"className":"max-w-2xl w-full text-center","children":[["$","div",null,{"className":"relative mb-8","children":[["$","h1",null,{"className":"text-[150px] md:text-[200px] font-bold text-gray-200 select-none","children":"404"}],["$","div",null,{"className":"absolute inset-0 flex items-center justify-center","children":["$","div",null,{"className":"bg-white rounded-2xl shadow-xl p-8","children":[["$","p",null,{"className":"text-6xl mb-4","children":"🏜️"}],["$","p",null,{"className":"text-xl font-semibold text-gray-800","children":"Page Introuvable"}]]}]}]]}],["$","h2",null,{"className":"text-2xl md:text-3xl font-bold text-gray-900 mb-4","children":"Oops! Cette page s'est perdue dans le Sahara"}],["$","p",null,{"className":"text-lg text-gray-600 mb-8 max-w-lg mx-auto","children":"La page que vous recherchez n'existe pas ou a été déplacée. Peut-être qu'un chameau l'a emportée dans le désert..."}],["$","div",null,{"className":"bg-white rounded-2xl shadow-lg p-6 mb-8 max-w-lg mx-auto","children":[["$","p",null,{"className":"text-sm font-semibold text-gray-700 mb-3","children":"Suggestions populaires :"}],["$","div",null,{"className":"flex flex-wrap gap-2 justify-center","children":[["$","$L7",null,{"href":"/search?location=Marrakech","className":"px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-medium text-gray-700 transition-colors","children":"Riads à Marrakech"}],["$","$L7",null,{"href":"/search?location=Essaouira","className":"px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-medium text-gray-700 transition-colors","children":"Maisons à Essaouira"}],["$","$L7",null,{"href":"/search?location=Chefchaouen","className":"px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-medium text-gray-700 transition-colors","children":"Hébergements à Chefchaouen"}]]}]]}],["$","div",null,{"className":"flex gap-4 justify-center","children":["$L8","$L9"]}]]}]}],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],"$La"]}],"$Lb","$Lc"]}]}]]}]]}],{"children":["safety","$Ld",{"children":["__PAGE__","$Le",{},null,false]},null,false]},null,false],"$Lf",false]],"m":"$undefined","G":["$10",[]],"s":false,"S":true}
16:I[93558,["9249","static/chunks/8bb4d8db-dfbedecd1ce1b10b.js","9664","static/chunks/9664-dcc323304d96ebba.js","6799","static/chunks/6799-5700cd15871c3a68.js","3558","static/chunks/3558-7762df6d0b7818d5.js","7638","static/chunks/7638-a456914beab47d3a.js","8509","static/chunks/8509-cd16161b0a738228.js","1470","static/chunks/1470-0ec85a2f49d662b6.js","7177","static/chunks/app/layout-15771846853b158e.js"],"Toaster"]
17:I[92576,["9249","static/chunks/8bb4d8db-dfbedecd1ce1b10b.js","9664","static/chunks/9664-dcc323304d96ebba.js","6799","static/chunks/6799-5700cd15871c3a68.js","3558","static/chunks/3558-7762df6d0b7818d5.js","7638","static/chunks/7638-a456914beab47d3a.js","8509","static/chunks/8509-cd16161b0a738228.js","1470","static/chunks/1470-0ec85a2f49d662b6.js","7177","static/chunks/app/layout-15771846853b158e.js"],"Analytics"]
25:I[15104,[],"ViewportBoundary"]
27:I[15104,[],"MetadataBoundary"]
28:"$Sreact.suspense"
8:["$","$L7",null,{"href":"/","className":"inline-flex items-center gap-2 px-6 py-3 bg-white hover:bg-gray-50 text-gray-700 rounded-lg font-medium transition-all transform hover:scale-105 shadow-lg","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-5 h-5","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"}]]}],"Retour à l'accueil"]}]
9:["$","$L7",null,{"href":"/search","className":"inline-flex items-center gap-2 px-6 py-3 bg-mbnb-coral hover:bg-mbnb-coral-dark text-white rounded-lg font-medium transition-all transform hover:scale-105 shadow-lg","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-5 h-5","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}]]}],"Explorer les hébergements"]}]
a:["$","footer",null,{"className":"bg-gray-900 text-gray-300","children":[["$","div",null,{"className":"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8","children":["$","div",null,{"className":"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6","children":[["$","div",null,{"children":[["$","h3",null,{"className":"text-white font-semibold mb-3","children":"À propos"}],["$","ul",null,{"className":"space-y-1.5 text-sm","children":[["$","li",null,{"children":["$","$L7",null,{"href":"/about","className":"hover:text-mbnb-coral transition-colors","children":"Qui sommes-nous"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/how-it-works","className":"hover:text-mbnb-coral transition-colors","children":"Comment ça marche"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/newsroom","className":"hover:text-mbnb-coral transition-colors","children":"Actualités"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/investors","className":"hover:text-mbnb-coral transition-colors","children":"Investisseurs"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/careers","className":"hover:text-mbnb-coral transition-colors","children":"Carrières"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/sustainability","className":"hover:text-mbnb-coral transition-colors","children":"Durabilité"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/diversity","className":"hover:text-mbnb-coral transition-colors","children":"Diversité"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/gift-cards","className":"hover:text-mbnb-coral transition-colors","children":"Cartes cadeaux"}]}]]}]]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-white font-semibold mb-3","children":"Support"}],["$","ul",null,{"className":"space-y-1.5 text-sm","children":[["$","li",null,{"children":["$","$L7",null,{"href":"/help","className":"hover:text-mbnb-coral transition-colors","children":"Centre d'aide"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/contact","className":"hover:text-mbnb-coral transition-colors","children":"Nous contacter"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/safety","className":"hover:text-mbnb-coral transition-colors","children":"Sécurité"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/cancellation","className":"hover:text-mbnb-coral transition-colors","children":"Annulation"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/trust-safety","className":"hover:text-mbnb-coral transition-colors","children":"Confiance"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/accessibility","className":"hover:text-mbnb-coral transition-colors","children":"Accessibilité"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/associates","className":"hover:text-mbnb-coral transition-colors","children":"Partenaires"}]}]]}]]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-white font-semibold mb-3","children":"Hôtes"}],["$","ul",null,{"className":"space-y-1.5 text-sm","children":[["$","li",null,{"children":["$","$L7",null,{"href":"/host/homes","className":"hover:text-mbnb-coral transition-colors","children":"Héberger"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/host/experiences","className":"hover:text-mbnb-coral transition-colors","children":"Expériences"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/host/resources","className":"hover:text-mbnb-coral transition-colors","children":"Ressources"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/host/responsible","className":"hover:text-mbnb-coral transition-colors","children":"Responsable"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/host/insurance","className":"hover:text-mbnb-coral transition-colors","children":"Assurance"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/host/academy","className":"hover:text-mbnb-coral transition-colors","children":"Académie"}]}]]}]]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-white font-semibold mb-3","children":"Destinations"}],["$","ul",null,{"className":"space-y-1.5 text-sm","children":[["$","li",null,{"children":["$","$L7",null,{"href":"/destinations/marrakech","className":"hover:text-mbnb-coral transition-colors","children":"Marrakech"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/destinations/casablanca","className":"hover:text-mbnb-coral transition-colors","children":"Casablanca"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/destinations/fes","className":"hover:text-mbnb-coral transition-colors","children":"Fès"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/destinations/chefchaouen","className":"hover:text-mbnb-coral transition-colors","children":"Chefchaouen"}]}],"$L11","$L12","$L13"]}]]}],"$L14"]}]}],"$L15"]}]
b:["$","$L16",null,{"position":"top-right","toastOptions":{"duration":4000,"style":{"background":"#363636","color":"#fff","borderRadius":"8px"},"success":{"iconTheme":{"primary":"#22C55E","secondary":"#fff"}},"error":{"iconTheme":{"primary":"#EF4444","secondary":"#fff"}}}}]
c:["$","$L17",null,{}]
d:["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}]
e:["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen bg-neutral-50","children":[["$","section",null,{"className":"bg-mbnb-navy text-white py-16","children":["$","div",null,{"className":"container mx-auto px-4","children":["$","div",null,{"className":"text-center max-w-4xl mx-auto","children":[["$","h1",null,{"className":"text-4xl md:text-5xl font-bold mb-6","children":"Sécurité et confiance"}],["$","p",null,{"className":"text-xl md:text-2xl mb-8 text-white/90","children":"Votre sécurité est notre priorité absolue pour des voyages sereins au Maroc"}],["$","div",null,{"className":"flex flex-wrap justify-center gap-4","children":[["$","span",null,{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border bg-gray-100 text-gray-dark border-gray-200 rounded-md text-sm px-4 py-2","children":["$undefined","1M+ séjours sécurisés",false]}],["$","span",null,{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border bg-transparent rounded-md text-sm px-4 py-2 border-white text-white","children":["$undefined","Support 24h/24",false]}],["$","span",null,{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border bg-transparent rounded-md text-sm px-4 py-2 border-white text-white","children":["$undefined","Assurance incluse",false]}]]}]]}]}]}],["$","section",null,{"className":"py-12","children":["$","div",null,{"className":"container mx-auto px-4","children":["$","div",null,{"className":"max-w-4xl mx-auto","children":["$","div",null,{"ref":"$undefined","className":"rounded-xl overflow-hidden transition-all duration-300 bg-white border border-gray-200 p-5 mb-8","children":["$","div",null,{"ref":"$undefined","className":"p-8","children":[["$","h2",null,{"className":"text-2xl font-bold text-neutral-900 mb-4","children":"Notre engagement sécurité"}],["$","div",null,{"className":"prose prose-neutral max-w-none","children":[["$","p",null,{"className":"text-lg text-neutral-700 mb-4","children":["Chez ",["$","strong",null,{"children":"Mbnb"}],", la sécurité de nos voyageurs et Hosts est notre priorité absolue. Nous avons développé un écosystème de confiance robuste avec des protocoles rigoureux, une technologie avancée et une équipe dédiée disponible 24h/24."]}],["$","p",null,{"className":"text-neutral-600 mb-4","children":"Chaque réservation bénéficie automatiquement de nos protections complètes : assurance voyage intégrée, vérification d'identité systématique, contrôle qualité des logements et support d'urgence multilingue."}],["$","p",null,{"className":"text-neutral-600","children":"Notre approche préventive et nos standards élevés nous permettent de maintenir un taux de satisfaction sécurité de 99% parmi nos utilisateurs."}]]}]]}]}]}]}]}],["$","section",null,{"className":"py-12 bg-white","children":["$","div",null,{"className":"container mx-auto px-4","children":["$","div",null,{"className":"max-w-6xl mx-auto","children":[["$","h2",null,{"className":"text-3xl font-bold text-center mb-12 text-neutral-900","children":"Nos dispositifs de sécurité"}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 gap-8","children":[["$","div","0",{"ref":"$undefined","className":"rounded-xl overflow-hidden duration-300 bg-white border border-gray-200 p-5 hover:shadow-lg transition-shadow","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5","children":["$","div",null,{"className":"flex items-center space-x-3","children":[["$","div",null,{"className":"p-3 bg-primary-100 rounded-lg","children":["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"h-6 w-6 text-primary-600","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M15 9h3.75M15 12h3.75M15 15h3.75M4.5 19.5h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Zm6-10.125a1.875 1.875 0 1 1-3.75 0 1.875 1.875 0 0 1 3.75 0Zm1.294 6.336a6.721 6.721 0 0 1-3.17.789 6.721 6.721 0 0 1-3.168-.789 3.376 3.376 0 0 1 6.338 0Z"}]]}]}],"$L18"]}]}],"$L19"]}],"$L1a","$L1b","$L1c"]}]]}]}]}],"$L1d","$L1e","$L1f","$L20","$L21","$L22","$L23"]}],null,"$L24"]}]
f:["$","$1","h",{"children":[null,[["$","$L25",null,{"children":"$L26"}],["$","meta",null,{"name":"next-size-adjust","content":""}]],["$","$L27",null,{"children":["$","div",null,{"hidden":true,"children":["$","$28",null,{"fallback":null,"children":"$L29"}]}]}]]}]
42:I[15104,[],"OutletBoundary"]
44:I[94777,[],"AsyncMetadataOutlet"]
11:["$","li",null,{"children":["$","$L7",null,{"href":"/destinations/essaouira","className":"hover:text-mbnb-coral transition-colors","children":"Essaouira"}]}]
12:["$","li",null,{"children":["$","$L7",null,{"href":"/destinations/rabat","className":"hover:text-mbnb-coral transition-colors","children":"Rabat"}]}]
13:["$","li",null,{"children":["$","$L7",null,{"href":"/destinations/all","className":"hover:text-mbnb-coral transition-colors","children":"Toutes"}]}]
14:["$","div",null,{"children":[["$","h3",null,{"className":"text-white font-semibold mb-3","children":"App Mobile"}],["$","div",null,{"className":"space-y-2 mb-4","children":[["$","$L7",null,{"href":"https://apps.apple.com/app/mbnb-location-vacances-maroc/id1234567890","className":"block","children":["$","div",null,{"className":"bg-gray-800 hover:bg-gray-700 rounded px-3 py-1.5 flex items-center gap-2 transition-colors","children":[["$","svg",null,{"className":"w-4 h-4","viewBox":"0 0 24 24","children":["$","path",null,{"fill":"#FFFFFF","d":"M18.71 19.5C17.88 20.74 17 21.95 15.66 21.97C14.32 22 13.89 21.18 12.37 21.18C10.84 21.18 10.37 21.95 9.09997 22C7.78997 22.05 6.79997 20.68 5.95997 19.47C4.24997 17 2.93997 12.45 4.69997 9.39C5.56997 7.87 7.12997 6.91 8.81997 6.88C10.1 6.86 11.32 7.75 12.11 7.75C12.89 7.75 14.37 6.68 15.92 6.84C16.57 6.87 18.39 7.1 19.56 8.82C19.47 8.88 17.39 10.1 17.41 12.63C17.44 15.65 20.06 16.66 20.09 16.67C20.06 16.74 19.67 18.11 18.71 19.5ZM13 3.5C13.73 2.67 14.94 2.04 15.94 2C16.07 3.17 15.6 4.35 14.9 5.19C14.21 6.04 13.07 6.7 11.95 6.61C11.8 5.46 12.36 4.26 13 3.5Z"}]}],["$","span",null,{"className":"text-xs","children":"App Store"}]]}]}],["$","$L7",null,{"href":"https://play.google.com/store/apps/details?id=com.mbnb.app","className":"block","children":["$","div",null,{"className":"bg-gray-800 hover:bg-gray-700 rounded px-3 py-1.5 flex items-center gap-2 transition-colors","children":[["$","svg",null,{"className":"w-4 h-4","viewBox":"0 0 24 24","children":[["$","path",null,{"fill":"#4285F4","d":"M3 20.5V3.5C3 2.91 3.34 2.39 3.84 2.15L13.69 12L3.84 21.85C3.34 21.6 3 21.09 3 20.5Z"}],["$","path",null,{"fill":"#34A853","d":"M16.81 15.12L6.05 21.34L14.54 12.85L16.81 15.12Z"}],["$","path",null,{"fill":"#FBBC05","d":"M20.16 10.81C20.5 11.08 20.75 11.5 20.75 12C20.75 12.5 20.5 12.92 20.16 13.19L17.89 14.5L15.39 12L17.89 9.5L20.16 10.81Z"}],["$","path",null,{"fill":"#EA4335","d":"M6.05 2.66L16.81 8.88L14.54 11.15L6.05 2.66Z"}]]}],["$","span",null,{"className":"text-xs","children":"Google Play"}]]}]}]]}],["$","h3",null,{"className":"text-white font-semibold mb-2 text-sm","children":"Suivez-nous"}],["$","div",null,{"className":"flex gap-2","children":[["$","$L7",null,{"href":"https://facebook.com/mbnb","className":"bg-white hover:bg-gray-100 p-1.5 rounded transition-colors","children":["$","svg",null,{"className":"w-4 h-4","viewBox":"0 0 24 24","children":["$","path",null,{"fill":"#1877F2","d":"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"}]}]}],["$","$L7",null,{"href":"https://x.com/mbnb","className":"bg-black hover:bg-gray-800 p-1.5 rounded transition-colors","children":["$","svg",null,{"className":"w-4 h-4","viewBox":"0 0 24 24","children":["$","path",null,{"fill":"#FFFFFF","d":"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"}]}]}],["$","$L7",null,{"href":"https://instagram.com/mbnb","className":"bg-white hover:bg-gray-100 p-1.5 rounded transition-colors","children":["$","svg",null,{"className":"w-4 h-4","viewBox":"0 0 24 24","children":[["$","defs",null,{"children":["$","radialGradient",null,{"id":"instagram-gradient","cx":"0.3","cy":"1","r":"1.4","children":[["$","stop",null,{"offset":"0%","stopColor":"#FED576"}],["$","stop",null,{"offset":"26%","stopColor":"#F47133"}],["$","stop",null,{"offset":"61%","stopColor":"#BC3081"}],["$","stop",null,{"offset":"100%","stopColor":"#4F5BD5"}]]}]}],["$","path",null,{"fill":"url(#instagram-gradient)","d":"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zM5.838 12a6.162 6.162 0 1112.324 0 6.162 6.162 0 01-12.324 0zM12 16a4 4 0 110-8 4 4 0 010 8zm4.965-10.405a1.44 1.44 0 112.881.001 1.44 1.44 0 01-2.881-.001z"}]]}]}],"$L2a","$L2b"]}]]}]
15:["$","div",null,{"className":"border-t border-gray-800","children":["$","div",null,{"className":"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4","children":["$","div",null,{"className":"flex flex-col lg:flex-row justify-between items-center gap-3","children":[["$","div",null,{"className":"flex items-center gap-2","children":[["$","span",null,{"className":"text-xl font-bold text-mbnb-coral","children":"Mbnb"}],["$","span",null,{"className":"text-xs","children":["© ",2025," Mbnb, Inc."]}]]}],["$","div",null,{"className":"flex flex-wrap gap-3 text-xs","children":[["$","$L7",null,{"href":"/privacy","className":"hover:text-mbnb-coral transition-colors","children":"Confidentialité"}],["$","span",null,{"className":"text-gray-600","children":"·"}],["$","$L7",null,{"href":"/terms","className":"hover:text-mbnb-coral transition-colors","children":"Conditions"}],["$","span",null,{"className":"text-gray-600","children":"·"}],["$","$L7",null,{"href":"/legal","className":"hover:text-mbnb-coral transition-colors","children":"Mentions légales"}],["$","span",null,{"className":"text-gray-600","children":"·"}],["$","$L7",null,{"href":"/cookies","className":"hover:text-mbnb-coral transition-colors","children":"Cookies"}]]}],["$","div",null,{"className":"flex items-center gap-4 text-xs","children":[["$","div",null,{"className":"flex items-center gap-1","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-3 h-3","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}],["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"}]]}],["$","span",null,{"children":"Maroc"}]]}],["$","div",null,{"className":"flex items-center gap-1","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-3 h-3","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}]]}],["$","span",null,{"children":"<EMAIL>"}]]}]]}]]}]}]}]
18:["$","div",null,{"className":"flex-1","children":[["$","h3",null,{"ref":"$undefined","className":"font-semibold tracking-tight text-xl text-neutral-900","children":"Vérification d'identité"}],["$","div",null,{"className":"flex items-center mt-1","children":[["$","span",null,{"className":"text-2xl font-bold text-primary-600","children":[98,"%"]}],["$","span",null,{"className":"text-sm text-neutral-500 ml-2","children":"taux de vérification"}]]}]]}]
19:["$","div",null,{"ref":"$undefined","className":"","children":[["$","p",null,{"className":"text-neutral-600 mb-4","children":"Contrôle rigoureux de tous les utilisateurs"}],["$","ul",null,{"className":"space-y-2","children":[["$","li","0",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Pièce d'identité officielle obligatoire"]}],["$","li","1",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Vérification croisée bases de données"]}],["$","li","2",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Selfie avec document pour authentification"]}],["$","li","3",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Validation en moins de 24h"]}],["$","li","4",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Badge vérifié visible sur profils"]}]]}]]}]
1a:["$","div","1",{"ref":"$undefined","className":"rounded-xl overflow-hidden duration-300 bg-white border border-gray-200 p-5 hover:shadow-lg transition-shadow","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5","children":["$","div",null,{"className":"flex items-center space-x-3","children":[["$","div",null,{"className":"p-3 bg-primary-100 rounded-lg","children":["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"h-6 w-6 text-primary-600","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}]]}]}],["$","div",null,{"className":"flex-1","children":[["$","h3",null,{"ref":"$undefined","className":"font-semibold tracking-tight text-xl text-neutral-900","children":"Assurance voyage intégrée"}],["$","div",null,{"className":"flex items-center mt-1","children":[["$","span",null,{"className":"text-2xl font-bold text-primary-600","children":[100,"%"]}],["$","span",null,{"className":"text-sm text-neutral-500 ml-2","children":"voyages couverts"}]]}]]}]]}]}],["$","div",null,{"ref":"$undefined","className":"","children":[["$","p",null,{"className":"text-neutral-600 mb-4","children":"Protection complète incluse automatiquement"}],["$","ul",null,{"className":"space-y-2","children":[["$","li","0",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Assurance responsabilité civile jusqu'à 1M MAD"]}],["$","li","1",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Protection biens personnels 50,000 MAD"]}],["$","li","2",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Assistance médicale d'urgence 24h/24"]}],["$","li","3",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Rapatriement sanitaire si nécessaire"]}],["$","li","4",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Annulation voyage pour cause médicale"]}]]}]]}]]}]
1b:["$","div","2",{"ref":"$undefined","className":"rounded-xl overflow-hidden duration-300 bg-white border border-gray-200 p-5 hover:shadow-lg transition-shadow","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5","children":["$","div",null,{"className":"flex items-center space-x-3","children":[["$","div",null,{"className":"p-3 bg-primary-100 rounded-lg","children":["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"h-6 w-6 text-primary-600","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M6.827 6.175A2.31 2.31 0 0 1 5.186 7.23c-.38.054-.757.112-1.134.175C2.999 7.58 2.25 8.507 2.25 9.574V18a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9.574c0-1.067-.75-1.994-1.802-2.169a47.865 47.865 0 0 0-1.134-.175 2.31 2.31 0 0 1-1.64-1.055l-.822-1.316a2.192 2.192 0 0 0-1.736-1.039 48.774 48.774 0 0 0-5.232 0 2.192 2.192 0 0 0-1.736 1.039l-.821 1.316Z"}],["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M16.5 12.75a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0ZM18.75 10.5h.008v.008h-.008V10.5Z"}]]}]}],["$","div",null,{"className":"flex-1","children":[["$","h3",null,{"ref":"$undefined","className":"font-semibold tracking-tight text-xl text-neutral-900","children":"Contrôle qualité logements"}],["$","div",null,{"className":"flex items-center mt-1","children":[["$","span",null,{"className":"text-2xl font-bold text-primary-600","children":[95,"%"]}],["$","span",null,{"className":"text-sm text-neutral-500 ml-2","children":"logements inspectés"}]]}]]}]]}]}],["$","div",null,{"ref":"$undefined","className":"","children":[["$","p",null,{"className":"text-neutral-600 mb-4","children":"Inspection et validation des propriétés"}],["$","ul",null,{"className":"space-y-2","children":[["$","li","0",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Visite obligatoire avant première publication"]}],["$","li","1",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Photos récentes datées géolocalisées"]}],["$","li","2",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Contrôle conformité sécurité incendie"]}],["$","li","3",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Vérification équipements sécurité"]}],["$","li","4",{"className":"text-sm text-neutral-600 flex items-start","children":["$L2c","Audit qualité périodique aléatoire"]}]]}]]}]]}]
1c:["$","div","3",{"ref":"$undefined","className":"rounded-xl overflow-hidden duration-300 bg-white border border-gray-200 p-5 hover:shadow-lg transition-shadow","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5","children":["$","div",null,{"className":"flex items-center space-x-3","children":[["$","div",null,{"className":"p-3 bg-primary-100 rounded-lg","children":["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"h-6 w-6 text-primary-600","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M10.125 2.25h-4.5c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125v-9M10.125 2.25h.375a9 9 0 0 1 9 9v.375M10.125 2.25A3.375 3.375 0 0 1 13.5 5.625v1.5c0 .621.504 1.125 1.125 1.125h1.5a3.375 3.375 0 0 1 3.375 3.375M9 15l2.25 2.25L15 12"}]]}]}],["$","div",null,{"className":"flex-1","children":[["$","h3",null,{"ref":"$undefined","className":"font-semibold tracking-tight text-xl text-neutral-900","children":"Standards communauté"}],["$","div",null,{"className":"flex items-center mt-1","children":[["$","span",null,{"className":"text-2xl font-bold text-primary-600","children":[99,"%"]}],["$","span",null,{"className":"text-sm text-neutral-500 ml-2","children":"satisfaction sécurité"}]]}]]}]]}]}],["$","div",null,{"ref":"$undefined","className":"","children":[["$","p",null,{"className":"text-neutral-600 mb-4","children":"Règles strictes et modération active"}],["$","ul",null,{"className":"space-y-2","children":[["$","li","0",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Charte communauté détaillée obligatoire"]}],["$","li","1",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Système de signalement en temps réel"]}],["$","li","2",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Équipe modération 24h/24 en français/arabe"]}],["$","li","3",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Sanctions graduées selon gravité"]}],["$","li","4",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0","children":[null,"$L2d"]}],"Exclusion définitive cas graves"]}]]}]]}]]}]
1d:["$","section",null,{"className":"py-12","children":["$","div",null,{"className":"container mx-auto px-4","children":["$","div",null,{"className":"max-w-4xl mx-auto","children":[["$","h2",null,{"className":"text-3xl font-bold text-center mb-8 text-neutral-900","children":"Contacts d'urgence"}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 gap-6","children":[["$","div","0",{"ref":"$undefined","className":"rounded-xl overflow-hidden duration-300 bg-white border border-gray-200 p-5 hover:shadow-lg transition-shadow","children":["$","div",null,{"ref":"$undefined","className":"p-6","children":[["$","div",null,{"className":"flex items-center space-x-3 mb-4","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-5 h-5 text-primary-600","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}]]}],["$","h3",null,{"className":"text-lg font-semibold text-neutral-900","children":"Urgence Mbnb"}]]}],["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"children":["$","span",null,{"className":"text-2xl font-bold text-primary-600","children":"+212 5 22 00 00 00"}]}],["$","div",null,{"className":"space-y-1 text-sm","children":[["$","div",null,{"children":[["$","span",null,{"className":"font-medium text-neutral-700","children":"Disponibilité:"}],["$","span",null,{"className":"text-neutral-600 ml-2","children":"24h/24 - 7j/7"}]]}],["$","div",null,{"children":[["$","span",null,{"className":"font-medium text-neutral-700","children":"Langues:"}],["$","span",null,{"className":"text-neutral-600 ml-2","children":"Français, Arabe, Anglais, Espagnol"}]]}],["$","p",null,{"className":"text-neutral-600 mt-2","children":"Ligne directe pour urgences sécurité"}]]}]]}]]}]}],["$","div","1",{"ref":"$undefined","className":"rounded-xl overflow-hidden duration-300 bg-white border border-gray-200 p-5 hover:shadow-lg transition-shadow","children":["$","div",null,{"ref":"$undefined","className":"p-6","children":[["$","div",null,{"className":"flex items-center space-x-3 mb-4","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-5 h-5 text-primary-600","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}]]}],["$","h3",null,{"className":"text-lg font-semibold text-neutral-900","children":"Police Tourisme Maroc"}]]}],["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"children":["$","span",null,{"className":"text-2xl font-bold text-primary-600","children":"19"}]}],["$","div",null,{"className":"space-y-1 text-sm","children":[["$","div",null,{"children":[["$","span",null,{"className":"font-medium text-neutral-700","children":"Disponibilité:"}],["$","span",null,{"className":"text-neutral-600 ml-2","children":"24h/24"}]]}],["$","div",null,{"children":[["$","span",null,{"className":"font-medium text-neutral-700","children":"Langues:"}],["$","span",null,{"className":"text-neutral-600 ml-2","children":"Arabe, Français"}]]}],["$","p",null,{"className":"text-neutral-600 mt-2","children":"Police spécialisée tourisme"}]]}]]}]]}]}],["$","div","2",{"ref":"$undefined","className":"rounded-xl overflow-hidden duration-300 bg-white border border-gray-200 p-5 hover:shadow-lg transition-shadow","children":"$L2e"}],"$L2f"]}]]}]}]}]
1e:["$","section",null,{"className":"py-12 bg-white","children":["$","div",null,{"className":"container mx-auto px-4","children":["$","div",null,{"className":"max-w-6xl mx-auto","children":[["$","h2",null,{"className":"text-3xl font-bold text-center mb-12 text-neutral-900","children":"Conseils de sécurité"}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-3 gap-8","children":[["$","div","0",{"ref":"$undefined","className":"rounded-xl overflow-hidden duration-300 bg-white border border-gray-200 p-5 hover:shadow-lg transition-shadow","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5","children":["$","div",null,{"className":"flex items-center space-x-3","children":[["$","div",null,{"className":"p-2 bg-secondary-100 rounded-lg","children":["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"h-6 w-6 text-secondary-600","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}]]}]}],["$","h3",null,{"ref":"$undefined","className":"font-semibold tracking-tight text-xl text-neutral-900","children":"Avant votre séjour"}]]}]}],["$","div",null,{"ref":"$undefined","className":"","children":["$","ul",null,{"className":"space-y-3","children":[["$","li","0",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","span",null,{"className":"text-secondary-500 mr-2 mt-1","children":"•"}],"Vérifiez profil complet et avis récents Host"]}],["$","li","1",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","span",null,{"className":"text-secondary-500 mr-2 mt-1","children":"•"}],"Communiquez uniquement via messagerie Mbnb"]}],["$","li","2",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","span",null,{"className":"text-secondary-500 mr-2 mt-1","children":"•"}],"Lisez attentivement règlement intérieur"]}],["$","li","3",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","span",null,{"className":"text-secondary-500 mr-2 mt-1","children":"•"}],"Informez contact urgence de votre itinéraire"]}],["$","li","4",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","span",null,{"className":"text-secondary-500 mr-2 mt-1","children":"•"}],"Vérifiez couverture assurance voyage personnelle"]}]]}]}]]}],["$","div","1",{"ref":"$undefined","className":"rounded-xl overflow-hidden duration-300 bg-white border border-gray-200 p-5 hover:shadow-lg transition-shadow","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5","children":["$","div",null,{"className":"flex items-center space-x-3","children":[["$","div",null,{"className":"p-2 bg-secondary-100 rounded-lg","children":["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"h-6 w-6 text-secondary-600","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}]}],["$","h3",null,{"ref":"$undefined","className":"font-semibold tracking-tight text-xl text-neutral-900","children":"Pendant votre séjour"}]]}]}],["$","div",null,{"ref":"$undefined","className":"","children":["$","ul",null,{"className":"space-y-3","children":[["$","li","0",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","span",null,{"className":"text-secondary-500 mr-2 mt-1","children":"•"}],"Inspectez logement dès arrivée et signalez anomalies"]}],["$","li","1",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","span",null,{"className":"text-secondary-500 mr-2 mt-1","children":"•"}],"Respectez nombre maximum voyageurs autorisé"]}],["$","li","2",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","span",null,{"className":"text-secondary-500 mr-2 mt-1","children":"•"}],"Ne laissez pas entrer personnes non déclarées"]}],"$L30","$L31"]}]}]]}],"$L32"]}]]}]}]}]
1f:["$","section",null,{"className":"py-12","children":["$","div",null,{"className":"container mx-auto px-4","children":["$","div",null,{"className":"max-w-6xl mx-auto","children":[["$","h2",null,{"className":"text-3xl font-bold text-center mb-8 text-neutral-900","children":"Protection des Hosts"}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-3 gap-6","children":[["$","div","0",{"ref":"$undefined","className":"rounded-xl overflow-hidden duration-300 bg-white border border-gray-200 p-5 text-center hover:shadow-lg transition-shadow","children":["$","div",null,{"ref":"$undefined","className":"p-6","children":[["$","h3",null,{"className":"text-lg font-semibold text-neutral-900 mb-3","children":"Vérification invités"}],["$","p",null,{"className":"text-neutral-600 mb-4","children":"Tous les Travelers sont vérifiés avant réservation"}],["$","div",null,{"className":"space-y-1","children":[["$","div","0",{"className":"text-sm text-neutral-600 flex items-center justify-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Identité confirmée"]}],["$","div","1",{"className":"text-sm text-neutral-600 flex items-center justify-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Historique visible"]}],["$","div","2",{"className":"text-sm text-neutral-600 flex items-center justify-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Avis authentiques"]}]]}]]}]}],["$","div","1",{"ref":"$undefined","className":"rounded-xl overflow-hidden duration-300 bg-white border border-gray-200 p-5 text-center hover:shadow-lg transition-shadow","children":["$","div",null,{"ref":"$undefined","className":"p-6","children":[["$","h3",null,{"className":"text-lg font-semibold text-neutral-900 mb-3","children":"Protection propriété"}],["$","p",null,{"className":"text-neutral-600 mb-4","children":"Assurance dommages et vol incluse"}],["$","div",null,{"className":"space-y-1","children":[["$","div","0",{"className":"text-sm text-neutral-600 flex items-center justify-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Couverture 500,000 MAD"]}],["$","div","1",{"className":"text-sm text-neutral-600 flex items-center justify-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Franchise réduite"]}],["$","div","2",{"className":"text-sm text-neutral-600 flex items-center justify-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2","children":[null,"$L33"]}],"Traitement rapide"]}]]}]]}]}],"$L34"]}]]}]}]}]
20:["$","section",null,{"className":"py-12 bg-white","children":["$","div",null,{"className":"container mx-auto px-4","children":["$","div",null,{"className":"max-w-4xl mx-auto","children":["$","div",null,{"ref":"$undefined","className":"rounded-xl overflow-hidden transition-all duration-300 bg-white border border-gray-200 p-5","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5","children":["$","h3",null,{"ref":"$undefined","className":"font-semibold tracking-tight text-2xl text-center text-neutral-900 flex items-center justify-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-6 h-6 mr-2","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}]]}],"Assurance voyage Mbnb"]}]}],["$","div",null,{"ref":"$undefined","className":"p-8","children":[["$","div",null,{"className":"text-center mb-6","children":["$","p",null,{"className":"text-lg text-neutral-700","children":"Couverture automatique incluse dans chaque réservation"}]}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 gap-6","children":[["$","div",null,{"children":[["$","h3",null,{"className":"text-lg font-semibold text-neutral-900 mb-4","children":"Couvertures incluses"}],["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"flex justify-between items-center","children":[["$","span",null,{"className":"text-sm text-neutral-600","children":"Responsabilité civile"}],["$","span",null,{"className":"font-medium","children":"1,000,000 MAD"}]]}],["$","div",null,{"className":"flex justify-between items-center","children":[["$","span",null,{"className":"text-sm text-neutral-600","children":"Protection biens personnels"}],["$","span",null,{"className":"font-medium","children":"50,000 MAD"}]]}],["$","div",null,{"className":"flex justify-between items-center","children":[["$","span",null,{"className":"text-sm text-neutral-600","children":"Assistance médicale"}],["$","span",null,{"className":"font-medium","children":"Illimitée"}]]}],["$","div",null,{"className":"flex justify-between items-center","children":[["$","span",null,{"className":"text-sm text-neutral-600","children":"Rapatriement sanitaire"}],["$","span",null,{"className":"font-medium","children":"Inclus"}]]}]]}]]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-lg font-semibold text-neutral-900 mb-4","children":"Procédure sinistre"}],["$","div",null,{"className":"space-y-3 text-sm text-neutral-600","children":[["$","div",null,{"className":"flex items-start","children":[["$","span",null,{"className":"bg-primary-100 text-primary-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium mr-3 mt-0.5","children":"1"}],["$","span",null,{"children":"Déclaration immédiate via app Mbnb"}]]}],["$","div",null,{"className":"flex items-start","children":[["$","span",null,{"className":"bg-primary-100 text-primary-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium mr-3 mt-0.5","children":"2"}],["$","span",null,{"children":"Documentation avec photos/témoignages"}]]}],["$","div",null,{"className":"flex items-start","children":[["$","span",null,{"className":"bg-primary-100 text-primary-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium mr-3 mt-0.5","children":"3"}],["$","span",null,{"children":"Traitement expert sous 48h ouvrées"}]]}],["$","div",null,{"className":"flex items-start","children":[["$","span",null,{"className":"bg-primary-100 text-primary-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium mr-3 mt-0.5","children":"4"}],["$","span",null,{"children":"Indemnisation directe si acceptée"}]]}]]}]]}]]}],["$","div",null,{"className":"text-center mt-8","children":[["$","p",null,{"className":"text-sm text-neutral-600 mb-4","children":"Assurance fournie par Wafa Assurance - Agréé par Bank Al-Maghrib"}],"$L35"]}]]}]]}]}]}]}]
21:["$","section",null,{"className":"py-16 bg-gray-50","children":["$","div",null,{"className":"container mx-auto px-4","children":["$","div",null,{"className":"max-w-6xl mx-auto","children":[["$","div",null,{"className":"text-center mb-12","children":[["$","h2",null,{"className":"text-3xl font-bold text-neutral-900 mb-4","children":"Paiements ultra-sécurisés"}],["$","p",null,{"className":"text-lg text-neutral-600 max-w-3xl mx-auto","children":"6 moyens de paiement certifiés et sécurisés, intégrés directement à notre infrastructure backend"}]]}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6","children":[["$","div","0",{"ref":"$undefined","className":"rounded-xl overflow-hidden duration-300 bg-white p-5 hover:shadow-lg transition-shadow border-2 border-gray-100","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 pb-4","children":[["$","div",null,{"className":"flex items-center justify-between mb-2","children":[["$","div",null,{"className":"font-bold text-lg text-neutral-900","children":"CMI"}],["$","span",null,{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border px-2.5 py-1 rounded-md text-xs bg-green-50 text-green-700 border-green-200","children":["$undefined","Opérationnel 24h/24",false]}]]}],["$","p",null,{"className":"text-sm text-neutral-600 font-medium","children":"Centre Monétique Interbancaire"}],["$","p",null,{"className":"text-sm text-neutral-500","children":"Cartes bancaires principales"}]]}],["$","div",null,{"ref":"$undefined","className":"","children":[["$","p",null,{"className":"text-sm text-neutral-600 mb-4","children":"Gateway principal pour cartes Visa/Mastercard au Maroc"}],["$","div",null,{"className":"mb-4","children":[["$","h4",null,{"className":"text-sm font-semibold text-neutral-900 mb-2","children":"Sécurité:"}],["$","div",null,{"className":"flex flex-wrap gap-1","children":[["$","span","0",{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border bg-gray-100 text-gray-dark border-gray-200 px-2.5 py-1 rounded-md text-xs","children":["$undefined","3D Secure",false]}],["$","span","1",{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border bg-gray-100 text-gray-dark border-gray-200 px-2.5 py-1 rounded-md text-xs","children":["$undefined","Tokenisation",false]}],["$","span","2",{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border bg-gray-100 text-gray-dark border-gray-200 px-2.5 py-1 rounded-md text-xs","children":["$undefined","Chiffrement AES-256",false]}],["$","span","3",{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border bg-gray-100 text-gray-dark border-gray-200 px-2.5 py-1 rounded-md text-xs","children":["$undefined","Anti-fraude IA",false]}]]}]]}],["$","div",null,{"children":[["$","h4",null,{"className":"text-sm font-semibold text-neutral-900 mb-2","children":"Devises:"}],["$","div",null,{"className":"flex gap-2","children":[["$","span","0",{"className":"text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded","children":"MAD"}],["$","span","1",{"className":"text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded","children":"EUR"}],["$","span","2",{"className":"text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded","children":"USD"}]]}]]}]]}]]}],["$","div","1",{"ref":"$undefined","className":"rounded-xl overflow-hidden duration-300 bg-white p-5 hover:shadow-lg transition-shadow border-2 border-gray-100","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 pb-4","children":[["$","div",null,{"className":"flex items-center justify-between mb-2","children":[["$","div",null,{"className":"font-bold text-lg text-neutral-900","children":"WafaCash"}],["$","span",null,{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border px-2.5 py-1 rounded-md text-xs bg-green-50 text-green-700 border-green-200","children":["$undefined","Disponible 24h/24",false]}]]}],["$","p",null,{"className":"text-sm text-neutral-600 font-medium","children":"Attijariwafa Bank"}],["$","p",null,{"className":"text-sm text-neutral-500","children":"Virement instantané"}]]}],["$","div",null,{"ref":"$undefined","className":"","children":[["$","p",null,{"className":"text-sm text-neutral-600 mb-4","children":"Virements instantanés depuis tous comptes bancaires marocains"}],"$L36","$L37"]}]]}],"$L38","$L39","$L3a","$L3b"]}],"$L3c"]}]}]}]
22:["$","section",null,{"className":"py-16 bg-white","children":["$","div",null,{"className":"container mx-auto px-4","children":["$","div",null,{"className":"max-w-6xl mx-auto","children":[["$","div",null,{"className":"text-center mb-12","children":[["$","h2",null,{"className":"text-3xl font-bold text-neutral-900 mb-4","children":"Conformité légale 95% complète"}],["$","p",null,{"className":"text-lg text-neutral-600 max-w-3xl mx-auto","children":"Mbnb respecte intégralement la législation marocaine et les standards internationaux"}]]}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 gap-8","children":[["$","div",null,{"ref":"$undefined","className":"rounded-xl overflow-hidden transition-all duration-300 bg-white p-5 border-2 border-blue-100","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 bg-blue-50","children":["$","h3",null,{"ref":"$undefined","className":"font-semibold tracking-tight text-lg text-blue-900 flex items-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"h-5 w-5 mr-2","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}]]}],"Protection des Données CNDP"]}]}],["$","div",null,{"ref":"$undefined","className":"p-6","children":[["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"flex justify-between","children":[["$","span",null,{"className":"text-sm text-neutral-600","children":"Déclaration:"}],["$","span",null,{"className":"font-medium text-blue-700","children":"D-GC/2025/001"}]]}],["$","div",null,{"className":"flex justify-between","children":[["$","span",null,{"className":"text-sm text-neutral-600","children":"Loi:"}],["$","span",null,{"className":"font-medium","children":"Loi 09-08 sur protection données personnelles"}]]}],["$","div",null,{"className":"flex justify-between","children":[["$","span",null,{"className":"text-sm text-neutral-600","children":"Statut:"}],["$","span",null,{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border border-gray-200 px-2.5 py-1 text-sm rounded-md bg-green-100 text-green-800","children":["$undefined","Conforme et déclaré",false]}]]}],["$","div",null,{"className":"flex justify-between","children":[["$","span",null,{"className":"text-sm text-neutral-600","children":"Renouvellement:"}],["$","span",null,{"className":"font-medium","children":"2025-12-31"}]]}]]}],["$","div",null,{"className":"mt-4","children":[["$","h4",null,{"className":"text-sm font-semibold mb-2","children":"Protections actives:"}],["$","ul",null,{"className":"space-y-1","children":[["$","li","0",{"className":"text-xs text-neutral-600 flex items-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"h-3 w-3 text-green-500 mr-2","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Masquage automatique données sensibles"]}],["$","li","1",{"className":"text-xs text-neutral-600 flex items-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"h-3 w-3 text-green-500 mr-2","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Droits GDPR étendus"]}],["$","li","2",{"className":"text-xs text-neutral-600 flex items-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"h-3 w-3 text-green-500 mr-2","children":[null,"$L3d"]}],"Audit conformité annuel"]}]]}]]}]]}]]}],"$L3e","$L3f","$L40"]}],"$L41"]}]}]}]
23:["$","section",null,{"className":"py-16 bg-mbnb-coral text-white","children":["$","div",null,{"className":"container mx-auto px-4 text-center","children":[["$","h2",null,{"className":"text-3xl md:text-4xl font-bold mb-6","children":"Une question sur la sécurité ?"}],["$","p",null,{"className":"text-xl mb-8 text-white/90","children":"Notre équipe sécurité est disponible 24h/24 pour vous accompagner"}],["$","div",null,{"className":"flex flex-wrap justify-center gap-4","children":[["$","$L7",null,{"href":"/contact-safety","children":["$","button",null,{"ref":"$undefined","className":"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-mbnb-coral focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed bg-mbnb-coral hover:bg-mbnb-coral-dark text-white shadow-sm py-3 text-lg px-8","disabled":false,"children":[null,"Contacter l'équipe sécurité","$undefined"]}]}],["$","$L7",null,{"href":"/trust-safety","children":["$","button",null,{"ref":"$undefined","className":"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-mbnb-coral focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed border-2 py-3 text-lg px-8 border-white text-white hover:bg-white hover:text-primary-600","disabled":false,"children":[null,"Centre de confiance","$undefined"]}]}]]}]]}]}]
24:["$","$L42",null,{"children":["$L43",["$","$L44",null,{"promise":"$@45"}]]}]
2a:["$","$L7",null,{"href":"https://youtube.com/mbnb","className":"bg-white hover:bg-gray-100 p-1.5 rounded transition-colors","children":["$","svg",null,{"className":"w-4 h-4","viewBox":"0 0 24 24","children":["$","path",null,{"fill":"#FF0000","d":"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"}]}]}]
2b:["$","$L7",null,{"href":"https://linkedin.com/company/mbnb","className":"bg-white hover:bg-gray-100 p-1.5 rounded transition-colors","children":["$","svg",null,{"className":"w-4 h-4","viewBox":"0 0 24 24","children":["$","path",null,{"fill":"#0A66C2","d":"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"}]}]}]
2c:["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}]
2d:["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]
2e:["$","div",null,{"ref":"$undefined","className":"p-6","children":[["$","div",null,{"className":"flex items-center space-x-3 mb-4","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-5 h-5 text-primary-600","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}]]}],["$","h3",null,{"className":"text-lg font-semibold text-neutral-900","children":"SAMU Maroc"}]]}],["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"children":["$","span",null,{"className":"text-2xl font-bold text-primary-600","children":"15"}]}],["$","div",null,{"className":"space-y-1 text-sm","children":[["$","div",null,{"children":[["$","span",null,{"className":"font-medium text-neutral-700","children":"Disponibilité:"}],["$","span",null,{"className":"text-neutral-600 ml-2","children":"24h/24"}]]}],["$","div",null,{"children":[["$","span",null,{"className":"font-medium text-neutral-700","children":"Langues:"}],["$","span",null,{"className":"text-neutral-600 ml-2","children":"Arabe, Français"}]]}],["$","p",null,{"className":"text-neutral-600 mt-2","children":"Services médicaux d'urgence"}]]}]]}]]}]
2f:["$","div","3",{"ref":"$undefined","className":"rounded-xl overflow-hidden duration-300 bg-white border border-gray-200 p-5 hover:shadow-lg transition-shadow","children":["$","div",null,{"ref":"$undefined","className":"p-6","children":[["$","div",null,{"className":"flex items-center space-x-3 mb-4","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-5 h-5 text-primary-600","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}]]}],["$","h3",null,{"className":"text-lg font-semibold text-neutral-900","children":"Pompiers"}]]}],["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"children":["$","span",null,{"className":"text-2xl font-bold text-primary-600","children":"15"}]}],["$","div",null,{"className":"space-y-1 text-sm","children":[["$","div",null,{"children":[["$","span",null,{"className":"font-medium text-neutral-700","children":"Disponibilité:"}],["$","span",null,{"className":"text-neutral-600 ml-2","children":"24h/24"}]]}],["$","div",null,{"children":[["$","span",null,{"className":"font-medium text-neutral-700","children":"Langues:"}],["$","span",null,{"className":"text-neutral-600 ml-2","children":"Arabe, Français"}]]}],["$","p",null,{"className":"text-neutral-600 mt-2","children":"Secours et lutte contre incendie"}]]}]]}]]}]}]
30:["$","li","3",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","span",null,{"className":"text-secondary-500 mr-2 mt-1","children":"•"}],"Gardez toujours copie pièces identité avec vous"]}]
31:["$","li","4",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","span",null,{"className":"text-secondary-500 mr-2 mt-1","children":"•"}],"Contactez Host pour questions lieu de résidence"]}]
32:["$","div","2",{"ref":"$undefined","className":"rounded-xl overflow-hidden duration-300 bg-white border border-gray-200 p-5 hover:shadow-lg transition-shadow","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5","children":["$","div",null,{"className":"flex items-center space-x-3","children":[["$","div",null,{"className":"p-2 bg-secondary-100 rounded-lg","children":["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"h-6 w-6 text-secondary-600","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}]]}]}],["$","h3",null,{"ref":"$undefined","className":"font-semibold tracking-tight text-xl text-neutral-900","children":"Signalement incidents"}]]}]}],["$","div",null,{"ref":"$undefined","className":"","children":["$","ul",null,{"className":"space-y-3","children":[["$","li","0",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","span",null,{"className":"text-secondary-500 mr-2 mt-1","children":"•"}],"Signalez immédiatement tout problème sécurité"]}],["$","li","1",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","span",null,{"className":"text-secondary-500 mr-2 mt-1","children":"•"}],"Documentez avec photos/vidéos si possible"]}],["$","li","2",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","span",null,{"className":"text-secondary-500 mr-2 mt-1","children":"•"}],"Contactez équipe Mbnb via app ou téléphone"]}],["$","li","3",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","span",null,{"className":"text-secondary-500 mr-2 mt-1","children":"•"}],"Préservez preuves communications suspectes"]}],["$","li","4",{"className":"text-sm text-neutral-600 flex items-start","children":[["$","span",null,{"className":"text-secondary-500 mr-2 mt-1","children":"•"}],"Coopérez avec enquêtes sécurité"]}]]}]}]]}]
33:["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]
34:["$","div","2",{"ref":"$undefined","className":"rounded-xl overflow-hidden duration-300 bg-white border border-gray-200 p-5 text-center hover:shadow-lg transition-shadow","children":["$","div",null,{"ref":"$undefined","className":"p-6","children":[["$","h3",null,{"className":"text-lg font-semibold text-neutral-900 mb-3","children":"Support dédié"}],["$","p",null,{"className":"text-neutral-600 mb-4","children":"Assistance Hosts prioritaire 24h/24"}],["$","div",null,{"className":"space-y-1","children":[["$","div","0",{"className":"text-sm text-neutral-600 flex items-center justify-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Ligne directe"]}],["$","div","1",{"className":"text-sm text-neutral-600 flex items-center justify-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Résolution express"]}],["$","div","2",{"className":"text-sm text-neutral-600 flex items-center justify-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4 text-green-500 mr-2","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Équipe experte"]}]]}]]}]}]
35:["$","$L7",null,{"href":"/insurance-details","children":["$","button",null,{"ref":"$undefined","className":"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-mbnb-coral focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed border-2 border-mbnb-coral text-mbnb-coral hover:bg-mbnb-coral hover:text-white py-2 text-base px-6","disabled":false,"children":[null,"Voir conditions détaillées","$undefined"]}]}]
36:["$","div",null,{"className":"mb-4","children":[["$","h4",null,{"className":"text-sm font-semibold text-neutral-900 mb-2","children":"Sécurité:"}],["$","div",null,{"className":"flex flex-wrap gap-1","children":[["$","span","0",{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border bg-gray-100 text-gray-dark border-gray-200 px-2.5 py-1 rounded-md text-xs","children":["$undefined","SMS OTP",false]}],["$","span","1",{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border bg-gray-100 text-gray-dark border-gray-200 px-2.5 py-1 rounded-md text-xs","children":["$undefined","API sécurisée Wafa",false]}],["$","span","2",{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border bg-gray-100 text-gray-dark border-gray-200 px-2.5 py-1 rounded-md text-xs","children":["$undefined","Monitoring temps réel",false]}]]}]]}]
37:["$","div",null,{"children":[["$","h4",null,{"className":"text-sm font-semibold text-neutral-900 mb-2","children":"Devises:"}],["$","div",null,{"className":"flex gap-2","children":[["$","span","0",{"className":"text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded","children":"MAD"}]]}]]}]
38:["$","div","2",{"ref":"$undefined","className":"rounded-xl overflow-hidden duration-300 bg-white p-5 hover:shadow-lg transition-shadow border-2 border-gray-100","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 pb-4","children":[["$","div",null,{"className":"flex items-center justify-between mb-2","children":[["$","div",null,{"className":"font-bold text-lg text-neutral-900","children":"CashPlus"}],["$","span",null,{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border px-2.5 py-1 rounded-md text-xs bg-green-50 text-green-700 border-green-200","children":["$undefined","Réseau national",false]}]]}],["$","p",null,{"className":"text-sm text-neutral-600 font-medium","children":"Réseau national CashPlus"}],["$","p",null,{"className":"text-sm text-neutral-500","children":"Virement instantané"}]]}],["$","div",null,{"ref":"$undefined","className":"","children":[["$","p",null,{"className":"text-sm text-neutral-600 mb-4","children":"Réseau de 8000+ points de vente à travers le Maroc"}],["$","div",null,{"className":"mb-4","children":[["$","h4",null,{"className":"text-sm font-semibold text-neutral-900 mb-2","children":"Sécurité:"}],["$","div",null,{"className":"flex flex-wrap gap-1","children":[["$","span","0",{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border bg-gray-100 text-gray-dark border-gray-200 px-2.5 py-1 rounded-md text-xs","children":["$undefined","Code PIN",false]}],["$","span","1",{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border bg-gray-100 text-gray-dark border-gray-200 px-2.5 py-1 rounded-md text-xs","children":["$undefined","Géolocalisation",false]}],["$","span","2",{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border bg-gray-100 text-gray-dark border-gray-200 px-2.5 py-1 rounded-md text-xs","children":["$undefined","Réseau sécurisé",false]}]]}]]}],["$","div",null,{"children":[["$","h4",null,{"className":"text-sm font-semibold text-neutral-900 mb-2","children":"Devises:"}],["$","div",null,{"className":"flex gap-2","children":[["$","span","0",{"className":"text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded","children":"MAD"}]]}]]}]]}]]}]
39:["$","div","3",{"ref":"$undefined","className":"rounded-xl overflow-hidden duration-300 bg-white p-5 hover:shadow-lg transition-shadow border-2 border-gray-100","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 pb-4","children":[["$","div",null,{"className":"flex items-center justify-between mb-2","children":[["$","div",null,{"className":"font-bold text-lg text-neutral-900","children":"AliPay"}],["$","span",null,{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border px-2.5 py-1 rounded-md text-xs bg-green-50 text-green-700 border-green-200","children":["$undefined","International",false]}]]}],["$","p",null,{"className":"text-sm text-neutral-600 font-medium","children":"Ant Group (Alibaba)"}],["$","p",null,{"className":"text-sm text-neutral-500","children":"Touristes chinois"}]]}],["$","div",null,{"ref":"$undefined","className":"","children":[["$","p",null,{"className":"text-sm text-neutral-600 mb-4","children":"Solution optimisée pour 800M+ voyageurs chinois"}],["$","div",null,{"className":"mb-4","children":[["$","h4",null,{"className":"text-sm font-semibold text-neutral-900 mb-2","children":"Sécurité:"}],["$","div",null,{"className":"flex flex-wrap gap-1","children":[["$","span","0",{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border bg-gray-100 text-gray-dark border-gray-200 px-2.5 py-1 rounded-md text-xs","children":["$undefined","FaceID biométrique",false]}],["$","span","1",{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border bg-gray-100 text-gray-dark border-gray-200 px-2.5 py-1 rounded-md text-xs","children":["$undefined","AI Anti-fraude",false]}],["$","span","2",{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border bg-gray-100 text-gray-dark border-gray-200 px-2.5 py-1 rounded-md text-xs","children":["$undefined","Blockchain verify",false]}]]}]]}],["$","div",null,{"children":[["$","h4",null,{"className":"text-sm font-semibold text-neutral-900 mb-2","children":"Devises:"}],["$","div",null,{"className":"flex gap-2","children":[["$","span","0",{"className":"text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded","children":"CNY"}],["$","span","1",{"className":"text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded","children":"MAD"}]]}]]}]]}]]}]
3a:["$","div","4",{"ref":"$undefined","className":"rounded-xl overflow-hidden duration-300 bg-white p-5 hover:shadow-lg transition-shadow border-2 border-gray-100","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 pb-4","children":[["$","div",null,{"className":"flex items-center justify-between mb-2","children":[["$","div",null,{"className":"font-bold text-lg text-neutral-900","children":"Google Pay"}],["$","span",null,{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border px-2.5 py-1 rounded-md text-xs bg-green-50 text-green-700 border-green-200","children":["$undefined","Mobile optimisé",false]}]]}],["$","p",null,{"className":"text-sm text-neutral-600 font-medium","children":"Google Payment Solutions"}],["$","p",null,{"className":"text-sm text-neutral-500","children":"Mobile Android"}]]}],["$","div",null,{"ref":"$undefined","className":"","children":[["$","p",null,{"className":"text-sm text-neutral-600 mb-4","children":"Paiement mobile sécurisé pour appareils Android"}],["$","div",null,{"className":"mb-4","children":[["$","h4",null,{"className":"text-sm font-semibold text-neutral-900 mb-2","children":"Sécurité:"}],["$","div",null,{"className":"flex flex-wrap gap-1","children":[["$","span","0",{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border bg-gray-100 text-gray-dark border-gray-200 px-2.5 py-1 rounded-md text-xs","children":["$undefined","Biométrie Android",false]}],["$","span","1",{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border bg-gray-100 text-gray-dark border-gray-200 px-2.5 py-1 rounded-md text-xs","children":["$undefined","Tokenisation Google",false]}],["$","span","2",{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border bg-gray-100 text-gray-dark border-gray-200 px-2.5 py-1 rounded-md text-xs","children":["$undefined","Device Trust",false]}]]}]]}],["$","div",null,{"children":[["$","h4",null,{"className":"text-sm font-semibold text-neutral-900 mb-2","children":"Devises:"}],["$","div",null,{"className":"flex gap-2","children":[["$","span","0",{"className":"text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded","children":"MAD"}],["$","span","1",{"className":"text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded","children":"EUR"}],["$","span","2",{"className":"text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded","children":"USD"}]]}]]}]]}]]}]
3b:["$","div","5",{"ref":"$undefined","className":"rounded-xl overflow-hidden duration-300 bg-white p-5 hover:shadow-lg transition-shadow border-2 border-gray-100","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 pb-4","children":[["$","div",null,{"className":"flex items-center justify-between mb-2","children":[["$","div",null,{"className":"font-bold text-lg text-neutral-900","children":"Apple Pay"}],["$","span",null,{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border px-2.5 py-1 rounded-md text-xs bg-green-50 text-green-700 border-green-200","children":["$undefined","Écosystème Apple",false]}]]}],["$","p",null,{"className":"text-sm text-neutral-600 font-medium","children":"Apple Payment Services"}],["$","p",null,{"className":"text-sm text-neutral-500","children":"Mobile iOS"}]]}],["$","div",null,{"ref":"$undefined","className":"","children":[["$","p",null,{"className":"text-sm text-neutral-600 mb-4","children":"Paiement mobile sécurisé iPhone/iPad/Mac/Watch"}],["$","div",null,{"className":"mb-4","children":[["$","h4",null,{"className":"text-sm font-semibold text-neutral-900 mb-2","children":"Sécurité:"}],["$","div",null,{"className":"flex flex-wrap gap-1","children":[["$","span","0",{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border bg-gray-100 text-gray-dark border-gray-200 px-2.5 py-1 rounded-md text-xs","children":["$undefined","Touch/Face ID",false]}],["$","span","1",{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border bg-gray-100 text-gray-dark border-gray-200 px-2.5 py-1 rounded-md text-xs","children":["$undefined","Secure Element",false]}],["$","span","2",{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border bg-gray-100 text-gray-dark border-gray-200 px-2.5 py-1 rounded-md text-xs","children":["$undefined","Device Unique ID",false]}]]}]]}],["$","div",null,{"children":[["$","h4",null,{"className":"text-sm font-semibold text-neutral-900 mb-2","children":"Devises:"}],["$","div",null,{"className":"flex gap-2","children":[["$","span","0",{"className":"text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded","children":"MAD"}],["$","span","1",{"className":"text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded","children":"EUR"}],["$","span","2",{"className":"text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded","children":"USD"}]]}]]}]]}]]}]
3c:["$","div",null,{"className":"text-center mt-8","children":["$","span",null,{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border border-gray-200 text-sm rounded-md bg-green-100 text-green-800 px-4 py-2","children":["$undefined",[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"h-4 w-4 mr-2","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Tous les paiements sont chiffrés bout-en-bout et surveillés 24h/24"],false]}]}]
3d:["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]
3e:["$","div",null,{"ref":"$undefined","className":"rounded-xl overflow-hidden transition-all duration-300 bg-white p-5 border-2 border-green-100","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 bg-green-50","children":["$","h3",null,{"ref":"$undefined","className":"font-semibold tracking-tight text-lg text-green-900 flex items-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"h-5 w-5 mr-2","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M10.125 2.25h-4.5c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125v-9M10.125 2.25h.375a9 9 0 0 1 9 9v.375M10.125 2.25A3.375 3.375 0 0 1 13.5 5.625v1.5c0 .621.504 1.125 1.125 1.125h1.5a3.375 3.375 0 0 1 3.375 3.375M9 15l2.25 2.25L15 12"}]]}],"Licence Touristique"]}]}],["$","div",null,{"ref":"$undefined","className":"p-6","children":[["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"flex justify-between","children":[["$","span",null,{"className":"text-sm text-neutral-600","children":"Licence:"}],["$","span",null,{"className":"font-medium text-green-700","children":"EHT-2025-MBNB"}]]}],["$","div",null,{"className":"flex justify-between","children":[["$","span",null,{"className":"text-sm text-neutral-600","children":"CRI:"}],["$","span",null,{"className":"font-medium","children":"Déclaré CRI Casablanca-Settat"}]]}],["$","div",null,{"className":"flex justify-between","children":[["$","span",null,{"className":"text-sm text-neutral-600","children":"Statut:"}],["$","span",null,{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border border-gray-200 px-2.5 py-1 text-sm rounded-md bg-green-100 text-green-800","children":["$undefined","Exploitation légale autorisée",false]}]]}],["$","div",null,{"className":"flex justify-between","children":[["$","span",null,{"className":"text-sm text-neutral-600","children":"Renouvellement:"}],["$","span",null,{"className":"font-medium","children":"2026-12-31"}]]}]]}],["$","div",null,{"className":"mt-4","children":[["$","h4",null,{"className":"text-sm font-semibold mb-2","children":"Autorisations:"}],["$","ul",null,{"className":"space-y-1","children":[["$","li","0",{"className":"text-xs text-neutral-600 flex items-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"h-3 w-3 text-green-500 mr-2","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Hébergement touristique légal"]}],["$","li","1",{"className":"text-xs text-neutral-600 flex items-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"h-3 w-3 text-green-500 mr-2","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Intermédiation réservations"]}],["$","li","2",{"className":"text-xs text-neutral-600 flex items-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"h-3 w-3 text-green-500 mr-2","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Commission 18% activities"]}]]}]]}]]}]]}]
3f:["$","div",null,{"ref":"$undefined","className":"rounded-xl overflow-hidden transition-all duration-300 bg-white p-5 border-2 border-purple-100","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 bg-purple-50","children":["$","h3",null,{"ref":"$undefined","className":"font-semibold tracking-tight text-lg text-purple-900 flex items-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"h-5 w-5 mr-2","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}]]}],"Conformité Fiscale"]}]}],["$","div",null,{"ref":"$undefined","className":"p-6","children":[["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"flex justify-between","children":[["$","span",null,{"className":"text-sm text-neutral-600","children":"TVA Tourisme:"}],["$","span",null,{"className":"font-medium text-purple-700","children":"10% hébergement touristique / 20% services standard"}]]}],["$","div",null,{"className":"flex justify-between","children":[["$","span",null,{"className":"text-sm text-neutral-600","children":"ID Fiscal:"}],["$","span",null,{"className":"font-medium","children":"MBNB-TAX-ID-2025"}]]}],["$","div",null,{"className":"flex justify-between","children":[["$","span",null,{"className":"text-sm text-neutral-600","children":"Statut:"}],["$","span",null,{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border border-gray-200 px-2.5 py-1 text-sm rounded-md bg-green-100 text-green-800","children":["$undefined","Déclarations automatisées mensuelles",false]}]]}],["$","div",null,{"className":"flex justify-between","children":[["$","span",null,{"className":"text-sm text-neutral-600","children":"Autorité:"}],["$","span",null,{"className":"font-medium text-xs","children":"Direction Générale des Impôts"}]]}]]}],["$","div",null,{"className":"mt-4","children":[["$","h4",null,{"className":"text-sm font-semibold mb-2","children":"Services:"}],["$","ul",null,{"className":"space-y-1","children":[["$","li","0",{"className":"text-xs text-neutral-600 flex items-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"h-3 w-3 text-purple-500 mr-2","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"TVA touristique optimisée"]}],["$","li","1",{"className":"text-xs text-neutral-600 flex items-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"h-3 w-3 text-purple-500 mr-2","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Factures conformes DGI"]}],["$","li","2",{"className":"text-xs text-neutral-600 flex items-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"h-3 w-3 text-purple-500 mr-2","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"Reporting fiscal automatique"]}]]}]]}]]}]]}]
40:["$","div",null,{"ref":"$undefined","className":"rounded-xl overflow-hidden transition-all duration-300 bg-white p-5 border-2 border-orange-100","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 bg-orange-50","children":["$","h3",null,{"ref":"$undefined","className":"font-semibold tracking-tight text-lg text-orange-900 flex items-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"h-5 w-5 mr-2","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}]]}],"Partenaires Assurance"]}]}],["$","div",null,{"ref":"$undefined","className":"p-6","children":[["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"flex justify-between","children":[["$","span",null,{"className":"text-sm text-neutral-600","children":"Partenaires:"}],["$","div",null,{"className":"text-right","children":[["$","div","0",{"className":"font-medium text-orange-700 text-sm","children":"Wafa Assurance"}],["$","div","1",{"className":"font-medium text-orange-700 text-sm","children":"Saham Assurance"}]]}]]}],["$","div",null,{"className":"flex justify-between","children":[["$","span",null,{"className":"text-sm text-neutral-600","children":"Couverture:"}],["$","span",null,{"className":"font-medium","children":"Responsabilité civile + Protection voyageurs"}]]}],["$","div",null,{"className":"flex justify-between","children":[["$","span",null,{"className":"text-sm text-neutral-600","children":"Statut:"}],["$","span",null,{"ref":"$undefined","className":"inline-flex items-center gap-1.5 font-medium border border-gray-200 px-2.5 py-1 text-sm rounded-md bg-green-100 text-green-800","children":["$undefined","Couverture active 24h/24",false]}]]}]]}],["$","div",null,{"className":"mt-4","children":[["$","h4",null,{"className":"text-sm font-semibold mb-2","children":"Montants garantis:"}],["$","ul",null,{"className":"space-y-1","children":[["$","li","0",{"className":"text-xs text-neutral-600 flex items-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"h-3 w-3 text-orange-500 mr-2","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"1M MAD responsabilité"]}],["$","li","1",{"className":"text-xs text-neutral-600 flex items-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"h-3 w-3 text-orange-500 mr-2","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"500K MAD dommages"]}],["$","li","2",{"className":"text-xs text-neutral-600 flex items-center","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"h-3 w-3 text-orange-500 mr-2","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}]]}],"100K MAD médical"]}]]}]]}]]}]]}]
41:["$","div",null,{"className":"text-center mt-12","children":["$","div",null,{"className":"bg-green-50 border border-green-200 rounded-lg p-6 max-w-4xl mx-auto","children":[["$","h3",null,{"className":"text-lg font-semibold text-green-900 mb-2","children":"Conformité légale validée à 95%"}],["$","p",null,{"className":"text-sm text-green-800","children":"Toutes nos licences, déclarations et autorisations sont à jour et régulièrement auditées. Mbnb opère en parfaite conformité avec le code du tourisme marocain et les lois sur la protection des données."}],["$","div",null,{"className":"flex justify-center space-x-4 mt-4","children":[["$","$L7",null,{"href":"/legal/cndp","children":["$","button",null,{"ref":"$undefined","className":"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-mbnb-coral focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed border-2 border-mbnb-coral text-mbnb-coral hover:bg-mbnb-coral hover:text-white px-3 py-1.5 text-sm","disabled":false,"children":[null,"Voir déclaration CNDP","$undefined"]}]}],["$","$L7",null,{"href":"/legal/tourism","children":["$","button",null,{"ref":"$undefined","className":"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-mbnb-coral focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed border-2 border-mbnb-coral text-mbnb-coral hover:bg-mbnb-coral hover:text-white px-3 py-1.5 text-sm","disabled":false,"children":[null,"Licence touristique","$undefined"]}]}]]}]]}]}]
26:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1, maximum-scale=5, user-scalable=yes"}],["$","meta","2",{"name":"theme-color","media":"(prefers-color-scheme: light)","content":"#FF5A5F"}],["$","meta","3",{"name":"theme-color","media":"(prefers-color-scheme: dark)","content":"#E74C3C"}]]
43:null
46:I[36505,[],"IconMark"]
45:{"metadata":[["$","title","0",{"children":"Sécurité et confiance Mbnb | Protocoles de sécurité locations de vacances | Mbnb"}],["$","meta","1",{"name":"description","content":"Découvrez les protocoles de sécurité Mbnb : vérification identité, assurances voyage, standards communauté. Voyagez en toute confiance au Maroc."}],["$","link","2",{"rel":"author","href":"https://mbnb.ma"}],["$","meta","3",{"name":"author","content":"Mbnb"}],["$","link","4",{"rel":"manifest","href":"/site.webmanifest","crossOrigin":"$undefined"}],["$","meta","5",{"name":"keywords","content":"sécurité, confiance, vérification identité, assurance voyage, protocoles sécurité, Mbnb, Maroc"}],["$","meta","6",{"name":"creator","content":"Mbnb"}],["$","meta","7",{"name":"publisher","content":"Mbnb"}],["$","meta","8",{"name":"robots","content":"index, follow"}],["$","meta","9",{"name":"googlebot","content":"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"}],["$","link","10",{"rel":"canonical","href":"https://mbnb.ma"}],["$","link","11",{"rel":"alternate","hrefLang":"fr-MA","href":"https://mbnb.ma/fr"}],["$","link","12",{"rel":"alternate","hrefLang":"ar-MA","href":"https://mbnb.ma/ar"}],["$","link","13",{"rel":"alternate","hrefLang":"en-US","href":"https://mbnb.ma/en"}],["$","link","14",{"rel":"alternate","hrefLang":"es-ES","href":"https://mbnb.ma/es"}],["$","link","15",{"rel":"alternate","hrefLang":"zh-CN","href":"https://mbnb.ma/zh"}],["$","link","16",{"rel":"alternate","hrefLang":"ru-RU","href":"https://mbnb.ma/ru"}],["$","meta","17",{"name":"format-detection","content":"telephone=no, address=no, email=no"}],["$","meta","18",{"name":"google-site-verification","content":"YOUR_GOOGLE_VERIFICATION_CODE"}],["$","meta","19",{"name":"y_key","content":"YOUR_YAHOO_VERIFICATION_CODE"}],["$","meta","20",{"name":"yandex-verification","content":"YOUR_YANDEX_VERIFICATION_CODE"}],["$","meta","21",{"name":"me","content":"<EMAIL>"}],["$","meta","22",{"property":"og:title","content":"Sécurité et confiance - Mbnb"}],["$","meta","23",{"property":"og:description","content":"Protocoles de sécurité complets pour des voyages sereins"}],["$","meta","24",{"property":"og:image","content":"https://mbnb.ma/images/og/safety.jpg"}],["$","meta","25",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","26",{"name":"twitter:creator","content":"@mbnb_ma"}],["$","meta","27",{"name":"twitter:title","content":"Mbnb - Plateforme de Location de Vacances au Maroc"}],["$","meta","28",{"name":"twitter:description","content":"Découvrez des hébergements authentiques et les TOP 100 activités touristiques au Maroc. Première plateforme marocaine mariant IA prédictive et patrimoine authentique."}],["$","meta","29",{"name":"twitter:image","content":"https://cdn.mbnb.ma/twitter-image.jpg"}],["$","link","30",{"rel":"icon","href":"/favicon.ico"}],["$","link","31",{"rel":"icon","href":"/favicon-16x16.png","sizes":"16x16","type":"image/png"}],["$","link","32",{"rel":"icon","href":"/favicon-32x32.png","sizes":"32x32","type":"image/png"}],["$","link","33",{"rel":"apple-touch-icon","href":"/apple-touch-icon.png"}],["$","link","34",{"rel":"apple-touch-icon","href":"/apple-touch-icon-180x180.png","sizes":"180x180","type":"image/png"}],["$","link","35",{"rel":"mask-icon","href":"/safari-pinned-tab.svg"}],["$","$L46","36",{}]],"error":null,"digest":"$undefined"}
29:"$45:metadata"

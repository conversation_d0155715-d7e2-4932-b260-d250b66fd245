(()=>{var a={};a.id=4844,a.ids=[4844],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1691:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"}))})},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24316:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))})},26596:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{fillRule:"evenodd",d:"M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z",clipRule:"evenodd"}))})},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},51974:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))})},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70376:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>u,metadata:()=>m});var d=c(5939),e=c(51974),f=c(92176),g=c(1691),h=c(24316),i=c(11110);let j=i.forwardRef(function({title:a,titleId:b,...c},d){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:d,"aria-labelledby":b},c),a?i.createElement("title",{id:b},a):null,i.createElement("path",{d:"M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"}),i.createElement("path",{fillRule:"evenodd",d:"M1.323 11.447C2.811 6.976 7.028 3.75 12.001 3.75c4.97 0 9.185 3.223 10.675 7.69.12.362.12.752 0 1.113-1.487 4.471-5.705 7.697-10.677 7.697-4.97 0-9.186-3.223-10.675-7.69a1.762 1.762 0 0 1 0-1.113ZM17.25 12a5.25 5.25 0 1 1-10.5 0 5.25 5.25 0 0 1 10.5 0Z",clipRule:"evenodd"}))});var k=c(26596);let l=i.forwardRef(function({title:a,titleId:b,...c},d){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:d,"aria-labelledby":b},c),a?i.createElement("title",{id:b},a):null,i.createElement("path",{fillRule:"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z",clipRule:"evenodd"}))}),m={title:"Signaler un Probl\xe8me - Mbnb | Mod\xe9ration et Protection Communaut\xe9",description:"Signalez tout comportement inappropri\xe9 ou probl\xe8me de s\xe9curit\xe9. \xc9quipe mod\xe9ration 24h/24, traitement sous 4h, protection communaut\xe9 Mbnb.",keywords:"signalement, mod\xe9ration, s\xe9curit\xe9 communaut\xe9, protection, rapport incident, Mbnb",openGraph:{title:"Signaler un Probl\xe8me - Mbnb",description:"Protection et mod\xe9ration communautaire 24h/24",images:["/images/community-safety.jpg"]}},n=[{id:"safety-security",title:"S\xe9curit\xe9 et Protection",icon:e.A,description:"Comportements dangereux, menaces, harc\xe8lement, discrimination",priority:"Urgente",responseTime:"2h maximum",color:"red",examples:["Menaces physiques ou verbales","Harc\xe8lement sexuel ou discrimination","Comportement violent ou agressif","Violation de domicile ou propri\xe9t\xe9 priv\xe9e","Activit\xe9s ill\xe9gales dans l'h\xe9bergement"],actions:["Suspension imm\xe9diate si n\xe9cessaire","Enqu\xeate prioritaire","Contact forces de l'ordre si requis"]},{id:"property-fraud",title:"Fraude et Tromperie",icon:f.A,description:"Annonces frauduleuses, fausses informations, escroquerie",priority:"Haute",responseTime:"4h maximum",color:"orange",examples:["Photos ne correspondant pas \xe0 la r\xe9alit\xe9","\xc9quipements ou services non existants","Annulation abusive de derni\xe8re minute","Surfacturation ou frais cach\xe9s","Fausse identit\xe9 host ou traveler"],actions:["V\xe9rification imm\xe9diate","Suspension annonce si confirm\xe9","Remboursement int\xe9gral si applicable"]},{id:"quality-service",title:"Qualit\xe9 et Service",icon:g.A,description:"Propret\xe9, standards qualit\xe9, service client d\xe9faillant",priority:"Normale",responseTime:"24h maximum",color:"yellow",examples:["Propret\xe9 insuffisante de l'h\xe9bergement","\xc9quipements d\xe9faillants ou manquants","Communication inexistante avec le host","Non-respect des r\xe8gles int\xe9rieures","Nuisances sonores r\xe9p\xe9t\xe9es"],actions:["M\xe9diation host-traveler","Plan d'am\xe9lioration qualit\xe9","Compensation si justifi\xe9e"]},{id:"platform-abuse",title:"Abus de Plateforme",icon:j,description:"Violation conditions d'utilisation, spam, compte multiple",priority:"Normale",responseTime:"48h maximum",color:"blue",examples:["Cr\xe9ation de comptes multiples (smurf)","Manipulation des avis et notes","Spam ou publicit\xe9 non autoris\xe9e","Utilisation commerciale non d\xe9clar\xe9e","Contournement des frais Mbnb"],actions:["V\xe9rification technique","Fusion ou suppression comptes","Sanctions selon gravit\xe9"]}],o={locations:["Casablanca (\xe9quipe principale)","Rabat (support)","Marrakech (terrain)"],specializations:["S\xe9curit\xe9 et protection (12 sp\xe9cialistes)","Fraude et v\xe9rification (15 sp\xe9cialistes)","Qualit\xe9 et m\xe9diation (20 sp\xe9cialistes)"]},p=[{step:1,title:"Signalement",duration:"2-5 minutes",description:"Formulaire d\xe9taill\xe9 avec preuves (photos, captures, t\xe9moignages)",automated:!0,actions:["Accus\xe9 r\xe9ception automatique","R\xe9f\xe9rence unique attribu\xe9e","Notification \xe9quipe mod\xe9ration"]},{step:2,title:"Analyse Priorit\xe9",duration:"15-30 minutes",description:"Classification automatique + validation humaine selon gravit\xe9",automated:!1,actions:["Cat\xe9gorisation selon type","Attribution niveau priorit\xe9","Assignation mod\xe9rateur comp\xe9tent"]},{step:3,title:"Enqu\xeate",duration:"2h-48h selon priorit\xe9",description:"V\xe9rification faits, contact parties concern\xe9es, collecte preuves",automated:!1,actions:["Contact host et traveler","V\xe9rification techniques si n\xe9cessaire","Collecte t\xe9moignages additionnels"]},{step:4,title:"D\xe9cision",duration:"30 minutes-2h",description:"D\xe9cision finale bas\xe9e sur preuves et politique communaut\xe9",automated:!1,actions:["Application sanctions si requises","Communication aux parties","Suivi et surveillance"]}],q=[{level:"Avertissement",description:"Premier incident mineur, rappel des r\xe8gles",duration:"Permanent dans dossier",triggers:["Non-respect mineur r\xe8gles","Communication inappropri\xe9e ponctuelle","Retard check-in/out r\xe9p\xe9t\xe9s"],consequences:["Email explicatif d\xe9taill\xe9","Marquage dossier utilisateur","Surveillance renforc\xe9e 30 jours"]},{level:"Restriction Temporaire",description:"Limitation fonctionnalit\xe9s 7-30 jours selon gravit\xe9",duration:"7 jours \xe0 1 mois",triggers:["R\xe9cidive apr\xe8s avertissement","Violation mod\xe9r\xe9e standards","Communication agressive r\xe9p\xe9t\xe9e"],consequences:["Impossibilit\xe9 nouvelles r\xe9servations","Masquage annonces (hosts)","Contact support obligatoire r\xe9activation"]},{level:"Suspension Temporaire",description:"Compte gel\xe9 temporairement, enqu\xeate approfondie",duration:"1-6 mois",triggers:["Comportement inqui\xe9tant s\xe9curit\xe9","Fraude suspect\xe9e","Multiples violations graves"],consequences:["Blocage total compte","Annulation r\xe9servations en cours","Enqu\xeate juridique si n\xe9cessaire"]},{level:"Exclusion D\xe9finitive",description:"Bannissement permanent plateforme Mbnb",duration:"Permanent",triggers:["Violence ou menaces graves","Fraude confirm\xe9e importante","Activit\xe9s ill\xe9gales prouv\xe9es"],consequences:["Suppression d\xe9finitive compte","Signalement autorit\xe9s si requis","Blacklist pr\xe9ventive cr\xe9ations compte"]}],r={totalReports2024:"2,847",averageResponseTime:"6.2h",resolutionRate:"94.8%",breakdown:[{category:"Qualit\xe9 et Service",percentage:42,count:"1,196 signalements"},{category:"Fraude et Tromperie",percentage:28,count:"797 signalements"},{category:"Abus de Plateforme",percentage:18,count:"512 signalements"},{category:"S\xe9curit\xe9 et Protection",percentage:12,count:"342 signalements"}],sanctions:[{type:"Avertissements",count:"1,234",percentage:61},{type:"Restrictions temporaires",count:"456",percentage:23},{type:"Suspensions temporaires",count:"89",percentage:11},{type:"Exclusions d\xe9finitives",count:"23",percentage:5}]},s=[{law:"Code P\xe9nal Marocain - Articles 503-510",scope:"Harc\xe8lement, menaces, violences",application:"Collaboration automatique avec DGSN si signalement grave",contact:"Brigade Cybercriminalit\xe9 Casablanca"},{law:"Loi 09-08 Protection Donn\xe9es Personnelles",scope:"Respect vie priv\xe9e, masquage donn\xe9es sensibles",application:"Politique stricte protection identit\xe9 signalants",contact:"CNDP (Commission Nationale Protection Donn\xe9es)"},{law:"Code de Commerce - Fraude Commerciale",scope:"Pratiques commerciales d\xe9loyales, tromperie",application:"Signalement Direction G\xe9n\xe9rale Commerce si fraude confirm\xe9e",contact:"Minist\xe8re de l'Industrie et Commerce"},{law:"Code du Tourisme Marocain",scope:"Standards h\xe9bergements, s\xe9curit\xe9 touristes",application:"Coordination avec Minist\xe8re Tourisme pour violations graves",contact:"ONMT et D\xe9l\xe9gations R\xe9gionales Tourisme"}],t=[{type:"Urgence S\xe9curit\xe9 Mbnb",number:"+212 5 22 00 00 00",availability:"24h/24 - 7j/7",description:"Situations urgentes n\xe9cessitant intervention imm\xe9diate"},{type:"Police Secours Maroc",number:"19",availability:"24h/24",description:"Urgences s\xe9curitaires graves n\xe9cessitant intervention police"},{type:"Brigade Cybercriminalit\xe9",number:"+212 5 37 73 73 73",availability:"Lun-Ven 8h-18h",description:"Fraudes en ligne, usurpation d'identit\xe9, chantage"},{type:"Num\xe9ro Vert Violence Femmes",number:"8350",availability:"24h/24",description:"Harc\xe8lement sexuel, violences bas\xe9es sur le genre"}];function u(){return(0,d.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,d.jsx)("div",{className:"bg-gray-50 border-b",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2 py-4 text-sm",children:[(0,d.jsx)("a",{href:"/",className:"text-gray-500 hover:text-mbnb-coral transition-colors",children:"Accueil"}),(0,d.jsx)(h.A,{className:"h-4 w-4 text-gray-400"}),(0,d.jsx)("span",{className:"text-gray-900 font-medium",children:"Signaler un Probl\xe8me"})]})})}),(0,d.jsxs)("div",{className:"relative bg-gradient-to-r from-mbnb-coral to-red-600 text-white",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-30"}),(0,d.jsx)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)(e.A,{className:"h-16 w-16 mx-auto mb-6 text-white"}),(0,d.jsx)("h1",{className:"text-4xl md:text-6xl font-bold mb-6",children:"Signaler un Probl\xe8me"}),(0,d.jsx)("p",{className:"text-xl md:text-2xl mb-8 max-w-4xl mx-auto",children:"Votre s\xe9curit\xe9 et celle de notre communaut\xe9 sont prioritaires. Signalez tout comportement inappropri\xe9 ou probl\xe8me de s\xe9curit\xe9."}),(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-8 text-lg",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl font-bold",children:"24h/24"}),(0,d.jsx)("div",{className:"text-white/80",children:"\xc9quipe mod\xe9ration"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl font-bold",children:"6.2h"}),(0,d.jsx)("div",{className:"text-white/80",children:"Temps r\xe9ponse moyen"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl font-bold",children:"94.8%"}),(0,d.jsx)("div",{className:"text-white/80",children:"Taux r\xe9solution"})]})]})]})})]}),(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,d.jsxs)("div",{className:"text-center mb-16",children:[(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Types de Signalements"}),(0,d.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Choisissez la cat\xe9gorie qui correspond le mieux \xe0 votre probl\xe8me pour un traitement optimis\xe9"})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:n.map(a=>(0,d.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg border border-gray-100 p-8",children:[(0,d.jsxs)("div",{className:"flex items-start space-x-4 mb-6",children:[(0,d.jsx)("div",{className:`bg-${a.color}-100 rounded-full p-3`,children:(0,d.jsx)(a.icon,{className:`h-8 w-8 text-${a.color}-600`})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:a.title}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:`text-xs font-medium px-2 py-1 rounded-full bg-${a.color}-100 text-${a.color}-700`,children:a.priority}),(0,d.jsx)("span",{className:"text-xs text-gray-500",children:a.responseTime})]})]}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:a.description})]})]}),(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsx)("h4",{className:"text-sm font-semibold text-gray-900 mb-3",children:"Exemples typiques:"}),(0,d.jsx)("div",{className:"space-y-2",children:a.examples.slice(0,3).map((b,c)=>(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)(f.A,{className:`h-4 w-4 text-${a.color}-500 mt-1 mr-2 flex-shrink-0`}),(0,d.jsx)("span",{className:"text-sm text-gray-700",children:b})]},c))})]}),(0,d.jsxs)("div",{className:"space-y-2 mb-6",children:[(0,d.jsx)("h4",{className:"text-sm font-semibold text-gray-900",children:"Actions automatiques:"}),a.actions.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)(k.A,{className:"h-4 w-4 text-green-500 mt-1 mr-2 flex-shrink-0"}),(0,d.jsx)("span",{className:"text-sm text-gray-600",children:a})]},b))]}),(0,d.jsx)("button",{className:`w-full py-3 px-6 rounded-lg font-semibold transition-colors bg-${a.color}-600 text-white hover:bg-${a.color}-700`,children:"Signaler ce type de probl\xe8me"})]},a.id))})]}),(0,d.jsx)("div",{className:"bg-gray-50 py-16",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"text-center mb-16",children:[(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Processus de Mod\xe9ration"}),(0,d.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Traitement professionnel en 4 \xe9tapes par notre \xe9quipe de 47 mod\xe9rateurs certifi\xe9s"})]}),(0,d.jsx)("div",{className:"bg-white rounded-2xl shadow-lg border border-gray-100 p-8 mb-12",children:(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:p.map((a,b)=>(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsxs)("div",{className:"relative mb-6",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-mbnb-coral rounded-full flex items-center justify-center mx-auto",children:(0,d.jsx)("span",{className:"text-2xl font-bold text-white",children:a.step})}),(0,d.jsx)("div",{className:`absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${a.automated?"bg-blue-500 text-white":"bg-green-500 text-white"}`,children:a.automated?"A":"H"})]}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:a.title}),(0,d.jsx)("div",{className:"text-sm text-mbnb-coral font-medium mb-3",children:a.duration}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:a.description}),(0,d.jsx)("div",{className:"space-y-1",children:a.actions.map((a,b)=>(0,d.jsxs)("div",{className:"text-xs text-gray-500",children:["• ",a]},b))})]},b))})}),(0,d.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg border border-gray-100 p-8",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-6 text-center",children:"Notre \xc9quipe de Mod\xe9ration"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-mbnb-coral mb-2",children:"47"}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Mod\xe9rateurs actifs"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-mbnb-coral mb-2",children:"24/7"}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Couverture permanente"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-mbnb-coral mb-2",children:"4"}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Langues support\xe9es"})]})]}),(0,d.jsxs)("div",{className:"mt-8 grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-semibold text-gray-900 mb-3",children:"Sp\xe9cialisations:"}),(0,d.jsx)("ul",{className:"space-y-1",children:o.specializations.map((a,b)=>(0,d.jsxs)("li",{className:"text-sm text-gray-600 flex items-center",children:[(0,d.jsx)(k.A,{className:"h-4 w-4 text-green-500 mr-2"}),a]},b))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-semibold text-gray-900 mb-3",children:"Localisation:"}),(0,d.jsx)("ul",{className:"space-y-1",children:o.locations.map((a,b)=>(0,d.jsxs)("li",{className:"text-sm text-gray-600 flex items-center",children:[(0,d.jsx)(k.A,{className:"h-4 w-4 text-blue-500 mr-2"}),a]},b))})]})]})]})]})}),(0,d.jsx)("div",{className:"py-16",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"text-center mb-16",children:[(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Syst\xe8me de Sanctions Gradu\xe9es"}),(0,d.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Sanctions proportionnelles selon la gravit\xe9 des violations et r\xe9cidives"})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:q.map((a,b)=>(0,d.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg border border-gray-100 p-6",children:[(0,d.jsxs)("div",{className:"text-center mb-4",children:[(0,d.jsx)("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${0===b?"bg-yellow-100 text-yellow-800":1===b?"bg-orange-100 text-orange-800":2===b?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:a.level}),(0,d.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:a.duration})]}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-4 text-center",children:a.description}),(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsx)("h4",{className:"text-xs font-semibold text-gray-900 mb-2",children:"D\xe9clencheurs:"}),(0,d.jsx)("ul",{className:"space-y-1",children:a.triggers.slice(0,2).map((a,b)=>(0,d.jsxs)("li",{className:"text-xs text-gray-600 flex items-start",children:[(0,d.jsx)("span",{className:"text-red-400 mr-1",children:"•"}),a]},b))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"text-xs font-semibold text-gray-900 mb-2",children:"Cons\xe9quences:"}),(0,d.jsx)("ul",{className:"space-y-1",children:a.consequences.slice(0,2).map((a,b)=>(0,d.jsxs)("li",{className:"text-xs text-gray-600 flex items-start",children:[(0,d.jsx)(l,{className:"h-3 w-3 text-red-500 mt-0.5 mr-1 flex-shrink-0"}),a]},b))})]})]},b))})]})}),(0,d.jsx)("div",{className:"bg-gray-50 py-16",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"text-center mb-16",children:[(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Statistiques Transparentes 2024"}),(0,d.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Donn\xe9es de mod\xe9ration publiques pour total transparence communautaire"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,d.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg border border-gray-100 p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-6 text-center",children:"R\xe9partition Signalements"}),(0,d.jsx)("div",{className:"space-y-4",children:r.breakdown.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900",children:a.category}),(0,d.jsx)("div",{className:"text-xs text-gray-500",children:a.count})]}),(0,d.jsx)("div",{className:"text-right",children:(0,d.jsxs)("div",{className:"text-lg font-bold text-mbnb-coral",children:[a.percentage,"%"]})})]},b))})]}),(0,d.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg border border-gray-100 p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-6 text-center",children:"Types de Sanctions"}),(0,d.jsx)("div",{className:"space-y-4",children:r.sanctions.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900",children:a.type}),(0,d.jsxs)("div",{className:"text-xs text-gray-500",children:[a.count," cas"]})]}),(0,d.jsx)("div",{className:"text-right",children:(0,d.jsxs)("div",{className:"text-lg font-bold text-mbnb-coral",children:[a.percentage,"%"]})})]},b))})]}),(0,d.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg border border-gray-100 p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-6 text-center",children:"Performance Globale"}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-mbnb-coral mb-1",children:r.totalReports2024}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Signalements trait\xe9s"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-green-600 mb-1",children:r.resolutionRate}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Taux de r\xe9solution"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-1",children:r.averageResponseTime}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Temps r\xe9ponse moyen"})]})]})]})]})]})}),(0,d.jsx)("div",{className:"py-16",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"text-center mb-16",children:[(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Cadre L\xe9gal et Partenariats"}),(0,d.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Collaboration active avec les autorit\xe9s marocaines pour protection maximale"})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:s.map((a,b)=>(0,d.jsxs)("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100 p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-2",children:a.law}),(0,d.jsxs)("div",{className:"grid grid-cols-1 gap-3",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Domaine:"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:a.scope})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Application:"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:a.application})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Contact:"}),(0,d.jsx)("p",{className:"text-sm text-blue-600",children:a.contact})]})]})]},b))})]})}),(0,d.jsx)("div",{className:"bg-gray-50 py-16",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"text-center mb-16",children:[(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Contacts d'Urgence"}),(0,d.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Num\xe9ros d'urgence selon la gravit\xe9 de la situation"})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:t.map((a,b)=>(0,d.jsxs)("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100 p-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,d.jsx)("div",{className:"bg-red-100 rounded-full p-3",children:(0,d.jsx)(f.A,{className:"h-8 w-8 text-red-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-bold text-gray-900",children:a.type}),(0,d.jsx)("div",{className:"text-2xl font-bold text-red-600",children:a.number})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Disponibilit\xe9:"}),(0,d.jsx)("span",{className:"text-sm text-gray-600 ml-2",children:a.availability})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Usage:"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:a.description})]})]})]},b))})]})}),(0,d.jsx)("div",{className:"bg-mbnb-coral text-white py-16",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,d.jsx)(e.A,{className:"h-16 w-16 mx-auto mb-6"}),(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-bold mb-6",children:"Votre S\xe9curit\xe9, Notre Priorit\xe9"}),(0,d.jsx)("p",{className:"text-xl mb-8 opacity-90",children:"N'h\xe9sitez jamais \xe0 signaler un comportement inappropri\xe9. Notre \xe9quipe est l\xe0 pour prot\xe9ger notre communaut\xe9 24h/24."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsxs)("a",{href:"/report-concern/form",className:"inline-flex items-center px-8 py-4 bg-white text-mbnb-coral font-semibold rounded-lg hover:bg-gray-100 transition-colors",children:[(0,d.jsx)(f.A,{className:"h-5 w-5 mr-2"}),"Faire un Signalement"]}),(0,d.jsxs)("a",{href:"/trust-safety",className:"inline-flex items-center px-8 py-4 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-mbnb-coral transition-colors",children:[(0,d.jsx)(g.A,{className:"h-5 w-5 mr-2"}),"Centre de Confiance"]})]}),(0,d.jsx)("p",{className:"text-sm mt-6 opacity-75",children:"R\xe9ponse sous 6h en moyenne • Traitement confidentiel • Protection garantie"})]})})]})}},80408:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},87032:()=>{},92176:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))})},99431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.default,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(73653),e=c(97714),f=c(85250),g=c(37587),h=c(22369),i=c(1889),j=c(96232),k=c(22841),l=c(46537),m=c(46027),n=c(78559),o=c(75928),p=c(19374),q=c(65971),r=c(261),s=c(79898),t=c(32967),u=c(26713),v=c(40139),w=c(14248),x=c(59580),y=c(57749),z=c(53123),A=c(89745),B=c(86439),C=c(96133),D=c(18283),E=c(39818),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["report-concern",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,70376)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/report-concern/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,56035)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,74827)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/error.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,96133)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/global-error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,72993)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,15034,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,54693,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/report-concern/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/report-concern/page",pathname:"/report-concern",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",relativeProjectDir:""});async function K(a,b,d){var F;let L="/report-concern/page";"/index"===L&&(L="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await J.prepare(a,b,{srcPage:L,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(L),{isOnDemandRevalidate:ah}=O,ai=J.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(F=$.routes[ag]??$.dynamicRoutes[ag])?void 0:F.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===J.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&J.isDev&&(aB=aa),J.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...D,tree:G,pages:H,GlobalError:C.default,handler:K,routeModule:J,__next_app__:I};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:L,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=J.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:J,page:L,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),J.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!J.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===J.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await J.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await J.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!J.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&E.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:L,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[4635,2922,4367],()=>b(b.s=99431));module.exports=c})();
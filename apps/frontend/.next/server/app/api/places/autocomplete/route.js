(()=>{var a={};a.id=881,a.ids=[881],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},36474:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>D,patchFetch:()=>C,routeModule:()=>y,serverHooks:()=>B,workAsyncStorage:()=>z,workUnitAsyncStorage:()=>A});var d={};c.r(d),c.d(d,{POST:()=>w});var e=c(26421),f=c(97714),g=c(85681),h=c(22369),i=c(37587),j=c(261),k=c(46537),l=c(3463),m=c(1889),n=c(85250),o=c(23620),p=c(29790),q=c(67876),r=c(57749),s=c(86439),t=c(40139),u=c(53444);let v=new Map;async function w(a){try{let{input:b,language:c="fr",types:d=["(cities)"],componentRestrictions:e}=await a.json();if(!b||"string"!=typeof b)return u.NextResponse.json({error:"Input requis"},{status:400});let f=`${b}-${c}-${JSON.stringify(d)}-${JSON.stringify(e)}`,g=v.get(f);if(g&&Date.now()-g.timestamp<6e4)return u.NextResponse.json(g.data);let h=process.env.NEXT_PUBLIC_GOOGLE_PLACES_API_KEY;if(!h)return console.warn("Google Places API key non configur\xe9e, utilisation des donn\xe9es locales"),u.NextResponse.json({predictions:x(b,c),status:"OK"});let i=new URL("https://maps.googleapis.com/maps/api/place/autocomplete/json");i.searchParams.append("input",b),i.searchParams.append("key",h),i.searchParams.append("language",c),d&&d.length>0&&i.searchParams.append("types",d.join("|")),e?.country&&i.searchParams.append("components",`country:${e.country}`);let j=await fetch(i.toString());if(!j.ok)throw Error(`Google Places API error: ${j.status}`);let k=await j.json();if(v.set(f,{data:k,timestamp:Date.now()}),v.size>100){let a=Array.from(v.entries()).sort((a,b)=>a[1].timestamp-b[1].timestamp)[0][0];v.delete(a)}return u.NextResponse.json(k)}catch(a){return console.error("Erreur API Places:",a),u.NextResponse.json({predictions:x("","fr"),status:"ERROR"})}}function x(a,b){let c=[{id:"marrakech",name:{fr:"Marrakech",ar:"مراكش",en:"Marrakech",es:"Marrakech",zh:"马拉喀什",ru:"Марракеш"},description:{fr:"La ville rouge",ar:"المدينة الحمراء",en:"The Red City",es:"La ciudad roja",zh:"红色之城",ru:"Красный город"}},{id:"casablanca",name:{fr:"Casablanca",ar:"الدار البيضاء",en:"Casablanca",es:"Casablanca",zh:"卡萨布兰卡",ru:"Касабланка"},description:{fr:"M\xe9tropole \xe9conomique du Maroc",ar:"العاصمة الاقتصادية للمغرب",en:"Economic capital of Morocco",es:"Capital econ\xf3mica de Marruecos",zh:"摩洛哥经济首都",ru:"Экономическая столица Марокко"}},{id:"fes",name:{fr:"F\xe8s",ar:"فاس",en:"Fez",es:"Fez",zh:"非斯",ru:"Фес"},description:{fr:"Capitale spirituelle et culturelle",ar:"العاصمة الروحية والثقافية",en:"Spiritual and cultural capital",es:"Capital espiritual y cultural",zh:"精神与文化之都",ru:"Духовная и культурная столица"}},{id:"chefchaouen",name:{fr:"Chefchaouen",ar:"شفشاون",en:"Chefchaouen",es:"Chefchaouen",zh:"舍夫沙万",ru:"Шефшауэн"},description:{fr:"La perle bleue du Rif",ar:"اللؤلؤة الزرقاء للريف",en:"The Blue Pearl of the Rif",es:"La perla azul del Rif",zh:"里夫的蓝色明珠",ru:"Голубая жемчужина Рифа"}},{id:"essaouira",name:{fr:"Essaouira",ar:"الصويرة",en:"Essaouira",es:"Essaouira",zh:"索维拉",ru:"Эс-Сувейра"},description:{fr:"Cit\xe9 des vents",ar:"مدينة الرياح",en:"City of Winds",es:"Ciudad de los vientos",zh:"风之城",ru:"Город ветров"}},{id:"agadir",name:{fr:"Agadir",ar:"أكادير",en:"Agadir",es:"Agadir",zh:"阿加迪尔",ru:"Агадир"},description:{fr:"Station baln\xe9aire moderne",ar:"منتجع ساحلي حديث",en:"Modern beach resort",es:"Moderno balneario",zh:"现代海滩度假胜地",ru:"Современный пляжный курорт"}},{id:"rabat",name:{fr:"Rabat",ar:"الرباط",en:"Rabat",es:"Rabat",zh:"拉巴特",ru:"Рабат"},description:{fr:"Capitale du Maroc",ar:"عاصمة المغرب",en:"Capital of Morocco",es:"Capital de Marruecos",zh:"摩洛哥首都",ru:"Столица Марокко"}},{id:"tanger",name:{fr:"Tanger",ar:"طنجة",en:"Tangier",es:"T\xe1nger",zh:"丹吉尔",ru:"Танжер"},description:{fr:"Porte de l'Afrique",ar:"بوابة إفريقيا",en:"Gateway to Africa",es:"Puerta de \xc1frica",zh:"非洲之门",ru:"Ворота в Африку"}},{id:"merzouga",name:{fr:"Merzouga",ar:"مرزوكة",en:"Merzouga",es:"Merzouga",zh:"梅尔祖加",ru:"Мерзуга"},description:{fr:"Porte du d\xe9sert du Sahara",ar:"بوابة الصحراء الكبرى",en:"Gateway to the Sahara Desert",es:"Puerta al desierto del Sahara",zh:"撒哈拉沙漠之门",ru:"Ворота в пустыню Сахара"}},{id:"ouarzazate",name:{fr:"Ouarzazate",ar:"ورزازات",en:"Ouarzazate",es:"Ouarzazate",zh:"瓦尔扎扎特",ru:"Уарзазат"},description:{fr:"Hollywood du Maroc",ar:"هوليوود المغرب",en:"Hollywood of Morocco",es:"Hollywood de Marruecos",zh:"摩洛哥好莱坞",ru:"Голливуд Марокко"}}],d=["fr","ar","en","es","zh","ru"].includes(b)?b:"fr";return(a?c.filter(b=>b.name[d].toLowerCase().includes(a.toLowerCase())||b.description[d].toLowerCase().includes(a.toLowerCase())):c).map(a=>({description:`${a.name[d]}, ${a.description[d]}, Maroc`,place_id:`local_${a.id}`,structured_formatting:{main_text:a.name[d],secondary_text:`${a.description[d]}, Maroc`},types:["locality","political"]}))}let y=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/places/autocomplete/route",pathname:"/api/places/autocomplete",filename:"route",bundlePath:"app/api/places/autocomplete/route"},distDir:".next",relativeProjectDir:"",resolvedPagePath:"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/api/places/autocomplete/route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:z,workUnitAsyncStorage:A,serverHooks:B}=y;function C(){return(0,g.patchFetch)({workAsyncStorage:z,workUnitAsyncStorage:A})}async function D(a,b,c){var d;let e="/api/places/autocomplete/route";"/index"===e&&(e="/");let g=await y.prepare(a,b,{srcPage:e,multiZoneDraftMode:!1});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:z,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(z.dynamicRoutes[E]||z.routes[D]);if(F&&!x){let a=!!z.routes[D],b=z.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||y.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===y.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:z,renderOpts:{experimental:{cacheComponents:!!w.experimental.cacheComponents,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>y.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>y.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await y.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await y.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:z,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await y.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},80408:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},87032:()=>{}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4635,8825],()=>b(b.s=36474));module.exports=c})();
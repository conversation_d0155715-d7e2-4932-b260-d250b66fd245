1:"$Sreact.fragment"
2:I[31201,["9249","static/chunks/8bb4d8db-dfbedecd1ce1b10b.js","9664","static/chunks/9664-dcc323304d96ebba.js","6799","static/chunks/6799-5700cd15871c3a68.js","3558","static/chunks/3558-7762df6d0b7818d5.js","7638","static/chunks/7638-a456914beab47d3a.js","8509","static/chunks/8509-cd16161b0a738228.js","1470","static/chunks/1470-0ec85a2f49d662b6.js","7177","static/chunks/app/layout-15771846853b158e.js"],"Providers"]
3:I[41470,["9249","static/chunks/8bb4d8db-dfbedecd1ce1b10b.js","9664","static/chunks/9664-dcc323304d96ebba.js","6799","static/chunks/6799-5700cd15871c3a68.js","3558","static/chunks/3558-7762df6d0b7818d5.js","7638","static/chunks/7638-a456914beab47d3a.js","8509","static/chunks/8509-cd16161b0a738228.js","1470","static/chunks/1470-0ec85a2f49d662b6.js","7177","static/chunks/app/layout-15771846853b158e.js"],"default"]
4:I[85341,[],""]
5:I[81869,["8039","static/chunks/app/error-9c03368aa6cf1490.js"],"default"]
6:I[90025,[],""]
7:I[19664,["9664","static/chunks/9664-dcc323304d96ebba.js","1392","static/chunks/app/help/account/page-a243b30d8237f2b7.js"],""]
11:I[56065,["4219","static/chunks/app/global-error-74438679bdbb3441.js"],"default"]
:HL["/_next/static/media/5aae3a1c1074c5e1-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/media/8c2fd50d66d22a18-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/media/904be59b21bd51cb-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/media/da6e5417d357d163-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/media/dd5f2241e050216b-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/media/eaead17c7dbfcd5d-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/css/c963da8451813b8e.css","style"]
:HL["/_next/static/css/52e7035135458db8.css","style"]
0:{"P":null,"b":"zGL_M_ILxIICrvP-h80z7","p":"","c":["","help","account"],"i":false,"f":[[["",{"children":["help",{"children":["account",{"children":["__PAGE__",{}]}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/c963da8451813b8e.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/52e7035135458db8.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"fr","dir":"ltr","className":"__variable_660b3b __variable_d59ba8 __variable_338cf8","suppressHydrationWarning":true,"children":[["$","head",null,{"children":[["$","link",null,{"rel":"preconnect","href":"https://fonts.googleapis.com"}],["$","link",null,{"rel":"preconnect","href":"https://fonts.gstatic.com","crossOrigin":"anonymous"}],["$","link",null,{"rel":"preconnect","href":"https://cdn.mbnb.ma"}],["$","link",null,{"rel":"preconnect","href":"https://api.mbnb.ma"}],["$","link",null,{"rel":"dns-prefetch","href":"https://www.google-analytics.com"}],["$","link",null,{"rel":"dns-prefetch","href":"https://www.googletagmanager.com"}],["$","script",null,{"type":"application/ld+json","dangerouslySetInnerHTML":{"__html":"{\"@context\":\"https://schema.org\",\"@type\":\"Organization\",\"name\":\"Mbnb\",\"url\":\"https://www.mbnb.ma\",\"logo\":\"https://cdn.mbnb.ma/logo.png\",\"sameAs\":[\"https://www.facebook.com/mbnb.ma\",\"https://www.instagram.com/mbnb.ma\",\"https://twitter.com/mbnb_ma\",\"https://www.linkedin.com/company/mbnb-ma\"],\"contactPoint\":{\"@type\":\"ContactPoint\",\"telephone\":\"+212-5XX-XXXXXX\",\"contactType\":\"customer service\",\"availableLanguage\":[\"French\",\"Arabic\",\"English\",\"Spanish\"],\"areaServed\":\"MA\"}}"}}]]}],["$","body",null,{"className":"min-h-screen bg-white font-sans antialiased","children":["$","$L2",null,{"children":[["$","div",null,{"className":"flex flex-col min-h-screen","children":[["$","$L3",null,{}],["$","main",null,{"className":"flex-grow","children":["$","$L4",null,{"parallelRouterKey":"children","error":"$5","errorStyles":[],"errorScripts":[],"template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","div",null,{"className":"min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 flex items-center justify-center px-4","children":["$","div",null,{"className":"max-w-2xl w-full text-center","children":[["$","div",null,{"className":"relative mb-8","children":[["$","h1",null,{"className":"text-[150px] md:text-[200px] font-bold text-gray-200 select-none","children":"404"}],["$","div",null,{"className":"absolute inset-0 flex items-center justify-center","children":["$","div",null,{"className":"bg-white rounded-2xl shadow-xl p-8","children":[["$","p",null,{"className":"text-6xl mb-4","children":"🏜️"}],["$","p",null,{"className":"text-xl font-semibold text-gray-800","children":"Page Introuvable"}]]}]}]]}],["$","h2",null,{"className":"text-2xl md:text-3xl font-bold text-gray-900 mb-4","children":"Oops! Cette page s'est perdue dans le Sahara"}],["$","p",null,{"className":"text-lg text-gray-600 mb-8 max-w-lg mx-auto","children":"La page que vous recherchez n'existe pas ou a été déplacée. Peut-être qu'un chameau l'a emportée dans le désert..."}],["$","div",null,{"className":"bg-white rounded-2xl shadow-lg p-6 mb-8 max-w-lg mx-auto","children":[["$","p",null,{"className":"text-sm font-semibold text-gray-700 mb-3","children":"Suggestions populaires :"}],["$","div",null,{"className":"flex flex-wrap gap-2 justify-center","children":[["$","$L7",null,{"href":"/search?location=Marrakech","className":"px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-medium text-gray-700 transition-colors","children":"Riads à Marrakech"}],["$","$L7",null,{"href":"/search?location=Essaouira","className":"px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-medium text-gray-700 transition-colors","children":"Maisons à Essaouira"}],["$","$L7",null,{"href":"/search?location=Chefchaouen","className":"px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-medium text-gray-700 transition-colors","children":"Hébergements à Chefchaouen"}]]}]]}],["$","div",null,{"className":"flex gap-4 justify-center","children":["$L8","$L9"]}]]}]}],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],"$La"]}],"$Lb","$Lc"]}]}]]}]]}],{"children":["help","$Ld",{"children":["account","$Le",{"children":["__PAGE__","$Lf",{},null,false]},null,false]},null,false]},null,false],"$L10",false]],"m":"$undefined","G":["$11",[]],"s":false,"S":true}
17:I[93558,["9249","static/chunks/8bb4d8db-dfbedecd1ce1b10b.js","9664","static/chunks/9664-dcc323304d96ebba.js","6799","static/chunks/6799-5700cd15871c3a68.js","3558","static/chunks/3558-7762df6d0b7818d5.js","7638","static/chunks/7638-a456914beab47d3a.js","8509","static/chunks/8509-cd16161b0a738228.js","1470","static/chunks/1470-0ec85a2f49d662b6.js","7177","static/chunks/app/layout-15771846853b158e.js"],"Toaster"]
18:I[92576,["9249","static/chunks/8bb4d8db-dfbedecd1ce1b10b.js","9664","static/chunks/9664-dcc323304d96ebba.js","6799","static/chunks/6799-5700cd15871c3a68.js","3558","static/chunks/3558-7762df6d0b7818d5.js","7638","static/chunks/7638-a456914beab47d3a.js","8509","static/chunks/8509-cd16161b0a738228.js","1470","static/chunks/1470-0ec85a2f49d662b6.js","7177","static/chunks/app/layout-15771846853b158e.js"],"Analytics"]
24:I[15104,[],"ViewportBoundary"]
26:I[15104,[],"MetadataBoundary"]
27:"$Sreact.suspense"
8:["$","$L7",null,{"href":"/","className":"inline-flex items-center gap-2 px-6 py-3 bg-white hover:bg-gray-50 text-gray-700 rounded-lg font-medium transition-all transform hover:scale-105 shadow-lg","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-5 h-5","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"}]]}],"Retour à l'accueil"]}]
9:["$","$L7",null,{"href":"/search","className":"inline-flex items-center gap-2 px-6 py-3 bg-mbnb-coral hover:bg-mbnb-coral-dark text-white rounded-lg font-medium transition-all transform hover:scale-105 shadow-lg","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-5 h-5","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}]]}],"Explorer les hébergements"]}]
a:["$","footer",null,{"className":"bg-gray-900 text-gray-300","children":[["$","div",null,{"className":"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8","children":["$","div",null,{"className":"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6","children":[["$","div",null,{"children":[["$","h3",null,{"className":"text-white font-semibold mb-3","children":"À propos"}],["$","ul",null,{"className":"space-y-1.5 text-sm","children":[["$","li",null,{"children":["$","$L7",null,{"href":"/about","className":"hover:text-mbnb-coral transition-colors","children":"Qui sommes-nous"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/how-it-works","className":"hover:text-mbnb-coral transition-colors","children":"Comment ça marche"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/newsroom","className":"hover:text-mbnb-coral transition-colors","children":"Actualités"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/investors","className":"hover:text-mbnb-coral transition-colors","children":"Investisseurs"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/careers","className":"hover:text-mbnb-coral transition-colors","children":"Carrières"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/sustainability","className":"hover:text-mbnb-coral transition-colors","children":"Durabilité"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/diversity","className":"hover:text-mbnb-coral transition-colors","children":"Diversité"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/gift-cards","className":"hover:text-mbnb-coral transition-colors","children":"Cartes cadeaux"}]}]]}]]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-white font-semibold mb-3","children":"Support"}],["$","ul",null,{"className":"space-y-1.5 text-sm","children":[["$","li",null,{"children":["$","$L7",null,{"href":"/help","className":"hover:text-mbnb-coral transition-colors","children":"Centre d'aide"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/contact","className":"hover:text-mbnb-coral transition-colors","children":"Nous contacter"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/safety","className":"hover:text-mbnb-coral transition-colors","children":"Sécurité"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/cancellation","className":"hover:text-mbnb-coral transition-colors","children":"Annulation"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/trust-safety","className":"hover:text-mbnb-coral transition-colors","children":"Confiance"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/accessibility","className":"hover:text-mbnb-coral transition-colors","children":"Accessibilité"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/associates","className":"hover:text-mbnb-coral transition-colors","children":"Partenaires"}]}]]}]]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-white font-semibold mb-3","children":"Hôtes"}],["$","ul",null,{"className":"space-y-1.5 text-sm","children":[["$","li",null,{"children":["$","$L7",null,{"href":"/host/homes","className":"hover:text-mbnb-coral transition-colors","children":"Héberger"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/host/experiences","className":"hover:text-mbnb-coral transition-colors","children":"Expériences"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/host/resources","className":"hover:text-mbnb-coral transition-colors","children":"Ressources"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/host/responsible","className":"hover:text-mbnb-coral transition-colors","children":"Responsable"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/host/insurance","className":"hover:text-mbnb-coral transition-colors","children":"Assurance"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/host/academy","className":"hover:text-mbnb-coral transition-colors","children":"Académie"}]}]]}]]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-white font-semibold mb-3","children":"Destinations"}],["$","ul",null,{"className":"space-y-1.5 text-sm","children":[["$","li",null,{"children":["$","$L7",null,{"href":"/destinations/marrakech","className":"hover:text-mbnb-coral transition-colors","children":"Marrakech"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/destinations/casablanca","className":"hover:text-mbnb-coral transition-colors","children":"Casablanca"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/destinations/fes","className":"hover:text-mbnb-coral transition-colors","children":"Fès"}]}],["$","li",null,{"children":["$","$L7",null,{"href":"/destinations/chefchaouen","className":"hover:text-mbnb-coral transition-colors","children":"Chefchaouen"}]}],"$L12","$L13","$L14"]}]]}],"$L15"]}]}],"$L16"]}]
b:["$","$L17",null,{"position":"top-right","toastOptions":{"duration":4000,"style":{"background":"#363636","color":"#fff","borderRadius":"8px"},"success":{"iconTheme":{"primary":"#22C55E","secondary":"#fff"}},"error":{"iconTheme":{"primary":"#EF4444","secondary":"#fff"}}}}]
c:["$","$L18",null,{}]
d:["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}]
e:["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}]
f:["$","$1","c",{"children":[["$","main",null,{"className":"min-h-screen py-16","children":["$","div",null,{"className":"container-mbnb","children":[["$","div",null,{"className":"flex items-center gap-2 text-sm text-gray-600 mb-8","children":[["$","$L7",null,{"href":"/help","className":"hover:text-mbnb-coral transition-colors","children":"Centre d'aide"}],["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"m8.25 4.5 7.5 7.5-7.5 7.5"}]]}],["$","span",null,{"className":"text-gray-900","children":"Compte utilisateur"}]]}],["$","div",null,{"className":"text-center mb-12","children":[["$","div",null,{"className":"w-16 h-16 bg-mbnb-coral/10 rounded-full flex items-center justify-center mx-auto mb-4","children":["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-8 h-8 text-mbnb-coral","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}]]}]}],["$","h1",null,{"className":"heading-1 text-gray-900 mb-4","children":"Gestion de compte"}],["$","p",null,{"className":"text-xl text-gray-600 max-w-2xl mx-auto","children":"Configurez votre profil, sécurité et préférences pour une expérience Mbnb optimale"}]]}],["$","div",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl font-semibold text-gray-900 mb-6 text-center","children":"Types de comptes Mbnb"}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-3 gap-6","children":[["$","div","Voyageur",{"className":"bg-white border rounded-xl p-6","children":[["$","div",null,{"className":"text-center mb-4","children":[["$","div",null,{"className":"text-4xl mb-2","children":"✈️"}],["$","h3",null,{"className":"text-lg font-semibold","children":"Voyageur"}]]}],["$","ul",null,{"className":"space-y-2","children":[["$","li","Réserver hébergements et activités",{"className":"flex items-start gap-2 text-sm text-gray-600","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-3 h-3 mt-1 text-mbnb-coral flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"m8.25 4.5 7.5 7.5-7.5 7.5"}]]}],["$","span",null,{"children":"Réserver hébergements et activités"}]]}],["$","li","Messagerie sécurisée avec hôtes",{"className":"flex items-start gap-2 text-sm text-gray-600","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-3 h-3 mt-1 text-mbnb-coral flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"m8.25 4.5 7.5 7.5-7.5 7.5"}]]}],["$","span",null,{"children":"Messagerie sécurisée avec hôtes"}]]}],["$","li","Historique complet des séjours",{"className":"flex items-start gap-2 text-sm text-gray-600","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-3 h-3 mt-1 text-mbnb-coral flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"m8.25 4.5 7.5 7.5-7.5 7.5"}]]}],["$","span",null,{"children":"Historique complet des séjours"}]]}],["$","li","Programme fidélité Mbnb Plus",{"className":"flex items-start gap-2 text-sm text-gray-600","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-3 h-3 mt-1 text-mbnb-coral flex-shrink-0","children":[null,"$L19"]}],"$L1a"]}],"$L1b"]}]]}],"$L1c","$L1d"]}]]}],"$L1e","$L1f","$L20","$L21","$L22"]}]}],null,"$L23"]}]
10:["$","$1","h",{"children":[null,[["$","$L24",null,{"children":"$L25"}],["$","meta",null,{"name":"next-size-adjust","content":""}]],["$","$L26",null,{"children":["$","div",null,{"hidden":true,"children":["$","$27",null,{"fallback":null,"children":"$L28"}]}]}]]}]
37:I[15104,[],"OutletBoundary"]
39:I[94777,[],"AsyncMetadataOutlet"]
12:["$","li",null,{"children":["$","$L7",null,{"href":"/destinations/essaouira","className":"hover:text-mbnb-coral transition-colors","children":"Essaouira"}]}]
13:["$","li",null,{"children":["$","$L7",null,{"href":"/destinations/rabat","className":"hover:text-mbnb-coral transition-colors","children":"Rabat"}]}]
14:["$","li",null,{"children":["$","$L7",null,{"href":"/destinations/all","className":"hover:text-mbnb-coral transition-colors","children":"Toutes"}]}]
15:["$","div",null,{"children":[["$","h3",null,{"className":"text-white font-semibold mb-3","children":"App Mobile"}],["$","div",null,{"className":"space-y-2 mb-4","children":[["$","$L7",null,{"href":"https://apps.apple.com/app/mbnb-location-vacances-maroc/id1234567890","className":"block","children":["$","div",null,{"className":"bg-gray-800 hover:bg-gray-700 rounded px-3 py-1.5 flex items-center gap-2 transition-colors","children":[["$","svg",null,{"className":"w-4 h-4","viewBox":"0 0 24 24","children":["$","path",null,{"fill":"#FFFFFF","d":"M18.71 19.5C17.88 20.74 17 21.95 15.66 21.97C14.32 22 13.89 21.18 12.37 21.18C10.84 21.18 10.37 21.95 9.09997 22C7.78997 22.05 6.79997 20.68 5.95997 19.47C4.24997 17 2.93997 12.45 4.69997 9.39C5.56997 7.87 7.12997 6.91 8.81997 6.88C10.1 6.86 11.32 7.75 12.11 7.75C12.89 7.75 14.37 6.68 15.92 6.84C16.57 6.87 18.39 7.1 19.56 8.82C19.47 8.88 17.39 10.1 17.41 12.63C17.44 15.65 20.06 16.66 20.09 16.67C20.06 16.74 19.67 18.11 18.71 19.5ZM13 3.5C13.73 2.67 14.94 2.04 15.94 2C16.07 3.17 15.6 4.35 14.9 5.19C14.21 6.04 13.07 6.7 11.95 6.61C11.8 5.46 12.36 4.26 13 3.5Z"}]}],["$","span",null,{"className":"text-xs","children":"App Store"}]]}]}],["$","$L7",null,{"href":"https://play.google.com/store/apps/details?id=com.mbnb.app","className":"block","children":["$","div",null,{"className":"bg-gray-800 hover:bg-gray-700 rounded px-3 py-1.5 flex items-center gap-2 transition-colors","children":[["$","svg",null,{"className":"w-4 h-4","viewBox":"0 0 24 24","children":[["$","path",null,{"fill":"#4285F4","d":"M3 20.5V3.5C3 2.91 3.34 2.39 3.84 2.15L13.69 12L3.84 21.85C3.34 21.6 3 21.09 3 20.5Z"}],["$","path",null,{"fill":"#34A853","d":"M16.81 15.12L6.05 21.34L14.54 12.85L16.81 15.12Z"}],["$","path",null,{"fill":"#FBBC05","d":"M20.16 10.81C20.5 11.08 20.75 11.5 20.75 12C20.75 12.5 20.5 12.92 20.16 13.19L17.89 14.5L15.39 12L17.89 9.5L20.16 10.81Z"}],["$","path",null,{"fill":"#EA4335","d":"M6.05 2.66L16.81 8.88L14.54 11.15L6.05 2.66Z"}]]}],["$","span",null,{"className":"text-xs","children":"Google Play"}]]}]}]]}],["$","h3",null,{"className":"text-white font-semibold mb-2 text-sm","children":"Suivez-nous"}],["$","div",null,{"className":"flex gap-2","children":[["$","$L7",null,{"href":"https://facebook.com/mbnb","className":"bg-white hover:bg-gray-100 p-1.5 rounded transition-colors","children":["$","svg",null,{"className":"w-4 h-4","viewBox":"0 0 24 24","children":["$","path",null,{"fill":"#1877F2","d":"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"}]}]}],["$","$L7",null,{"href":"https://x.com/mbnb","className":"bg-black hover:bg-gray-800 p-1.5 rounded transition-colors","children":["$","svg",null,{"className":"w-4 h-4","viewBox":"0 0 24 24","children":["$","path",null,{"fill":"#FFFFFF","d":"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"}]}]}],["$","$L7",null,{"href":"https://instagram.com/mbnb","className":"bg-white hover:bg-gray-100 p-1.5 rounded transition-colors","children":["$","svg",null,{"className":"w-4 h-4","viewBox":"0 0 24 24","children":[["$","defs",null,{"children":["$","radialGradient",null,{"id":"instagram-gradient","cx":"0.3","cy":"1","r":"1.4","children":[["$","stop",null,{"offset":"0%","stopColor":"#FED576"}],["$","stop",null,{"offset":"26%","stopColor":"#F47133"}],["$","stop",null,{"offset":"61%","stopColor":"#BC3081"}],["$","stop",null,{"offset":"100%","stopColor":"#4F5BD5"}]]}]}],["$","path",null,{"fill":"url(#instagram-gradient)","d":"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zM5.838 12a6.162 6.162 0 1112.324 0 6.162 6.162 0 01-12.324 0zM12 16a4 4 0 110-8 4 4 0 010 8zm4.965-10.405a1.44 1.44 0 112.881.001 1.44 1.44 0 01-2.881-.001z"}]]}]}],"$L29","$L2a"]}]]}]
16:["$","div",null,{"className":"border-t border-gray-800","children":["$","div",null,{"className":"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4","children":["$","div",null,{"className":"flex flex-col lg:flex-row justify-between items-center gap-3","children":[["$","div",null,{"className":"flex items-center gap-2","children":[["$","span",null,{"className":"text-xl font-bold text-mbnb-coral","children":"Mbnb"}],["$","span",null,{"className":"text-xs","children":["© ",2025," Mbnb, Inc."]}]]}],["$","div",null,{"className":"flex flex-wrap gap-3 text-xs","children":[["$","$L7",null,{"href":"/privacy","className":"hover:text-mbnb-coral transition-colors","children":"Confidentialité"}],["$","span",null,{"className":"text-gray-600","children":"·"}],["$","$L7",null,{"href":"/terms","className":"hover:text-mbnb-coral transition-colors","children":"Conditions"}],["$","span",null,{"className":"text-gray-600","children":"·"}],["$","$L7",null,{"href":"/legal","className":"hover:text-mbnb-coral transition-colors","children":"Mentions légales"}],["$","span",null,{"className":"text-gray-600","children":"·"}],["$","$L7",null,{"href":"/cookies","className":"hover:text-mbnb-coral transition-colors","children":"Cookies"}]]}],["$","div",null,{"className":"flex items-center gap-4 text-xs","children":[["$","div",null,{"className":"flex items-center gap-1","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-3 h-3","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}],["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"}]]}],["$","span",null,{"children":"Maroc"}]]}],["$","div",null,{"className":"flex items-center gap-1","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-3 h-3","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}]]}],["$","span",null,{"children":"<EMAIL>"}]]}]]}]]}]}]}]
19:["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"m8.25 4.5 7.5 7.5-7.5 7.5"}]
1a:["$","span",null,{"children":"Programme fidélité Mbnb Plus"}]
1b:["$","li","Listes de favoris illimitées",{"className":"flex items-start gap-2 text-sm text-gray-600","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-3 h-3 mt-1 text-mbnb-coral flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"m8.25 4.5 7.5 7.5-7.5 7.5"}]]}],["$","span",null,{"children":"Listes de favoris illimitées"}]]}]
1c:["$","div","Hôte",{"className":"bg-white border rounded-xl p-6","children":[["$","div",null,{"className":"text-center mb-4","children":[["$","div",null,{"className":"text-4xl mb-2","children":"🏠"}],["$","h3",null,{"className":"text-lg font-semibold","children":"Hôte"}]]}],["$","ul",null,{"className":"space-y-2","children":[["$","li","Publier jusqu'à 10 annonces",{"className":"flex items-start gap-2 text-sm text-gray-600","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-3 h-3 mt-1 text-mbnb-coral flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"m8.25 4.5 7.5 7.5-7.5 7.5"}]]}],["$","span",null,{"children":"Publier jusqu'à 10 annonces"}]]}],["$","li","Calendrier synchronisé",{"className":"flex items-start gap-2 text-sm text-gray-600","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-3 h-3 mt-1 text-mbnb-coral flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"m8.25 4.5 7.5 7.5-7.5 7.5"}]]}],["$","span",null,{"children":"Calendrier synchronisé"}]]}],["$","li","Tarification dynamique IA",{"className":"flex items-start gap-2 text-sm text-gray-600","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-3 h-3 mt-1 text-mbnb-coral flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"m8.25 4.5 7.5 7.5-7.5 7.5"}]]}],["$","span",null,{"children":"Tarification dynamique IA"}]]}],["$","li","Statistiques détaillées",{"className":"flex items-start gap-2 text-sm text-gray-600","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-3 h-3 mt-1 text-mbnb-coral flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"m8.25 4.5 7.5 7.5-7.5 7.5"}]]}],["$","span",null,{"children":"Statistiques détaillées"}]]}],["$","li","Outils de gestion pro",{"className":"flex items-start gap-2 text-sm text-gray-600","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-3 h-3 mt-1 text-mbnb-coral flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"m8.25 4.5 7.5 7.5-7.5 7.5"}]]}],["$","span",null,{"children":"Outils de gestion pro"}]]}]]}]]}]
1d:["$","div","Pro",{"className":"bg-white border rounded-xl p-6","children":[["$","div",null,{"className":"text-center mb-4","children":[["$","div",null,{"className":"text-4xl mb-2","children":"💼"}],["$","h3",null,{"className":"text-lg font-semibold","children":"Pro"}]]}],["$","ul",null,{"className":"space-y-2","children":[["$","li","Annonces illimitées",{"className":"flex items-start gap-2 text-sm text-gray-600","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-3 h-3 mt-1 text-mbnb-coral flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"m8.25 4.5 7.5 7.5-7.5 7.5"}]]}],["$","span",null,{"children":"Annonces illimitées"}]]}],["$","li","API pour Channel Manager",{"className":"flex items-start gap-2 text-sm text-gray-600","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-3 h-3 mt-1 text-mbnb-coral flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"m8.25 4.5 7.5 7.5-7.5 7.5"}]]}],["$","span",null,{"children":"API pour Channel Manager"}]]}],["$","li","Facturation entreprise",{"className":"flex items-start gap-2 text-sm text-gray-600","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-3 h-3 mt-1 text-mbnb-coral flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"m8.25 4.5 7.5 7.5-7.5 7.5"}]]}],["$","span",null,{"children":"Facturation entreprise"}]]}],["$","li","Account Manager dédié",{"className":"flex items-start gap-2 text-sm text-gray-600","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-3 h-3 mt-1 text-mbnb-coral flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"m8.25 4.5 7.5 7.5-7.5 7.5"}]]}],["$","span",null,{"children":"Account Manager dédié"}]]}],["$","li","Commission négociée",{"className":"flex items-start gap-2 text-sm text-gray-600","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-3 h-3 mt-1 text-mbnb-coral flex-shrink-0","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"m8.25 4.5 7.5 7.5-7.5 7.5"}]]}],["$","span",null,{"children":"Commission négociée"}]]}]]}]]}]
1e:["$","div",null,{"className":"bg-mbnb-navy rounded-2xl p-8 mb-12 text-white","children":[["$","h2",null,{"className":"text-2xl font-bold mb-6 text-center","children":"Niveaux de vérification"}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-3 gap-6","children":[["$","div","Basique",{"className":"bg-white/10 backdrop-blur rounded-xl p-6","children":[["$","div",null,{"className":"inline-block px-3 py-1 rounded-full text-sm font-semibold mb-4 bg-gray-100 text-gray-700","children":"Basique"}],["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"children":[["$","h4",null,{"className":"font-semibold mb-2 text-sm","children":"Requis:"}],["$","ul",null,{"className":"space-y-1","children":[["$","li","Email vérifié",{"className":"text-xs opacity-90","children":["• ","Email vérifié"]}],["$","li","Téléphone vérifié",{"className":"text-xs opacity-90","children":["• ","Téléphone vérifié"]}]]}]]}],["$","div",null,{"children":[["$","h4",null,{"className":"font-semibold mb-1 text-sm","children":"Avantages:"}],["$","p",null,{"className":"text-xs opacity-90","children":"Peut parcourir et contacter"}]]}]]}]]}],["$","div","Vérifié",{"className":"bg-white/10 backdrop-blur rounded-xl p-6","children":[["$","div",null,{"className":"inline-block px-3 py-1 rounded-full text-sm font-semibold mb-4 bg-mbnb-teal/10 text-mbnb-teal","children":"Vérifié"}],["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"children":[["$","h4",null,{"className":"font-semibold mb-2 text-sm","children":"Requis:"}],["$","ul",null,{"className":"space-y-1","children":[["$","li","Identité vérifiée",{"className":"text-xs opacity-90","children":["• ","Identité vérifiée"]}],["$","li","Photo de profil",{"className":"text-xs opacity-90","children":["• ","Photo de profil"]}]]}]]}],["$","div",null,{"children":[["$","h4",null,{"className":"font-semibold mb-1 text-sm","children":"Avantages:"}],["$","p",null,{"className":"text-xs opacity-90","children":"Peut réserver et publier"}]]}]]}]]}],["$","div","Super",{"className":"bg-white/10 backdrop-blur rounded-xl p-6","children":[["$","div",null,{"className":"inline-block px-3 py-1 rounded-full text-sm font-semibold mb-4 bg-mbnb-coral/10 text-mbnb-coral","children":"Super"}],["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"children":[["$","h4",null,{"className":"font-semibold mb-2 text-sm","children":"Requis:"}],["$","ul",null,{"className":"space-y-1","children":[["$","li","10+ réservations",{"className":"text-xs opacity-90","children":["• ","10+ réservations"]}],["$","li","Note 4.8+",{"className":"text-xs opacity-90","children":["• ","Note 4.8+"]}]]}]]}],["$","div",null,{"children":[["$","h4",null,{"className":"font-semibold mb-1 text-sm","children":"Avantages:"}],["$","p",null,{"className":"text-xs opacity-90","children":"Avantages exclusifs"}]]}]]}]]}]]}]]}]
1f:["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 gap-8 mb-12","children":[["$","div","1",{"className":"bg-white border rounded-2xl p-8","children":[["$","div",null,{"className":"flex items-start gap-4 mb-6","children":[["$","div",null,{"className":"w-12 h-12 bg-mbnb-coral/10 rounded-lg flex items-center justify-center flex-shrink-0","children":["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-6 h-6 text-mbnb-coral","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}]]}]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-xl font-semibold text-gray-900 mb-2","children":"Créer et configurer votre profil"}],["$","p",null,{"className":"text-gray-600","children":"Première configuration de votre compte Mbnb"}]]}]]}],["$","div",null,{"className":"pl-16","children":["$","ul",null,{"className":"space-y-3","children":[["$","li","0",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[1,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Inscription avec email ou numéro de téléphone marocain"}]]}],["$","li","1",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[2,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Validation par code SMS ou email sous 5 minutes"}]]}],["$","li","2",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[3,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Ajout photo de profil obligatoire (JPG/PNG, max 5MB)"}]]}],["$","li","3",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[4,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Remplir nom complet tel que sur pièce d'identité"}]]}],["$","li","4",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[5,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Sélectionner langue préférée parmi 7 disponibles"}]]}],["$","li","5",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[6,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Ajouter description personnelle (min 50 caractères)"}]]}],["$","li","6",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[7,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Configurer devise d'affichage par défaut"}]]}],["$","li","7",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[8,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Activer authentification à deux facteurs (recommandé)"}]]}],["$","li","8",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[9,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Profil complété à 100% = Badge de confiance"}]]}]]}]}]]}],["$","div","2",{"className":"bg-white border rounded-2xl p-8","children":[["$","div",null,{"className":"flex items-start gap-4 mb-6","children":[["$","div",null,{"className":"w-12 h-12 bg-mbnb-coral/10 rounded-lg flex items-center justify-center flex-shrink-0","children":["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-6 h-6 text-mbnb-coral","children":[null,"$L2b"]}]}],"$L2c"]}],"$L2d"]}],"$L2e","$L2f","$L30","$L31"]}]
20:["$","div",null,{"className":"bg-gray-50 rounded-2xl p-8 mb-12","children":[["$","h2",null,{"className":"text-2xl font-semibold text-gray-900 mb-6","children":"Actions rapides"}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4","children":[["$","$L7",null,{"href":"/account/profile","className":"bg-white rounded-xl p-4 hover:shadow-md transition-shadow flex items-center gap-3","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-5 h-5 text-mbnb-coral","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M6.827 6.175A2.31 2.31 0 0 1 5.186 7.23c-.38.054-.757.112-1.134.175C2.999 7.58 2.25 8.507 2.25 9.574V18a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9.574c0-1.067-.75-1.994-1.802-2.169a47.865 47.865 0 0 0-1.134-.175 2.31 2.31 0 0 1-1.64-1.055l-.822-1.316a2.192 2.192 0 0 0-1.736-1.039 48.774 48.774 0 0 0-5.232 0 2.192 2.192 0 0 0-1.736 1.039l-.821 1.316Z"}],["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M16.5 12.75a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0ZM18.75 10.5h.008v.008h-.008V10.5Z"}]]}],["$","span",null,{"className":"text-sm font-medium","children":"Modifier photo"}]]}],["$","$L7",null,{"href":"/account/security","className":"bg-white rounded-xl p-4 hover:shadow-md transition-shadow flex items-center gap-3","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-5 h-5 text-mbnb-coral","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M15.75 5.25a3 3 0 0 1 3 3m3 0a6 6 0 0 1-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1 1 21.75 8.25Z"}]]}],["$","span",null,{"className":"text-sm font-medium","children":"Changer mot de passe"}]]}],["$","$L7",null,{"href":"/account/verification","className":"bg-white rounded-xl p-4 hover:shadow-md transition-shadow flex items-center gap-3","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-5 h-5 text-mbnb-coral","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M15 9h3.75M15 12h3.75M15 15h3.75M4.5 19.5h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Zm6-10.125a1.875 1.875 0 1 1-3.75 0 1.875 1.875 0 0 1 3.75 0Zm1.294 6.336a6.721 6.721 0 0 1-3.17.789 6.721 6.721 0 0 1-3.168-.789 3.376 3.376 0 0 1 6.338 0Z"}]]}],["$","span",null,{"className":"text-sm font-medium","children":"Vérifier identité"}]]}],["$","$L7",null,{"href":"/account/notifications","className":"bg-white rounded-xl p-4 hover:shadow-md transition-shadow flex items-center gap-3","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-5 h-5 text-mbnb-coral","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}]]}],["$","span",null,{"className":"text-sm font-medium","children":"Notifications"}]]}],["$","$L7",null,{"href":"/account/payment","className":"bg-white rounded-xl p-4 hover:shadow-md transition-shadow flex items-center gap-3","children":["$L32","$L33"]}],"$L34","$L35","$L36"]}]]}]
21:["$","div",null,{"className":"text-center bg-white border rounded-2xl p-8 mb-12","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-12 h-12 text-mbnb-coral mx-auto mb-4","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M10.5 1.5H8.25A2.25 2.25 0 0 0 6 3.75v16.5a2.25 2.25 0 0 0 2.25 2.25h7.5A2.25 2.25 0 0 0 18 20.25V3.75a2.25 2.25 0 0 0-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3"}]]}],["$","h3",null,{"className":"text-xl font-semibold mb-2","children":"Besoin d'aide avec votre compte?"}],["$","p",null,{"className":"text-gray-600 mb-6","children":"Notre équipe support est disponible 24/7"}],["$","div",null,{"className":"flex flex-col sm:flex-row gap-4 justify-center","children":[["$","$L7",null,{"href":"/contact","className":"bg-mbnb-coral text-white px-6 py-3 rounded-full font-semibold hover:bg-mbnb-coral/90 transition-colors","children":"Contacter le support"}],["$","$L7",null,{"href":"/help","className":"border-2 border-mbnb-coral text-mbnb-coral px-6 py-3 rounded-full font-semibold hover:bg-mbnb-coral/10 transition-colors","children":"Centre d'aide complet"}]]}]]}]
22:["$","div",null,{"className":"text-center","children":["$","$L7",null,{"href":"/help","className":"inline-flex items-center gap-2 text-mbnb-coral hover:text-mbnb-coral/80 font-semibold transition-colors","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-4 h-4","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}]]}],"Retour au centre d'aide"]}]}]
23:["$","$L37",null,{"children":["$L38",["$","$L39",null,{"promise":"$@3a"}]]}]
29:["$","$L7",null,{"href":"https://youtube.com/mbnb","className":"bg-white hover:bg-gray-100 p-1.5 rounded transition-colors","children":["$","svg",null,{"className":"w-4 h-4","viewBox":"0 0 24 24","children":["$","path",null,{"fill":"#FF0000","d":"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"}]}]}]
2a:["$","$L7",null,{"href":"https://linkedin.com/company/mbnb","className":"bg-white hover:bg-gray-100 p-1.5 rounded transition-colors","children":["$","svg",null,{"className":"w-4 h-4","viewBox":"0 0 24 24","children":["$","path",null,{"fill":"#0A66C2","d":"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"}]}]}]
2b:["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M15 9h3.75M15 12h3.75M15 15h3.75M4.5 19.5h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Zm6-10.125a1.875 1.875 0 1 1-3.75 0 1.875 1.875 0 0 1 3.75 0Zm1.294 6.336a6.721 6.721 0 0 1-3.17.789 6.721 6.721 0 0 1-3.168-.789 3.376 3.376 0 0 1 6.338 0Z"}]
2c:["$","div",null,{"children":[["$","h3",null,{"className":"text-xl font-semibold text-gray-900 mb-2","children":"Vérification d'identité obligatoire"}],["$","p",null,{"className":"text-gray-600","children":"Processus de vérification pour hôtes et voyageurs"}]]}]
2d:["$","div",null,{"className":"pl-16","children":["$","ul",null,{"className":"space-y-3","children":[["$","li","0",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[1,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Obligatoire pour: Première réservation ou location"}]]}],["$","li","1",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[2,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Documents acceptés: CIN, Passeport, Permis de séjour"}]]}],["$","li","2",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[3,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Photo selfie avec document pour validation IA"}]]}],["$","li","3",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[4,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Vérification automatique en 2-4 heures"}]]}],["$","li","4",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[5,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Vérification manuelle si échec IA (24-48h)"}]]}],["$","li","5",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[6,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Badge \"Identité vérifiée\" sur votre profil"}]]}],["$","li","6",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[7,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Renouvellement tous les 2 ans obligatoire"}]]}],["$","li","7",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[8,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Données cryptées et supprimées après validation"}]]}],["$","li","8",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[9,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Conformité CNDP (loi marocaine protection données)"}]]}]]}]}]
2e:["$","div","3",{"className":"bg-white border rounded-2xl p-8","children":[["$","div",null,{"className":"flex items-start gap-4 mb-6","children":[["$","div",null,{"className":"w-12 h-12 bg-mbnb-coral/10 rounded-lg flex items-center justify-center flex-shrink-0","children":["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-6 h-6 text-mbnb-coral","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"}]]}]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-xl font-semibold text-gray-900 mb-2","children":"Devenir Superhôte Mbnb"}],["$","p",null,{"className":"text-gray-600","children":"Critères et avantages du statut Superhôte"}]]}]]}],["$","div",null,{"className":"pl-16","children":["$","ul",null,{"className":"space-y-3","children":[["$","li","0",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[1,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Minimum 10 réservations complétées sur 12 mois"}]]}],["$","li","1",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[2,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Note moyenne ≥ 4.8 étoiles"}]]}],["$","li","2",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[3,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Taux de réponse ≥ 90% sous 24h"}]]}],["$","li","3",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[4,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Zéro annulation sauf cas de force majeure"}]]}],["$","li","4",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[5,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Avantages: Badge distinctif sur profil"}]]}],["$","li","5",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[6,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Boost visibilité +40% dans recherches"}]]}],["$","li","6",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[7,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Commission réduite: 1.2% au lieu de 1.6%"}]]}],["$","li","7",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[8,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Support prioritaire 24/7 dédié"}]]}],["$","li","8",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[9,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Invitation événements exclusifs Mbnb Maroc"}]]}]]}]}]]}]
2f:["$","div","4",{"className":"bg-white border rounded-2xl p-8","children":[["$","div",null,{"className":"flex items-start gap-4 mb-6","children":[["$","div",null,{"className":"w-12 h-12 bg-mbnb-coral/10 rounded-lg flex items-center justify-center flex-shrink-0","children":["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-6 h-6 text-mbnb-coral","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}]]}]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-xl font-semibold text-gray-900 mb-2","children":"Sécurité et confidentialité"}],["$","p",null,{"className":"text-gray-600","children":"Protéger votre compte et données personnelles"}]]}]]}],["$","div",null,{"className":"pl-16","children":["$","ul",null,{"className":"space-y-3","children":[["$","li","0",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[1,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Mot de passe fort: 12+ caractères, majuscules, chiffres"}]]}],["$","li","1",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[2,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Authentification 2FA par SMS ou Google Authenticator"}]]}],["$","li","2",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[3,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Alertes connexion depuis nouvel appareil"}]]}],["$","li","3",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[4,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Sessions actives visibles et révocables"}]]}],["$","li","4",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[5,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Paramètres de confidentialité personnalisables"}]]}],["$","li","5",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[6,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Masquage numéro téléphone dans messagerie"}]]}],["$","li","6",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[7,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Historique des connexions sur 90 jours"}]]}],["$","li","7",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[8,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Signalement activité suspecte en 1 clic"}]]}],["$","li","8",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[9,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Blocage utilisateurs indésirables permanent"}]]}]]}]}]]}]
30:["$","div","5",{"className":"bg-white border rounded-2xl p-8","children":[["$","div",null,{"className":"flex items-start gap-4 mb-6","children":[["$","div",null,{"className":"w-12 h-12 bg-mbnb-coral/10 rounded-lg flex items-center justify-center flex-shrink-0","children":["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-6 h-6 text-mbnb-coral","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}]]}]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-xl font-semibold text-gray-900 mb-2","children":"Notifications et préférences"}],["$","p",null,{"className":"text-gray-600","children":"Gérer vos alertes et communications"}]]}]]}],["$","div",null,{"className":"pl-16","children":["$","ul",null,{"className":"space-y-3","children":[["$","li","0",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[1,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Notifications push mobiles personnalisables"}]]}],["$","li","1",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[2,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Emails: Réservations, Messages, Promotions"}]]}],["$","li","2",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[3,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"SMS: Confirmations et rappels uniquement"}]]}],["$","li","3",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[4,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"WhatsApp Business: Support instantané"}]]}],["$","li","4",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[5,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Fréquence: Immédiat, Quotidien, Hebdomadaire"}]]}],["$","li","5",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[6,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Mode Ne Pas Déranger programmable"}]]}],["$","li","6",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[7,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Alertes prix pour destinations favorites"}]]}],["$","li","7",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[8,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Newsletter TOP 100 activités mensuelles"}]]}],["$","li","8",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[9,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Désabonnement en 1 clic respecté immédiatement"}]]}]]}]}]]}]
31:["$","div","6",{"className":"bg-white border rounded-2xl p-8","children":[["$","div",null,{"className":"flex items-start gap-4 mb-6","children":[["$","div",null,{"className":"w-12 h-12 bg-mbnb-coral/10 rounded-lg flex items-center justify-center flex-shrink-0","children":["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-6 h-6 text-mbnb-coral","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}]]}]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-xl font-semibold text-gray-900 mb-2","children":"Supprimer ou désactiver compte"}],["$","p",null,{"className":"text-gray-600","children":"Options pour pause ou suppression définitive"}]]}]]}],["$","div",null,{"className":"pl-16","children":["$","ul",null,{"className":"space-y-3","children":[["$","li","0",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[1,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Désactivation temporaire: Profil masqué, données conservées"}]]}],["$","li","1",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[2,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Réactivation possible à tout moment"}]]}],["$","li","2",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[3,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Suppression définitive: Délai de grâce 30 jours"}]]}],["$","li","3",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[4,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Export données personnelles avant suppression"}]]}],["$","li","4",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[5,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Réservations actives doivent être terminées"}]]}],["$","li","5",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[6,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Historique transactions conservé 5 ans (légal)"}]]}],["$","li","6",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[7,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Messages supprimés après 90 jours"}]]}],["$","li","7",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[8,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Avis et évaluations anonymisés mais conservés"}]]}],["$","li","8",{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"text-mbnb-coral font-semibold flex-shrink-0","children":[9,"."]}],["$","span",null,{"className":"text-gray-700 text-sm","children":"Confirmation par email + code SMS requis"}]]}]]}]}]]}]
32:["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-5 h-5 text-mbnb-coral","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"}]]}]
33:["$","span",null,{"className":"text-sm font-medium","children":"Moyens de paiement"}]
34:["$","$L7",null,{"href":"/account/language","className":"bg-white rounded-xl p-4 hover:shadow-md transition-shadow flex items-center gap-3","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-5 h-5 text-mbnb-coral","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"}]]}],["$","span",null,{"className":"text-sm font-medium","children":"Langue et devise"}]]}]
35:["$","$L7",null,{"href":"/account/privacy","className":"bg-white rounded-xl p-4 hover:shadow-md transition-shadow flex items-center gap-3","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-5 h-5 text-mbnb-coral","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"}]]}],["$","span",null,{"className":"text-sm font-medium","children":"Confidentialité"}]]}]
36:["$","$L7",null,{"href":"/account/close","className":"bg-white rounded-xl p-4 hover:shadow-md transition-shadow flex items-center gap-3","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","fill":"none","viewBox":"0 0 24 24","strokeWidth":1.5,"stroke":"currentColor","aria-hidden":"true","data-slot":"icon","ref":"$undefined","aria-labelledby":"$undefined","className":"w-5 h-5 text-red-500","children":[null,["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","d":"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}]]}],["$","span",null,{"className":"text-sm font-medium","children":"Fermer compte"}]]}]
25:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1, maximum-scale=5, user-scalable=yes"}],["$","meta","2",{"name":"theme-color","media":"(prefers-color-scheme: light)","content":"#FF5A5F"}],["$","meta","3",{"name":"theme-color","media":"(prefers-color-scheme: dark)","content":"#E74C3C"}]]
38:null
3b:I[36505,[],"IconMark"]
3a:{"metadata":[["$","title","0",{"children":"Aide Compte Utilisateur | Centre d'aide Mbnb | Mbnb"}],["$","meta","1",{"name":"description","content":"Gérez votre profil, sécurité, vérifications et préférences sur Mbnb."}],["$","link","2",{"rel":"author","href":"https://mbnb.ma"}],["$","meta","3",{"name":"author","content":"Mbnb"}],["$","link","4",{"rel":"manifest","href":"/site.webmanifest","crossOrigin":"$undefined"}],["$","meta","5",{"name":"keywords","content":"location vacances maroc,riad marrakech,villa essaouira,hébergement maroc,tourisme maroc,activités touristiques maroc,séjour authentique,patrimoine marocain,mbnb maroc,booking maroc,voyage maroc,réservation hébergement maroc"}],["$","meta","6",{"name":"creator","content":"Mbnb"}],["$","meta","7",{"name":"publisher","content":"Mbnb"}],["$","meta","8",{"name":"robots","content":"index, follow"}],["$","meta","9",{"name":"googlebot","content":"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"}],["$","link","10",{"rel":"canonical","href":"https://mbnb.ma"}],["$","link","11",{"rel":"alternate","hrefLang":"fr-MA","href":"https://mbnb.ma/fr"}],["$","link","12",{"rel":"alternate","hrefLang":"ar-MA","href":"https://mbnb.ma/ar"}],["$","link","13",{"rel":"alternate","hrefLang":"en-US","href":"https://mbnb.ma/en"}],["$","link","14",{"rel":"alternate","hrefLang":"es-ES","href":"https://mbnb.ma/es"}],["$","link","15",{"rel":"alternate","hrefLang":"zh-CN","href":"https://mbnb.ma/zh"}],["$","link","16",{"rel":"alternate","hrefLang":"ru-RU","href":"https://mbnb.ma/ru"}],["$","meta","17",{"name":"format-detection","content":"telephone=no, address=no, email=no"}],["$","meta","18",{"name":"google-site-verification","content":"YOUR_GOOGLE_VERIFICATION_CODE"}],["$","meta","19",{"name":"y_key","content":"YOUR_YAHOO_VERIFICATION_CODE"}],["$","meta","20",{"name":"yandex-verification","content":"YOUR_YANDEX_VERIFICATION_CODE"}],["$","meta","21",{"name":"me","content":"<EMAIL>"}],["$","meta","22",{"property":"og:title","content":"Mbnb - Plateforme de Location de Vacances au Maroc"}],["$","meta","23",{"property":"og:description","content":"Découvrez des hébergements authentiques et les TOP 100 activités touristiques au Maroc. Première plateforme marocaine mariant IA prédictive et patrimoine authentique."}],["$","meta","24",{"property":"og:url","content":"https://mbnb.ma"}],["$","meta","25",{"property":"og:site_name","content":"Mbnb"}],["$","meta","26",{"property":"og:locale","content":"fr_MA"}],["$","meta","27",{"property":"og:image","content":"https://cdn.mbnb.ma/og-image.jpg"}],["$","meta","28",{"property":"og:image:width","content":"1200"}],["$","meta","29",{"property":"og:image:height","content":"630"}],["$","meta","30",{"property":"og:image:alt","content":"Mbnb - Découvrez le Maroc authentique"}],["$","meta","31",{"property":"og:type","content":"website"}],["$","meta","32",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","33",{"name":"twitter:creator","content":"@mbnb_ma"}],["$","meta","34",{"name":"twitter:title","content":"Mbnb - Plateforme de Location de Vacances au Maroc"}],["$","meta","35",{"name":"twitter:description","content":"Découvrez des hébergements authentiques et les TOP 100 activités touristiques au Maroc. Première plateforme marocaine mariant IA prédictive et patrimoine authentique."}],["$","meta","36",{"name":"twitter:image","content":"https://cdn.mbnb.ma/twitter-image.jpg"}],["$","link","37",{"rel":"icon","href":"/favicon.ico"}],["$","link","38",{"rel":"icon","href":"/favicon-16x16.png","sizes":"16x16","type":"image/png"}],["$","link","39",{"rel":"icon","href":"/favicon-32x32.png","sizes":"32x32","type":"image/png"}],["$","link","40",{"rel":"apple-touch-icon","href":"/apple-touch-icon.png"}],["$","link","41",{"rel":"apple-touch-icon","href":"/apple-touch-icon-180x180.png","sizes":"180x180","type":"image/png"}],["$","link","42",{"rel":"mask-icon","href":"/safari-pinned-tab.svg"}],["$","$L3b","43",{}]],"error":null,"digest":"$undefined"}
28:"$3a:metadata"

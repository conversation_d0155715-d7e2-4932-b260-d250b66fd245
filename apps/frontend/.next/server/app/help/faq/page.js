"use strict";(()=>{var a={};a.id=9107,a.ids=[9107],a.modules={261:a=>{a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24316:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))})},26713:a=>{a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{a.exports=require("util")},28591:(a,b,c)=>{c.r(b),c.d(b,{default:()=>j,metadata:()=>h});var d=c(5939),e=c(3498),f=c.n(e),g=c(24316);let h={title:"FAQ - Questions Fr\xe9quentes | Mbnb",description:"Trouvez rapidement des r\xe9ponses \xe0 vos questions sur Mbnb, la plateforme de location de vacances et d'activit\xe9s touristiques au Maroc."},i=[{title:"Commissions et Tarifs",icon:"\uD83D\uDCB0",questions:[{q:"Comment fonctionne la commission Mbnb pour les propri\xe9t\xe9s?",a:`Notre structure de commission est \xe9volutive et transparente:
        • Phase 1 (0-3 mois): 14% total (1% host + 13% traveler)
        • Phase 2 (3-6 mois): 14.6% total (1.6% host + 13% traveler)
        • Phase 3 (6+ mois): 15% total (2% host + 13% traveler)
        Cette structure r\xe9compense la fid\xe9lit\xe9 de nos hosts sur le long terme.`},{q:"Quelle est la commission sur les activit\xe9s TOP 100?",a:"La commission sur les activit\xe9s est FIXE \xe0 18% (14% pour le provider + 4% pour le traveler). Cette commission garantit la qualit\xe9 et l'authenticit\xe9 de nos exp\xe9riences s\xe9lectionn\xe9es."},{q:"Y a-t-il des frais cach\xe9s?",a:"Non, absolument aucun frais cach\xe9. Toutes nos commissions sont clairement affich\xe9es avant la r\xe9servation. Pas de surprises!"}]},{title:"Programme de Fid\xe9lit\xe9",icon:"\uD83C\uDF1F",questions:[{q:"Comment fonctionne le programme Loyalty Mbnb?",a:`Notre programme compte 4 niveaux inspir\xe9s de la culture marocaine:
        • EXPLORATEUR (1000 pts): 2% de r\xe9duction
        • AVENTURIER (2500 pts): 5% de r\xe9duction
        • AMBASSADEUR (5000 pts): 7.5% de r\xe9duction
        • L\xc9GENDE (10000 pts): 10% de r\xe9duction
        Les points s'accumulent \xe0 chaque r\xe9servation!`},{q:"Comment gagner des points?",a:"Vous gagnez 10 points par 100 MAD d\xe9pens\xe9s sur les propri\xe9t\xe9s et 15 points par 100 MAD sur les activit\xe9s TOP 100. Les bonus culturels offrent des points suppl\xe9mentaires."},{q:"Les points expirent-ils?",a:"Les points sont valables 24 mois apr\xe8s leur acquisition. Toute nouvelle activit\xe9 prolonge automatiquement la validit\xe9 de tous vos points."}]},{title:"Moyens de Paiement",icon:"\uD83D\uDCB3",questions:[{q:"Quels moyens de paiement acceptez-vous?",a:`Nous acceptons 6 options de paiement:
        • CMI - Cartes bancaires marocaines (Visa, Mastercard)
        • WafaCash - Virement instantan\xe9
        • Cash Plus - Virement instantan\xe9
        • Alipay - Pour touristes chinois
        • Google Pay - Paiement mobile Android
        • Apple Pay - Paiement mobile iOS`},{q:"Le paiement est-il s\xe9curis\xe9?",a:"Oui, tous les paiements sont s\xe9curis\xe9s avec cryptage SSL 256-bit. Nous respectons les normes PCI-DSS et travaillons avec des partenaires certifi\xe9s."},{q:"Puis-je payer en plusieurs fois?",a:"Pour les r\xe9servations sup\xe9rieures \xe0 5000 MAD effectu\xe9es plus de 60 jours \xe0 l'avance, nous proposons un paiement en 2 fois sans frais."}]},{title:"Programme SuperH\xf4tes",icon:"\uD83C\uDFC6",questions:[{q:"Comment devenir SuperH\xf4te Mbnb?",a:`Les crit\xe8res pour devenir SuperH\xf4te sont:
        • Note minimum: 4.8/5 \xe9toiles
        • Au moins 10 avis v\xe9rifi\xe9s
        • Taux de r\xe9ponse ≥90%
        • Temps de r\xe9ponse <24 heures
        • 12 mois d'anciennet\xe9 minimum`},{q:"Quels sont les avantages SuperH\xf4tes?",a:"Badge distinctif, InstantBook automatique, programme de mentoring exclusif, support prioritaire, et visibilit\xe9 accrue dans les r\xe9sultats de recherche."},{q:"Le statut est-il permanent?",a:"Le statut est r\xe9\xe9valu\xe9 tous les 3 mois. Vous devez maintenir les crit\xe8res pour conserver votre badge SuperH\xf4te."}]},{title:"TOP 100 Activit\xe9s",icon:"\uD83C\uDFAF",questions:[{q:"Qu'est-ce que les TOP 100 activit\xe9s Mbnb?",a:"Une s\xe9lection curat\xe9e des 100 meilleures exp\xe9riences touristiques au Maroc, v\xe9rifi\xe9es pour leur authenticit\xe9 et qualit\xe9. De la visite guid\xe9e de m\xe9dina aux excursions dans l'Atlas."},{q:"Comment sont s\xe9lectionn\xe9es ces activit\xe9s?",a:"Chaque activit\xe9 est \xe9valu\xe9e sur: authenticit\xe9 culturelle, avis clients (min 4.5/5), s\xe9curit\xe9, valeur ajout\xe9e, et engagement local. Seules les meilleures sont retenues."},{q:"Puis-je annuler une activit\xe9?",a:"Oui, annulation gratuite jusqu'\xe0 48h avant l'activit\xe9. Entre 48h et 24h: remboursement 50%. Moins de 24h: non remboursable sauf cas de force majeure."}]},{title:"R\xe9servations et Annulations",icon:"\uD83D\uDCC5",questions:[{q:"Comment modifier ma r\xe9servation?",a:'Connectez-vous \xe0 votre compte, allez dans "Mes r\xe9servations", et cliquez sur "Modifier". Les modifications sont gratuites jusqu\'\xe0 7 jours avant l\'arriv\xe9e.'},{q:"Quelle est la politique d'annulation?",a:"Nous proposons 3 politiques: Flexible (remboursement jusqu'\xe0 24h), Mod\xe9r\xe9e (jusqu'\xe0 5 jours), Stricte (jusqu'\xe0 14 jours). Chaque host choisit sa politique."},{q:"Comment obtenir un remboursement?",a:"Les remboursements sont automatiques selon la politique d'annulation. Comptez 5-10 jours ouvrables pour voir le montant sur votre compte."}]},{title:"Technologie et S\xe9curit\xe9",icon:"\uD83D\uDD12",questions:[{q:"Comment Mbnb utilise l'IA?",a:"Nous utilisons l'IA pour: optimisation des prix (XGBoost), pr\xe9visions de demande (StatsForecast), recommandations personnalis\xe9es (algorithme Haversine), et traitement du langage (Qwen3)."},{q:"Mes donn\xe9es sont-elles prot\xe9g\xe9es?",a:"Oui, nous respectons le RGPD et la loi marocaine 09-08. Vos donn\xe9es sont crypt\xe9es, jamais vendues, et vous pouvez les supprimer \xe0 tout moment."},{q:"Quelle est la performance de la plateforme?",a:"Notre architecture Modulith avec Fastify garantit une r\xe9ponse rapide. Nous visons 3,500 requ\xeates/seconde avec 0 erreur TypeScript dans notre code."}]},{title:"Support et Assistance",icon:"\uD83D\uDCAC",questions:[{q:"Comment contacter le support?",a:"Support disponible 24/7 via: chat en direct, email (<EMAIL>), t\xe9l\xe9phone (+212 5XX-XXXXXX), ou centre d'aide en ligne."},{q:"En combien de temps obtenez-vous une r\xe9ponse?",a:"Chat: 2-5 minutes. Email: sous 4 heures. T\xe9l\xe9phone: imm\xe9diat aux heures ouvrables. Support prioritaire pour SuperH\xf4tes et niveau L\xe9gende."},{q:"Dans quelles langues est disponible le support?",a:"Support multilingue: Fran\xe7ais, Arabe, Anglais, Espagnol, et Darija. Support chinois et russe disponible pour les touristes internationaux."}]}];function j(){return(0,d.jsxs)("main",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)("div",{className:"bg-mbnb-navy text-white py-12",children:(0,d.jsx)("div",{className:"container-mbnb",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,d.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"Questions Fr\xe9quentes"}),(0,d.jsx)("p",{className:"text-xl text-white/90",children:"Trouvez rapidement des r\xe9ponses \xe0 toutes vos questions sur Mbnb"})]})})}),(0,d.jsx)("div",{className:"bg-white border-b",children:(0,d.jsx)("div",{className:"container-mbnb py-6",children:(0,d.jsx)("div",{className:"flex flex-wrap gap-4 justify-center",children:i.map((a,b)=>(0,d.jsxs)(f(),{href:`#${a.title.toLowerCase().replace(/\s+/g,"-")}`,className:"inline-flex items-center gap-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors",children:[(0,d.jsx)("span",{className:"text-xl",children:a.icon}),(0,d.jsx)("span",{className:"font-medium",children:a.title})]},b))})})}),(0,d.jsx)("div",{className:"container-mbnb py-12",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto",children:[i.map((a,b)=>(0,d.jsxs)("div",{id:a.title.toLowerCase().replace(/\s+/g,"-"),className:"mb-12 scroll-mt-24",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,d.jsx)("span",{className:"text-3xl",children:a.icon}),(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:a.title})]}),(0,d.jsx)("div",{className:"space-y-4",children:a.questions.map((a,b)=>(0,d.jsxs)("details",{className:"group bg-white rounded-xl border border-gray-200 hover:border-mbnb-coral transition-colors",children:[(0,d.jsxs)("summary",{className:"flex items-center justify-between p-6 cursor-pointer",children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-900 pr-4",children:a.q}),(0,d.jsx)(g.A,{className:"w-5 h-5 text-gray-400 group-open:rotate-90 transition-transform"})]}),(0,d.jsx)("div",{className:"px-6 pb-6 pt-0",children:(0,d.jsx)("p",{className:"text-gray-600 whitespace-pre-line",children:a.a})})]},b))})]},b)),(0,d.jsxs)("div",{className:"mt-16 p-8 bg-mbnb-teal/5 rounded-2xl text-center",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Vous ne trouvez pas votre r\xe9ponse?"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6 max-w-2xl mx-auto",children:"Notre \xe9quipe support est disponible 24/7 pour r\xe9pondre \xe0 toutes vos questions"}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)(f(),{href:"/help/support",className:"inline-flex items-center justify-center px-6 py-3 bg-mbnb-coral text-white rounded-lg hover:bg-mbnb-coral-dark transition-colors",children:"Contacter le Support"}),(0,d.jsx)(f(),{href:"/help",className:"inline-flex items-center justify-center px-6 py-3 bg-white text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Centre d'Aide Complet"})]})]}),(0,d.jsxs)("div",{className:"mt-12 grid grid-cols-2 md:grid-cols-4 gap-6 text-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-mbnb-coral mb-2",children:"24/7"}),(0,d.jsx)("div",{className:"text-gray-600",children:"Support Disponible"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-mbnb-coral mb-2",children:"7"}),(0,d.jsx)("div",{className:"text-gray-600",children:"Langues Support\xe9es"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-mbnb-coral mb-2",children:"4h"}),(0,d.jsx)("div",{className:"text-gray-600",children:"Temps R\xe9ponse Max"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-mbnb-coral mb-2",children:"100%"}),(0,d.jsx)("div",{className:"text-gray-600",children:"S\xe9curis\xe9"})]})]})]})})]})}},29294:a=>{a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{a.exports=require("path")},41025:a=>{a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},63033:a=>{a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},86439:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external")},94601:(a,b,c)=>{c.r(b),c.d(b,{GlobalError:()=>C.default,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(73653),e=c(97714),f=c(85250),g=c(37587),h=c(22369),i=c(1889),j=c(96232),k=c(22841),l=c(46537),m=c(46027),n=c(78559),o=c(75928),p=c(19374),q=c(65971),r=c(261),s=c(79898),t=c(32967),u=c(26713),v=c(40139),w=c(14248),x=c(59580),y=c(57749),z=c(53123),A=c(89745),B=c(86439),C=c(96133),D=c(18283),E=c(39818),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["help",{children:["faq",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,28591)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/help/faq/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,56035)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,74827)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/error.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,96133)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/global-error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,72993)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,15034,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,54693,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/help/faq/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/help/faq/page",pathname:"/help/faq",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",relativeProjectDir:""});async function K(a,b,d){var F;let L="/help/faq/page";"/index"===L&&(L="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await J.prepare(a,b,{srcPage:L,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(L),{isOnDemandRevalidate:ah}=O,ai=J.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(F=$.routes[ag]??$.dynamicRoutes[ag])?void 0:F.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===J.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&J.isDev&&(aB=aa),J.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...D,tree:G,pages:H,GlobalError:C.default,handler:K,routeModule:J,__next_app__:I};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:L,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=J.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:J,page:L,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),J.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!J.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===J.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await J.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await J.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!J.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&E.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:L,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4635,2922,4367],()=>b(b.s=94601));module.exports=c})();
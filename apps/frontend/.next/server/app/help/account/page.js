"use strict";(()=>{var a={};a.id=1392,a.ids=[1392],a.modules={261:a=>{a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11045:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 9h3.75M15 12h3.75M15 15h3.75M4.5 19.5h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Zm6-10.125a1.875 1.875 0 1 1-3.75 0 1.875 1.875 0 0 1 3.75 0Zm1.294 6.336a6.721 6.721 0 0 1-3.17.789 6.721 6.721 0 0 1-3.168-.789 3.376 3.376 0 0 1 6.338 0Z"}))})},16567:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"}))})},19e3:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"}))})},19121:a=>{a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21991:(a,b,c)=>{c.r(b),c.d(b,{GlobalError:()=>C.default,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(73653),e=c(97714),f=c(85250),g=c(37587),h=c(22369),i=c(1889),j=c(96232),k=c(22841),l=c(46537),m=c(46027),n=c(78559),o=c(75928),p=c(19374),q=c(65971),r=c(261),s=c(79898),t=c(32967),u=c(26713),v=c(40139),w=c(14248),x=c(59580),y=c(57749),z=c(53123),A=c(89745),B=c(86439),C=c(96133),D=c(18283),E=c(39818),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["help",{children:["account",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,67511)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/help/account/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,56035)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,74827)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/error.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,96133)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/global-error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,72993)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,15034,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,54693,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/help/account/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/help/account/page",pathname:"/help/account",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",relativeProjectDir:""});async function K(a,b,d){var F;let L="/help/account/page";"/index"===L&&(L="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await J.prepare(a,b,{srcPage:L,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(L),{isOnDemandRevalidate:ah}=O,ai=J.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(F=$.routes[ag]??$.dynamicRoutes[ag])?void 0:F.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===J.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&J.isDev&&(aB=aa),J.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...D,tree:G,pages:H,GlobalError:C.default,handler:K,routeModule:J,__next_app__:I};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:L,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=J.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:J,page:L,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),J.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!J.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===J.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await J.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await J.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!J.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&E.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:L,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},22333:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 1.5H8.25A2.25 2.25 0 0 0 6 3.75v16.5a2.25 2.25 0 0 0 2.25 2.25h7.5A2.25 2.25 0 0 0 18 20.25V3.75a2.25 2.25 0 0 0-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3"}))})},24316:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))})},26713:a=>{a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{a.exports=require("util")},29294:a=>{a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{a.exports=require("path")},34802:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 5.25a3 3 0 0 1 3 3m3 0a6 6 0 0 1-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1 1 21.75 8.25Z"}))})},41025:a=>{a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},51974:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))})},54716:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))})},55688:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.827 6.175A2.31 2.31 0 0 1 5.186 7.23c-.38.054-.757.112-1.134.175C2.999 7.58 2.25 8.507 2.25 9.574V18a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9.574c0-1.067-.75-1.994-1.802-2.169a47.865 47.865 0 0 0-1.134-.175 2.31 2.31 0 0 1-1.64-1.055l-.822-1.316a2.192 2.192 0 0 0-1.736-1.039 48.774 48.774 0 0 0-5.232 0 2.192 2.192 0 0 0-1.736 1.039l-.821 1.316Z"}),d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.5 12.75a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0ZM18.75 10.5h.008v.008h-.008V10.5Z"}))})},62560:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"}))})},63033:a=>{a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66404:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"}))})},67511:(a,b,c)=>{c.r(b),c.d(b,{default:()=>z,metadata:()=>v});var d=c(5939),e=c(3498),f=c.n(e),g=c(54716),h=c(11045),i=c(62560),j=c(51974),k=c(11110);let l=k.forwardRef(function({title:a,titleId:b,...c},d){return k.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:d,"aria-labelledby":b},c),a?k.createElement("title",{id:b},a):null,k.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))});var m=c(92176),n=c(24316),o=c(55688),p=c(34802),q=c(66404),r=c(16567),s=c(19e3),t=c(22333),u=c(71685);let v={title:"Aide Compte Utilisateur | Centre d'aide Mbnb",description:"G\xe9rez votre profil, s\xe9curit\xe9, v\xe9rifications et pr\xe9f\xe9rences sur Mbnb."},w=[{id:1,title:"Cr\xe9er et configurer votre profil",description:"Premi\xe8re configuration de votre compte Mbnb",icon:g.A,content:["Inscription avec email ou num\xe9ro de t\xe9l\xe9phone marocain","Validation par code SMS ou email sous 5 minutes","Ajout photo de profil obligatoire (JPG/PNG, max 5MB)","Remplir nom complet tel que sur pi\xe8ce d'identit\xe9","S\xe9lectionner langue pr\xe9f\xe9r\xe9e parmi 7 disponibles","Ajouter description personnelle (min 50 caract\xe8res)","Configurer devise d'affichage par d\xe9faut","Activer authentification \xe0 deux facteurs (recommand\xe9)","Profil compl\xe9t\xe9 \xe0 100% = Badge de confiance"]},{id:2,title:"V\xe9rification d'identit\xe9 obligatoire",description:"Processus de v\xe9rification pour h\xf4tes et voyageurs",icon:h.A,content:["Obligatoire pour: Premi\xe8re r\xe9servation ou location","Documents accept\xe9s: CIN, Passeport, Permis de s\xe9jour","Photo selfie avec document pour validation IA","V\xe9rification automatique en 2-4 heures","V\xe9rification manuelle si \xe9chec IA (24-48h)",'Badge "Identit\xe9 v\xe9rifi\xe9e" sur votre profil',"Renouvellement tous les 2 ans obligatoire","Donn\xe9es crypt\xe9es et supprim\xe9es apr\xe8s validation","Conformit\xe9 CNDP (loi marocaine protection donn\xe9es)"]},{id:3,title:"Devenir Superh\xf4te Mbnb",description:"Crit\xe8res et avantages du statut Superh\xf4te",icon:i.A,content:["Minimum 10 r\xe9servations compl\xe9t\xe9es sur 12 mois","Note moyenne ≥ 4.8 \xe9toiles","Taux de r\xe9ponse ≥ 90% sous 24h","Z\xe9ro annulation sauf cas de force majeure","Avantages: Badge distinctif sur profil","Boost visibilit\xe9 +40% dans recherches","Commission r\xe9duite: 1.2% au lieu de 1.6%","Support prioritaire 24/7 d\xe9di\xe9","Invitation \xe9v\xe9nements exclusifs Mbnb Maroc"]},{id:4,title:"S\xe9curit\xe9 et confidentialit\xe9",description:"Prot\xe9ger votre compte et donn\xe9es personnelles",icon:j.A,content:["Mot de passe fort: 12+ caract\xe8res, majuscules, chiffres","Authentification 2FA par SMS ou Google Authenticator","Alertes connexion depuis nouvel appareil","Sessions actives visibles et r\xe9vocables","Param\xe8tres de confidentialit\xe9 personnalisables","Masquage num\xe9ro t\xe9l\xe9phone dans messagerie","Historique des connexions sur 90 jours","Signalement activit\xe9 suspecte en 1 clic","Blocage utilisateurs ind\xe9sirables permanent"]},{id:5,title:"Notifications et pr\xe9f\xe9rences",description:"G\xe9rer vos alertes et communications",icon:l,content:["Notifications push mobiles personnalisables","Emails: R\xe9servations, Messages, Promotions","SMS: Confirmations et rappels uniquement","WhatsApp Business: Support instantan\xe9","Fr\xe9quence: Imm\xe9diat, Quotidien, Hebdomadaire","Mode Ne Pas D\xe9ranger programmable","Alertes prix pour destinations favorites","Newsletter TOP 100 activit\xe9s mensuelles","D\xe9sabonnement en 1 clic respect\xe9 imm\xe9diatement"]},{id:6,title:"Supprimer ou d\xe9sactiver compte",description:"Options pour pause ou suppression d\xe9finitive",icon:m.A,content:["D\xe9sactivation temporaire: Profil masqu\xe9, donn\xe9es conserv\xe9es","R\xe9activation possible \xe0 tout moment","Suppression d\xe9finitive: D\xe9lai de gr\xe2ce 30 jours","Export donn\xe9es personnelles avant suppression","R\xe9servations actives doivent \xeatre termin\xe9es","Historique transactions conserv\xe9 5 ans (l\xe9gal)","Messages supprim\xe9s apr\xe8s 90 jours","Avis et \xe9valuations anonymis\xe9s mais conserv\xe9s","Confirmation par email + code SMS requis"]}],x=[{type:"Voyageur",icon:"✈️",features:["R\xe9server h\xe9bergements et activit\xe9s","Messagerie s\xe9curis\xe9e avec h\xf4tes","Historique complet des s\xe9jours","Programme fid\xe9lit\xe9 Mbnb Plus","Listes de favoris illimit\xe9es"]},{type:"H\xf4te",icon:"\uD83C\uDFE0",features:["Publier jusqu'\xe0 10 annonces","Calendrier synchronis\xe9","Tarification dynamique IA","Statistiques d\xe9taill\xe9es","Outils de gestion pro"]},{type:"Pro",icon:"\uD83D\uDCBC",features:["Annonces illimit\xe9es","API pour Channel Manager","Facturation entreprise","Account Manager d\xe9di\xe9","Commission n\xe9goci\xe9e"]}],y=[{level:"Basique",color:"bg-gray-100 text-gray-700",requirements:["Email v\xe9rifi\xe9","T\xe9l\xe9phone v\xe9rifi\xe9"],benefits:"Peut parcourir et contacter"},{level:"V\xe9rifi\xe9",color:"bg-mbnb-teal/10 text-mbnb-teal",requirements:["Identit\xe9 v\xe9rifi\xe9e","Photo de profil"],benefits:"Peut r\xe9server et publier"},{level:"Super",color:"bg-mbnb-coral/10 text-mbnb-coral",requirements:["10+ r\xe9servations","Note 4.8+"],benefits:"Avantages exclusifs"}];function z(){return(0,d.jsx)("main",{className:"min-h-screen py-16",children:(0,d.jsxs)("div",{className:"container-mbnb",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600 mb-8",children:[(0,d.jsx)(f(),{href:"/help",className:"hover:text-mbnb-coral transition-colors",children:"Centre d'aide"}),(0,d.jsx)(n.A,{className:"w-4 h-4"}),(0,d.jsx)("span",{className:"text-gray-900",children:"Compte utilisateur"})]}),(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-mbnb-coral/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(g.A,{className:"w-8 h-8 text-mbnb-coral"})}),(0,d.jsx)("h1",{className:"heading-1 text-gray-900 mb-4",children:"Gestion de compte"}),(0,d.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Configurez votre profil, s\xe9curit\xe9 et pr\xe9f\xe9rences pour une exp\xe9rience Mbnb optimale"})]}),(0,d.jsxs)("div",{className:"mb-12",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-6 text-center",children:"Types de comptes Mbnb"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:x.map(a=>(0,d.jsxs)("div",{className:"bg-white border rounded-xl p-6",children:[(0,d.jsxs)("div",{className:"text-center mb-4",children:[(0,d.jsx)("div",{className:"text-4xl mb-2",children:a.icon}),(0,d.jsx)("h3",{className:"text-lg font-semibold",children:a.type})]}),(0,d.jsx)("ul",{className:"space-y-2",children:a.features.map(a=>(0,d.jsxs)("li",{className:"flex items-start gap-2 text-sm text-gray-600",children:[(0,d.jsx)(n.A,{className:"w-3 h-3 mt-1 text-mbnb-coral flex-shrink-0"}),(0,d.jsx)("span",{children:a})]},a))})]},a.type))})]}),(0,d.jsxs)("div",{className:"bg-mbnb-navy rounded-2xl p-8 mb-12 text-white",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold mb-6 text-center",children:"Niveaux de v\xe9rification"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:y.map(a=>(0,d.jsxs)("div",{className:"bg-white/10 backdrop-blur rounded-xl p-6",children:[(0,d.jsx)("div",{className:`inline-block px-3 py-1 rounded-full text-sm font-semibold mb-4 ${a.color}`,children:a.level}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-semibold mb-2 text-sm",children:"Requis:"}),(0,d.jsx)("ul",{className:"space-y-1",children:a.requirements.map(a=>(0,d.jsxs)("li",{className:"text-xs opacity-90",children:["• ",a]},a))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-semibold mb-1 text-sm",children:"Avantages:"}),(0,d.jsx)("p",{className:"text-xs opacity-90",children:a.benefits})]})]})]},a.level))})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-12",children:w.map(a=>{let b=a.icon;return(0,d.jsxs)("div",{className:"bg-white border rounded-2xl p-8",children:[(0,d.jsxs)("div",{className:"flex items-start gap-4 mb-6",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-mbnb-coral/10 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,d.jsx)(b,{className:"w-6 h-6 text-mbnb-coral"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:a.title}),(0,d.jsx)("p",{className:"text-gray-600",children:a.description})]})]}),(0,d.jsx)("div",{className:"pl-16",children:(0,d.jsx)("ul",{className:"space-y-3",children:a.content.map((a,b)=>(0,d.jsxs)("li",{className:"flex items-start gap-3",children:[(0,d.jsxs)("span",{className:"text-mbnb-coral font-semibold flex-shrink-0",children:[b+1,"."]}),(0,d.jsx)("span",{className:"text-gray-700 text-sm",children:a})]},b))})})]},a.id)})}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-2xl p-8 mb-12",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-6",children:"Actions rapides"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,d.jsxs)(f(),{href:"/account/profile",className:"bg-white rounded-xl p-4 hover:shadow-md transition-shadow flex items-center gap-3",children:[(0,d.jsx)(o.A,{className:"w-5 h-5 text-mbnb-coral"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"Modifier photo"})]}),(0,d.jsxs)(f(),{href:"/account/security",className:"bg-white rounded-xl p-4 hover:shadow-md transition-shadow flex items-center gap-3",children:[(0,d.jsx)(p.A,{className:"w-5 h-5 text-mbnb-coral"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"Changer mot de passe"})]}),(0,d.jsxs)(f(),{href:"/account/verification",className:"bg-white rounded-xl p-4 hover:shadow-md transition-shadow flex items-center gap-3",children:[(0,d.jsx)(h.A,{className:"w-5 h-5 text-mbnb-coral"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"V\xe9rifier identit\xe9"})]}),(0,d.jsxs)(f(),{href:"/account/notifications",className:"bg-white rounded-xl p-4 hover:shadow-md transition-shadow flex items-center gap-3",children:[(0,d.jsx)(l,{className:"w-5 h-5 text-mbnb-coral"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"Notifications"})]}),(0,d.jsxs)(f(),{href:"/account/payment",className:"bg-white rounded-xl p-4 hover:shadow-md transition-shadow flex items-center gap-3",children:[(0,d.jsx)(q.A,{className:"w-5 h-5 text-mbnb-coral"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"Moyens de paiement"})]}),(0,d.jsxs)(f(),{href:"/account/language",className:"bg-white rounded-xl p-4 hover:shadow-md transition-shadow flex items-center gap-3",children:[(0,d.jsx)(r.A,{className:"w-5 h-5 text-mbnb-coral"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"Langue et devise"})]}),(0,d.jsxs)(f(),{href:"/account/privacy",className:"bg-white rounded-xl p-4 hover:shadow-md transition-shadow flex items-center gap-3",children:[(0,d.jsx)(s.A,{className:"w-5 h-5 text-mbnb-coral"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"Confidentialit\xe9"})]}),(0,d.jsxs)(f(),{href:"/account/close",className:"bg-white rounded-xl p-4 hover:shadow-md transition-shadow flex items-center gap-3",children:[(0,d.jsx)(m.A,{className:"w-5 h-5 text-red-500"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"Fermer compte"})]})]})]}),(0,d.jsxs)("div",{className:"text-center bg-white border rounded-2xl p-8 mb-12",children:[(0,d.jsx)(t.A,{className:"w-12 h-12 text-mbnb-coral mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Besoin d'aide avec votre compte?"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:"Notre \xe9quipe support est disponible 24/7"}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)(f(),{href:"/contact",className:"bg-mbnb-coral text-white px-6 py-3 rounded-full font-semibold hover:bg-mbnb-coral/90 transition-colors",children:"Contacter le support"}),(0,d.jsx)(f(),{href:"/help",className:"border-2 border-mbnb-coral text-mbnb-coral px-6 py-3 rounded-full font-semibold hover:bg-mbnb-coral/10 transition-colors",children:"Centre d'aide complet"})]})]}),(0,d.jsx)("div",{className:"text-center",children:(0,d.jsxs)(f(),{href:"/help",className:"inline-flex items-center gap-2 text-mbnb-coral hover:text-mbnb-coral/80 font-semibold transition-colors",children:[(0,d.jsx)(u.A,{className:"w-4 h-4"}),"Retour au centre d'aide"]})})]})})}},71685:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))})},86439:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external")},92176:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))})}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4635,2922,4367],()=>b(b.s=21991));module.exports=c})();
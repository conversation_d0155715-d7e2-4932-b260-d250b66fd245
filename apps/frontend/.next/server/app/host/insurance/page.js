(()=>{var a={};a.id=9986,a.ids=[9986],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12545:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>o,metadata:()=>l});var d=c(5939),e=c(70679),f=c(79134),g=c(92061),h=c(43828);let i=(0,h.A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),j=(0,h.A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),k=(0,h.A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),l={title:"Assurance & Protection | Host Mbnb",description:"Protection compl\xe8te pour hosts Mbnb. Couverture jusqu'\xe0 1 million MAD incluse."},m=[{title:"Dommages mat\xe9riels",amount:"1,000,000 MAD",details:["Mobilier et \xe9quipements","Structures et b\xe2timents","D\xe9coration et objets d'art","Appareils \xe9lectrom\xe9nagers"]},{title:"Responsabilit\xe9 civile",amount:"500,000 MAD",details:["Blessures corporelles","Dommages aux tiers","Frais m\xe9dicaux d'urgence","Frais juridiques"]},{title:"Perte de revenus",amount:"100,000 MAD",details:["Annulation de r\xe9servation","Indisponibilit\xe9 du logement","Ev\xe9nements impr\xe9vus","Catastrophes naturelles"]},{title:"Assistance 24/7",amount:"Illimit\xe9",details:["Support t\xe9l\xe9phonique","Intervention d'urgence","Relogement d'urgence","Assistance juridique"]}],n=[{id:"CLM-2024-001",date:"15/01/2024",type:"Dommage mat\xe9riel",amount:"12,500 MAD",status:"Approuv\xe9"},{id:"CLM-2024-002",date:"02/02/2024",type:"Annulation",amount:"3,200 MAD",status:"En cours"},{id:"CLM-2024-003",date:"28/02/2024",type:"Responsabilit\xe9 civile",amount:"8,900 MAD",status:"Approuv\xe9"}];function o(){return(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)("div",{className:"bg-gradient-to-r from-blue-600 to-blue-700 text-white",children:(0,d.jsx)("div",{className:"container mx-auto px-4 py-16",children:(0,d.jsxs)("div",{className:"max-w-3xl",children:[(0,d.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"Protection Host Mbnb"}),(0,d.jsx)("p",{className:"text-xl text-white/90 mb-8",children:"Couverture compl\xe8te jusqu\\'\xe0 1 million MAD incluse dans votre abonnement Host."}),(0,d.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,d.jsx)("button",{className:"px-6 py-3 bg-white text-blue-700 rounded-lg hover:bg-gray-100 transition-colors",children:"D\xe9clarer un sinistre"}),(0,d.jsx)("button",{className:"px-6 py-3 bg-white/20 text-white rounded-lg hover:bg-white/30 transition-colors",children:"T\xe9l\xe9charger l\\'attestation"})]})]})})}),(0,d.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-12",children:[(0,d.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm",children:[(0,d.jsx)(e.A,{className:"w-8 h-8 text-blue-600 mb-3"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Couverture totale"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"1M MAD"})]}),(0,d.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm",children:[(0,d.jsx)(f.A,{className:"w-8 h-8 text-blue-600 mb-3"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"D\xe9lai de traitement"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"48h"})]}),(0,d.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm",children:[(0,d.jsx)(g.A,{className:"w-8 h-8 text-green-600 mb-3"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Sinistres approuv\xe9s"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"98%"})]}),(0,d.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm",children:[(0,d.jsx)(i,{className:"w-8 h-8 text-blue-600 mb-3"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Support"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"24/7"})]})]}),(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-8",children:"Votre couverture"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12",children:m.map((a,b)=>(0,d.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm",children:[(0,d.jsx)("h3",{className:"font-bold text-gray-900 mb-2",children:a.title}),(0,d.jsx)("p",{className:"text-2xl font-bold text-blue-600 mb-4",children:a.amount}),(0,d.jsx)("ul",{className:"space-y-2",children:a.details.map((a,b)=>(0,d.jsxs)("li",{className:"flex items-start",children:[(0,d.jsx)(g.A,{className:"w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5"}),(0,d.jsx)("span",{className:"text-sm text-gray-600",children:a})]},b))})]},b))}),(0,d.jsxs)("div",{className:"bg-white rounded-xl shadow-sm p-8 mb-12",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Historique des sinistres"}),(0,d.jsx)("button",{className:"text-mbnb-coral hover:text-mbnb-coral-dark font-medium",children:"Voir tout →"})]}),(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"w-full",children:[(0,d.jsx)("thead",{children:(0,d.jsxs)("tr",{className:"border-b",children:[(0,d.jsx)("th",{className:"text-left py-3 px-4 text-sm font-medium text-gray-600",children:"R\xe9f\xe9rence"}),(0,d.jsx)("th",{className:"text-left py-3 px-4 text-sm font-medium text-gray-600",children:"Date"}),(0,d.jsx)("th",{className:"text-left py-3 px-4 text-sm font-medium text-gray-600",children:"Type"}),(0,d.jsx)("th",{className:"text-left py-3 px-4 text-sm font-medium text-gray-600",children:"Montant"}),(0,d.jsx)("th",{className:"text-left py-3 px-4 text-sm font-medium text-gray-600",children:"Statut"})]})}),(0,d.jsx)("tbody",{children:n.map(a=>(0,d.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,d.jsx)("td",{className:"py-3 px-4 text-sm text-gray-900 font-medium",children:a.id}),(0,d.jsx)("td",{className:"py-3 px-4 text-sm text-gray-600",children:a.date}),(0,d.jsx)("td",{className:"py-3 px-4 text-sm text-gray-600",children:a.type}),(0,d.jsx)("td",{className:"py-3 px-4 text-sm text-gray-900 font-medium",children:a.amount}),(0,d.jsx)("td",{className:"py-3 px-4",children:(0,d.jsx)("span",{className:`px-2 py-1 text-xs rounded-full ${"Approuv\xe9"===a.status?"bg-green-100 text-green-700":"bg-yellow-100 text-yellow-700"}`,children:a.status})})]},a.id))})]})})]}),(0,d.jsx)("div",{className:"bg-gradient-to-r from-red-50 to-orange-50 rounded-2xl p-8 border border-red-200",children:(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)(j,{className:"w-8 h-8 text-red-600 mr-4 flex-shrink-0"}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:"Contact d'urgence"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:"En cas de sinistre urgent, contactez imm\xe9diatement notre \xe9quipe d'assistance."}),(0,d.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,d.jsxs)("a",{href:"tel:0800123456",className:"flex items-center text-red-600 hover:text-red-700 font-medium",children:[(0,d.jsx)(i,{className:"w-5 h-5 mr-2"}),"0800 123 456 (Gratuit)"]}),(0,d.jsxs)("a",{href:"mailto:<EMAIL>",className:"flex items-center text-red-600 hover:text-red-700 font-medium",children:[(0,d.jsx)(k,{className:"w-5 h-5 mr-2"}),"<EMAIL>"]})]})]})]})}),(0,d.jsxs)("div",{className:"mt-12",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Questions fr\xe9quentes"}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("details",{className:"bg-white rounded-lg p-4 shadow-sm",children:[(0,d.jsx)("summary",{className:"cursor-pointer font-medium text-gray-900",children:"Que faire en cas de sinistre?"}),(0,d.jsx)("p",{className:"mt-2 text-gray-600",children:"Contactez imm\xe9diatement notre service d'urgence, prenez des photos des dommages, et remplissez le formulaire de d\xe9claration dans les 48h."})]}),(0,d.jsxs)("details",{className:"bg-white rounded-lg p-4 shadow-sm",children:[(0,d.jsx)("summary",{className:"cursor-pointer font-medium text-gray-900",children:"Quels documents fournir pour une r\xe9clamation?"}),(0,d.jsx)("p",{className:"mt-2 text-gray-600",children:"Vous devez fournir: photos des dommages, factures originales, rapport de police (si applicable), et tout document justificatif pertinent."})]}),(0,d.jsxs)("details",{className:"bg-white rounded-lg p-4 shadow-sm",children:[(0,d.jsx)("summary",{className:"cursor-pointer font-medium text-gray-900",children:"Puis-je augmenter ma couverture?"}),(0,d.jsx)("p",{className:"mt-2 text-gray-600",children:"Oui, vous pouvez souscrire \xe0 des options suppl\xe9mentaires pour augmenter votre couverture jusqu'\xe0 5 millions MAD. Contactez notre service commercial."})]})]})]})]})]})}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},43828:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(11110),e={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let f=(a,b)=>{let c=(0,d.forwardRef)(({color:c="currentColor",size:f=24,strokeWidth:g=2,absoluteStrokeWidth:h,className:i="",children:j,...k},l)=>(0,d.createElement)("svg",{ref:l,...e,width:f,height:f,stroke:c,strokeWidth:h?24*Number(g)/Number(f):g,className:["lucide",`lucide-${a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim()}`,i].join(" "),...k},[...b.map(([a,b])=>(0,d.createElement)(a,b)),...Array.isArray(j)?j:[j]]));return c.displayName=`${a}`,c}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70679:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(43828).A)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},79134:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(43828).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},80408:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},87032:()=>{},89279:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.default,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(73653),e=c(97714),f=c(85250),g=c(37587),h=c(22369),i=c(1889),j=c(96232),k=c(22841),l=c(46537),m=c(46027),n=c(78559),o=c(75928),p=c(19374),q=c(65971),r=c(261),s=c(79898),t=c(32967),u=c(26713),v=c(40139),w=c(14248),x=c(59580),y=c(57749),z=c(53123),A=c(89745),B=c(86439),C=c(96133),D=c(18283),E=c(39818),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["host",{children:["insurance",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,12545)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/host/insurance/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,56035)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,74827)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/error.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,96133)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/global-error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,72993)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,15034,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,54693,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/host/insurance/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/host/insurance/page",pathname:"/host/insurance",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",relativeProjectDir:""});async function K(a,b,d){var F;let L="/host/insurance/page";"/index"===L&&(L="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await J.prepare(a,b,{srcPage:L,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(L),{isOnDemandRevalidate:ah}=O,ai=J.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(F=$.routes[ag]??$.dynamicRoutes[ag])?void 0:F.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===J.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&J.isDev&&(aB=aa),J.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...D,tree:G,pages:H,GlobalError:C.default,handler:K,routeModule:J,__next_app__:I};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:L,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=J.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:J,page:L,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),J.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!J.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===J.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await J.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await J.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!J.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&E.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:L,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},92061:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(43828).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4635,2922,4367],()=>b(b.s=89279));module.exports=c})();
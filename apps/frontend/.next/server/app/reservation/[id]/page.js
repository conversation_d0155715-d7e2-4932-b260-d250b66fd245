(()=>{var a={};a.id=2775,a.ids=[2775],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15417:(a,b,c)=>{"use strict";c.d(b,{MbnbImageGallery:()=>i,MbnbResponsiveImage:()=>g,useMbnbImagePreload:()=>h});var d=c(78157),e=c(31768),f=c.n(e);function g({baseName:a,folder:b,alt:c,className:e="",priority:f=!1,onClick:g}){let h=`/Storytelling/optimized/${b}/${a}`;return(0,d.jsxs)("picture",{className:e,onClick:g,children:[(0,d.jsx)("source",{srcSet:`${h}_mobile_375w.avif`,media:"(max-width: 375px)",type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_mobile_375w.webp`,media:"(max-width: 375px)",type:"image/webp"}),(0,d.jsx)("source",{srcSet:`${h}_mobile_375w.jpg`,media:"(max-width: 375px)",type:"image/jpeg"}),(0,d.jsx)("source",{srcSet:`${h}_mobile_414w.avif`,media:"(max-width: 414px)",type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_mobile_414w.webp`,media:"(max-width: 414px)",type:"image/webp"}),(0,d.jsx)("source",{srcSet:`${h}_mobile_414w.jpg`,media:"(max-width: 414px)",type:"image/jpeg"}),(0,d.jsx)("source",{srcSet:`${h}_mobile_480w.avif`,media:"(max-width: 480px)",type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_mobile_480w.webp`,media:"(max-width: 480px)",type:"image/webp"}),(0,d.jsx)("source",{srcSet:`${h}_mobile_480w.jpg`,media:"(max-width: 480px)",type:"image/jpeg"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_768w.avif`,media:"(max-width: 768px)",type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_768w.webp`,media:"(max-width: 768px)",type:"image/webp"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_768w.jpg`,media:"(max-width: 768px)",type:"image/jpeg"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_834w.avif`,media:"(max-width: 834px)",type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_834w.webp`,media:"(max-width: 834px)",type:"image/webp"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_834w.jpg`,media:"(max-width: 834px)",type:"image/jpeg"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_1024w.avif`,media:"(max-width: 1024px)",type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_1024w.webp`,media:"(max-width: 1024px)",type:"image/webp"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_1024w.jpg`,media:"(max-width: 1024px)",type:"image/jpeg"}),(0,d.jsx)("source",{srcSet:`${h}_desktop_1200w.avif`,media:"(max-width: 1200px)",type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_desktop_1200w.webp`,media:"(max-width: 1200px)",type:"image/webp"}),(0,d.jsx)("source",{srcSet:`${h}_desktop_1200w.jpg`,media:"(max-width: 1200px)",type:"image/jpeg"}),(0,d.jsx)("source",{srcSet:`${h}_desktop_1440w.avif`,media:"(max-width: 1440px)",type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_desktop_1440w.webp`,media:"(max-width: 1440px)",type:"image/webp"}),(0,d.jsx)("source",{srcSet:`${h}_desktop_1440w.jpg`,media:"(max-width: 1440px)",type:"image/jpeg"}),(0,d.jsx)("source",{srcSet:`${h}_desktop_1920w.avif`,type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_desktop_1920w.webp`,type:"image/webp"}),(0,d.jsx)("img",{src:`${h}_desktop_1920w.jpg`,alt:c,loading:f?"eager":"lazy",className:`${e} w-full`,decoding:"async"})]})}function h(a,b){}function i({images:a,className:b=""}){let[c,e]=f().useState(null);return(0,d.jsxs)("div",{className:`grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 ${b}`,children:[a.map((a,b)=>(0,d.jsxs)("div",{className:"relative group cursor-pointer overflow-hidden rounded-lg",onClick:()=>e(b),children:[(0,d.jsx)(g,{baseName:a.baseName,folder:a.folder,alt:a.alt,className:"transition-transform duration-300 group-hover:scale-110",priority:b<4}),(0,d.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity duration-300"})]},`${a.folder}-${a.baseName}`)),null!==c&&(0,d.jsx)("div",{className:"fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center p-4",onClick:()=>e(null),children:(0,d.jsx)(g,{baseName:a[c].baseName,folder:a[c].folder,alt:a[c].alt,className:"max-w-full max-h-full object-contain",priority:!0})})]})}},16563:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20318:(a,b,c)=>{"use strict";c.d(b,{$p:()=>k,U8:()=>e,Zg:()=>d,Zv:()=>i,a0:()=>f,bQ:()=>g,cg:()=>j,eo:()=>h,k5:()=>l,y$:()=>m});var d=function(a){return a.CMI="CMI",a.WAFACASH="WAFACASH",a.CASH_PLUS="CASH_PLUS",a.ALI_PAY="ALI_PAY",a.GOOGLE_PAY="GOOGLE_PAY",a.APPLE_PAY="APPLE_PAY",a}({}),e=function(a){return a.MAD="MAD",a.EUR="EUR",a.USD="USD",a.GBP="GBP",a.CAD="CAD",a.AUD="AUD",a.CNY="CNY",a.RUB="RUB",a}({}),f=function(a){return a.RIAD_AUTHENTIQUE="RIAD_AUTHENTIQUE",a.VILLA_COLONIALE="VILLA_COLONIALE",a.MAISON_TRADITIONNELLE="MAISON_TRADITIONNELLE",a.APPARTEMENT_MODERNE="APPARTEMENT_MODERNE",a.KASBAH_HISTORIQUE="KASBAH_HISTORIQUE",a.DAR_FAMILIAL="DAR_FAMILIAL",a}({}),g=function(a){return a.CULTURAL_HERITAGE="CULTURAL_HERITAGE",a.ADVENTURE_SPORTS="ADVENTURE_SPORTS",a.CULINARY_EXPERIENCE="CULINARY_EXPERIENCE",a.DESERT_EXPEDITION="DESERT_EXPEDITION",a.MOUNTAIN_TREKKING="MOUNTAIN_TREKKING",a.COASTAL_ACTIVITIES="COASTAL_ACTIVITIES",a.ARTISAN_WORKSHOPS="ARTISAN_WORKSHOPS",a.SPIRITUAL_WELLNESS="SPIRITUAL_WELLNESS",a}({}),h=function(a){return a.EXPLORATEUR="EXPLORATEUR",a.AVENTURIER="AVENTURIER",a.AMBASSADEUR="AMBASSADEUR",a.LEGENDE="LEGENDE",a}({}),i=function(a){return a.DISCOUNT_FUTURE_BOOKING="DISCOUNT_FUTURE_BOOKING",a.UPGRADE="UPGRADE",a.EXPERIENCE="EXPERIENCE",a.CASHBACK="CASHBACK",a}({}),j=function(a){return a.PENDING="PENDING",a.CONFIRMED="CONFIRMED",a.CANCELLED="CANCELLED",a.COMPLETED="COMPLETED",a.DISPUTED="DISPUTED",a.CHECK_IN="CHECK_IN",a.CHECK_OUT="CHECK_OUT",a.REFUNDED="REFUNDED",a.NO_SHOW="NO_SHOW",a}({}),k=function(a){return a.COMPLETED="completed",a.PENDING="pending",a.PROCESSING="processing",a.FAILED="failed",a}({}),l=function(a){return a.PHASE_1="PHASE_1",a.PHASE_2="PHASE_2",a.PHASE_3="PHASE_3",a}({});let m={MAX_CUMULATIVE_DISCOUNT:.25,MIN_AMOUNT_THRESHOLD:500,ACTIVITIES_EXCLUDED:!0,COMMISSION_RATE_ACTIVITIES:.18,FAMILY_MIN_TRAVELERS:4,LONG_STAY_WEEKLY_NIGHTS:7,LONG_STAY_MONTHLY_NIGHTS:28,LONG_STAY_QUARTERLY_NIGHTS:84,ATLAS_REGIONS:["fes-meknes","draa-tafilalet","beni-mellal-khenifra"]}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34293:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.default,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(73653),e=c(97714),f=c(85250),g=c(37587),h=c(22369),i=c(1889),j=c(96232),k=c(22841),l=c(46537),m=c(46027),n=c(78559),o=c(75928),p=c(19374),q=c(65971),r=c(261),s=c(79898),t=c(32967),u=c(26713),v=c(40139),w=c(14248),x=c(59580),y=c(57749),z=c(53123),A=c(89745),B=c(86439),C=c(96133),D=c(18283),E=c(39818),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["reservation",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,72331)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/reservation/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,56035)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,74827)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/error.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,96133)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/global-error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,72993)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,15034,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,54693,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/reservation/[id]/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/reservation/[id]/page",pathname:"/reservation/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",relativeProjectDir:""});async function K(a,b,d){var F;let L="/reservation/[id]/page";"/index"===L&&(L="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await J.prepare(a,b,{srcPage:L,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(L),{isOnDemandRevalidate:ah}=O,ai=J.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(F=$.routes[ag]??$.dynamicRoutes[ag])?void 0:F.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===J.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&J.isDev&&(aB=aa),J.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...D,tree:G,pages:H,GlobalError:C.default,handler:K,routeModule:J,__next_app__:I};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:L,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=J.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:J,page:L,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),J.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!J.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===J.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await J.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await J.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!J.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&E.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:L,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},35621:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>n});var d=c(78157),e=c(31768),f=c(71159),g=c(91795),h=c(16563),i=c(68322),j=c(87817),k=c(21956),l=c(20318),m=c(14196);function n(){let a=(0,f.useRouter)(),b=(0,f.useParams)(),c=(0,f.useSearchParams)(),n=b.id,{data:o,isLoading:p}=(0,k.wc)(n),[q,r]=(0,e.useState)(1),[s,t]=(0,e.useState)(!1),u=(0,m.MZ)(),v=c.get("checkIn")||u.toISOString().split("T")[0],w=(0,m.fi)(u,1),x=c.get("checkOut")||w.toISOString().split("T")[0],y=c.get("adults")||"2",z=c.get("children")||"0",A=c.get("infants")||"0",B=(0,m.Io)(v),C=Math.ceil(((0,m.Io)(x).getTime()-B.getTime())/864e5),D=o?.pricing.basePrice||0,E=D*C,F=Math.round(.13*E),G=E+F+200,[H,I]=(0,e.useState)({firstName:"",lastName:"",email:"",phone:"",paymentMethod:l.Zg.CMI}),J=()=>{q<3&&r(q+1)},K=()=>{q>1&&r(q-1)},L=async()=>{t(!0),setTimeout(()=>{r(4),t(!1)},2e3)};return p?(0,d.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-mbnb-coral mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Chargement de la r\xe9servation..."})]})}):o?(0,d.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-8",children:[(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsxs)("button",{onClick:()=>a.back(),className:"flex items-center text-gray-600 hover:text-gray-900 mb-4",children:[(0,d.jsx)(g.A,{className:"w-5 h-5 mr-2"}),"Retour"]}),(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:4===q?"R\xe9servation confirm\xe9e!":"Confirmer et payer"})]}),q<4&&(0,d.jsx)("div",{className:"flex items-center justify-center mb-8",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:`flex items-center justify-center w-10 h-10 rounded-full ${q>=1?"bg-mbnb-coral text-white":"bg-gray-200 text-gray-500"}`,children:"1"}),(0,d.jsx)("div",{className:`h-1 w-24 ${q>=2?"bg-mbnb-coral":"bg-gray-200"}`}),(0,d.jsx)("div",{className:`flex items-center justify-center w-10 h-10 rounded-full ${q>=2?"bg-mbnb-coral text-white":"bg-gray-200 text-gray-500"}`,children:"2"}),(0,d.jsx)("div",{className:`h-1 w-24 ${q>=3?"bg-mbnb-coral":"bg-gray-200"}`}),(0,d.jsx)("div",{className:`flex items-center justify-center w-10 h-10 rounded-full ${q>=3?"bg-mbnb-coral text-white":"bg-gray-200 text-gray-500"}`,children:"3"})]})}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,d.jsxs)("div",{className:"lg:col-span-2",children:[1===q&&(0,d.jsxs)("div",{className:"bg-white rounded-xl shadow-sm p-6",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold mb-6",children:"Vos informations"}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Pr\xe9nom"}),(0,d.jsx)("input",{type:"text",value:H.firstName,onChange:a=>I({...H,firstName:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-mbnb-coral focus:border-mbnb-coral",placeholder:"Jean"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nom"}),(0,d.jsx)("input",{type:"text",value:H.lastName,onChange:a=>I({...H,lastName:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-mbnb-coral focus:border-mbnb-coral",placeholder:"Dupont"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,d.jsx)("input",{type:"email",value:H.email,onChange:a=>I({...H,email:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-mbnb-coral focus:border-mbnb-coral",placeholder:"<EMAIL>"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"T\xe9l\xe9phone"}),(0,d.jsx)("input",{type:"tel",value:H.phone,onChange:a=>I({...H,phone:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-mbnb-coral focus:border-mbnb-coral",placeholder:"+212 6XX XXX XXX"})]})]}),(0,d.jsx)("button",{onClick:J,className:"w-full mt-6 bg-mbnb-coral text-white py-3 px-6 rounded-lg font-semibold hover:bg-mbnb-coral-dark transition-colors",children:"Continuer"})]}),2===q&&(0,d.jsxs)("div",{className:"bg-white rounded-xl shadow-sm p-6",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold mb-6",children:"Mode de paiement"}),(0,d.jsx)("div",{className:"space-y-3",children:[{gateway:l.Zg.CMI,label:"CMI (Banque Marocaine)"},{gateway:l.Zg.WAFACASH,label:"WafaCash"},{gateway:l.Zg.CASH_PLUS,label:"CashPlus"},{gateway:l.Zg.GOOGLE_PAY,label:"Google Pay"},{gateway:l.Zg.APPLE_PAY,label:"Apple Pay"}].map(a=>(0,d.jsxs)("label",{className:"flex items-center p-4 border rounded-lg cursor-pointer hover:border-mbnb-coral",children:[(0,d.jsx)("input",{type:"radio",name:"payment",value:a.gateway,checked:H.paymentMethod===a.gateway,onChange:a=>I({...H,paymentMethod:a.target.value}),className:"mr-3 text-mbnb-coral focus:ring-mbnb-coral"}),(0,d.jsx)("span",{className:"font-medium",children:a.label})]},a.gateway))}),(0,d.jsxs)("div",{className:"flex gap-4 mt-6",children:[(0,d.jsx)("button",{onClick:K,className:"flex-1 bg-gray-200 text-gray-700 py-3 px-6 rounded-lg font-semibold hover:bg-gray-300 transition-colors",children:"Retour"}),(0,d.jsx)("button",{onClick:J,className:"flex-1 bg-mbnb-coral text-white py-3 px-6 rounded-lg font-semibold hover:bg-mbnb-coral-dark transition-colors",children:"Continuer"})]})]}),3===q&&(0,d.jsxs)("div",{className:"bg-white rounded-xl shadow-sm p-6",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold mb-6",children:"V\xe9rifier et confirmer"}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,d.jsx)("h3",{className:"font-semibold mb-2",children:"Informations personnelles"}),(0,d.jsxs)("p",{className:"text-gray-600",children:[H.firstName," ",H.lastName]}),(0,d.jsx)("p",{className:"text-gray-600",children:H.email}),(0,d.jsx)("p",{className:"text-gray-600",children:H.phone})]}),(0,d.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,d.jsx)("h3",{className:"font-semibold mb-2",children:"Paiement"}),(0,d.jsxs)("p",{className:"text-gray-600",children:[H.paymentMethod===l.Zg.CMI&&"CMI (Banque Marocaine)",H.paymentMethod===l.Zg.WAFACASH&&"WafaCash",H.paymentMethod===l.Zg.CASH_PLUS&&"CashPlus",H.paymentMethod===l.Zg.GOOGLE_PAY&&"Google Pay",H.paymentMethod===l.Zg.APPLE_PAY&&"Apple Pay"]})]})]}),(0,d.jsxs)("div",{className:"flex gap-4 mt-6",children:[(0,d.jsx)("button",{onClick:K,className:"flex-1 bg-gray-200 text-gray-700 py-3 px-6 rounded-lg font-semibold hover:bg-gray-300 transition-colors",disabled:s,children:"Retour"}),(0,d.jsx)("button",{onClick:L,disabled:s,className:"flex-1 bg-mbnb-coral text-white py-3 px-6 rounded-lg font-semibold hover:bg-mbnb-coral-dark transition-colors disabled:opacity-50",children:s?"Traitement...":"Confirmer la r\xe9servation"})]})]}),4===q&&(0,d.jsxs)("div",{className:"bg-white rounded-xl shadow-sm p-6 text-center",children:[(0,d.jsx)(h.A,{className:"w-16 h-16 text-green-500 mx-auto mb-4"}),(0,d.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"R\xe9servation confirm\xe9e!"}),(0,d.jsxs)("p",{className:"text-gray-600 mb-6",children:["Vous recevrez un email de confirmation \xe0 ",H.email]}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)("button",{onClick:()=>a.push("/profile"),className:"w-full bg-mbnb-coral text-white py-3 px-6 rounded-lg font-semibold hover:bg-mbnb-coral-dark transition-colors",children:"Voir mes r\xe9servations"}),(0,d.jsx)("button",{onClick:()=>a.push("/"),className:"w-full bg-gray-200 text-gray-700 py-3 px-6 rounded-lg font-semibold hover:bg-gray-300 transition-colors",children:"Retour \xe0 l'accueil"})]})]})]}),(0,d.jsx)("div",{className:"lg:col-span-1",children:(0,d.jsxs)("div",{className:"bg-white rounded-xl shadow-sm p-6 sticky top-4",children:[(0,d.jsxs)("div",{className:"flex gap-4 mb-4",children:[(0,d.jsx)("div",{className:"relative w-24 h-24 rounded-lg overflow-hidden",children:(0,d.jsx)(j.XF,{baseName:o.media.images[0]?.baseName||"architecture_maroc_riad_bleu_majorelle_001",folder:o.media.images[0]?.folder||"impact-eleve/ACTE_1_EVEIL",alt:o.title,className:"object-cover"})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-900 line-clamp-2",children:o.title}),(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:[o.location.city,", ",o.location.region]}),(0,d.jsxs)("div",{className:"flex items-center mt-1",children:[(0,d.jsx)(i.A,{className:"w-4 h-4 text-mbnb-coral"}),(0,d.jsx)("span",{className:"text-sm font-medium ml-1",children:o.ratings.overall}),(0,d.jsxs)("span",{className:"text-sm text-gray-500 ml-1",children:["(",o.ratings.totalReviews," avis)"]})]})]})]}),(0,d.jsx)("div",{className:"border-t pt-4",children:(0,d.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Dates"}),(0,d.jsxs)("span",{className:"font-medium",children:[v," → ",x]})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Voyageurs"}),(0,d.jsxs)("span",{className:"font-medium",children:[y," adultes",parseInt(z)>0&&`, ${z} enfants`,parseInt(A)>0&&`, ${A} b\xe9b\xe9s`]})]})]})}),(0,d.jsxs)("div",{className:"border-t pt-4 mt-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsxs)("span",{className:"text-gray-600",children:[D.toLocaleString()," ",o.pricing.currency," \xd7 ",C," nuits"]}),(0,d.jsxs)("span",{className:"font-medium",children:[E.toLocaleString()," ",o.pricing.currency]})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Frais de service (13%)"}),(0,d.jsxs)("span",{className:"font-medium",children:[F.toLocaleString()," ",o.pricing.currency]})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Frais de nettoyage"}),(0,d.jsxs)("span",{className:"font-medium",children:[200..toLocaleString()," MAD"]})]})]}),(0,d.jsx)("div",{className:"border-t pt-4 mt-4",children:(0,d.jsxs)("div",{className:"flex justify-between text-lg font-bold",children:[(0,d.jsx)("span",{children:"Total"}),(0,d.jsxs)("span",{children:[G.toLocaleString()," ",o.pricing.currency]})]})})]})]})})]})]})}):(0,d.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Propri\xe9t\xe9 introuvable"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:"Cette propri\xe9t\xe9 n'existe pas ou a \xe9t\xe9 supprim\xe9e."}),(0,d.jsx)("button",{onClick:()=>a.back(),className:"bg-mbnb-coral text-white px-6 py-3 rounded-lg hover:bg-mbnb-coral-dark transition-colors",children:"Retour"})]})})}},36266:(a,b,c)=>{Promise.resolve().then(c.bind(c,72331))},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68322:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.006 5.404.434c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.434 2.082-5.005Z",clipRule:"evenodd"}))})},71159:(a,b,c)=>{"use strict";var d=c(30291);c.o(d,"useParams")&&c.d(b,{useParams:function(){return d.useParams}}),c.o(d,"usePathname")&&c.d(b,{usePathname:function(){return d.usePathname}}),c.o(d,"useRouter")&&c.d(b,{useRouter:function(){return d.useRouter}}),c.o(d,"useSearchParams")&&c.d(b,{useSearchParams:function(){return d.useSearchParams}})},72331:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(25459).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/reservation/[id]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/reservation/[id]/page.tsx","default")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},87817:(a,b,c)=>{"use strict";c.d(b,{XF:()=>d.MbnbResponsiveImage});var d=c(15417)},91795:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))})},99314:(a,b,c)=>{Promise.resolve().then(c.bind(c,35621))}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4635,2922,4380,4367,1956],()=>b(b.s=34293));module.exports=c})();
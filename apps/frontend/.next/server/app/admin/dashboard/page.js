(()=>{var a={};a.id=5957,a.ids=[5957],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3013:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4768).A)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4768:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(31768),e={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let f=(a,b)=>{let c=(0,d.forwardRef)(({color:c="currentColor",size:f=24,strokeWidth:g=2,absoluteStrokeWidth:h,className:i="",children:j,...k},l)=>(0,d.createElement)("svg",{ref:l,...e,width:f,height:f,stroke:c,strokeWidth:h?24*Number(g)/Number(f):g,className:["lucide",`lucide-${a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim()}`,i].join(" "),...k},[...b.map(([a,b])=>(0,d.createElement)(a,b)),...Array.isArray(j)?j:[j]]));return c.displayName=`${a}`,c}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29232:(a,b,c)=>{Promise.resolve().then(c.bind(c,76611))},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29957:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.default,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(73653),e=c(97714),f=c(85250),g=c(37587),h=c(22369),i=c(1889),j=c(96232),k=c(22841),l=c(46537),m=c(46027),n=c(78559),o=c(75928),p=c(19374),q=c(65971),r=c(261),s=c(79898),t=c(32967),u=c(26713),v=c(40139),w=c(14248),x=c(59580),y=c(57749),z=c(53123),A=c(89745),B=c(86439),C=c(96133),D=c(18283),E=c(39818),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["admin",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,82827)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/admin/dashboard/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,56035)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,74827)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/error.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,96133)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/global-error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,72993)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,15034,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,54693,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/admin/dashboard/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/admin/dashboard/page",pathname:"/admin/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",relativeProjectDir:""});async function K(a,b,d){var F;let L="/admin/dashboard/page";"/index"===L&&(L="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await J.prepare(a,b,{srcPage:L,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(L),{isOnDemandRevalidate:ah}=O,ai=J.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(F=$.routes[ag]??$.dynamicRoutes[ag])?void 0:F.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===J.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&J.isDev&&(aB=aa),J.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...D,tree:G,pages:H,GlobalError:C.default,handler:K,routeModule:J,__next_app__:I};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:L,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=J.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:J,page:L,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),J.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!J.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===J.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await J.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await J.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!J.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&E.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:L,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},57465:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4768).A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75704:(a,b,c)=>{"use strict";c.d(b,{Yq:()=>h,cn:()=>g});var d=c(79390),e=c(25442),f=c(14196);function g(...a){return(0,e.QP)((0,d.$)(a))}function h(a,b="short"){let c="string"==typeof a?(0,f.Io)(a):a;return"short"===b?new Intl.DateTimeFormat("fr-MA",{day:"numeric",month:"short",year:"numeric"}).format(c):new Intl.DateTimeFormat("fr-MA",{weekday:"long",day:"numeric",month:"long",year:"numeric"}).format(c)}},76611:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>u});var d=c(78157),e=c(31768),f=c(14380);class g{constructor(){this.config={baseURL:"http://localhost:3001/api/v1",timeout:3e4,retries:3}}getAdminToken(){return null}async request(a,b,c,d){let e=this.getAdminToken(),f=await fetch(`${this.config.baseURL}${b}`,{method:a,headers:{"Content-Type":"application/json",Authorization:e?`Bearer ${e}`:"","X-Mbnb-Platform":"admin","X-Mbnb-Client":"frontend-admin",...d?.headers},body:c?JSON.stringify(c):void 0,...d});if(!f.ok)throw Error(`Admin API Error: ${f.status} ${f.statusText}`);return f.json()}async get(a,b){return this.request("GET",a,void 0,b)}async post(a,b,c){return this.request("POST",a,b,c)}async put(a,b,c){return this.request("PUT",a,b,c)}async delete(a,b){return this.request("DELETE",a,void 0,b)}async patch(a,b,c){return this.request("PATCH",a,b,c)}async getMetrics(a){return this.get(`/api/v1/admin/metrics?period=${a}`)}async getSystemHealth(){return this.get("/api/v1/admin/health")}async getRecentActivities(){return this.get("/api/v1/admin/activities/recent")}}let h=new g;var i=c(75704),j=c(4768);let k=(0,j.A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),l=(0,j.A)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]),m=(0,j.A)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]),n=(0,j.A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);var o=c(98551),p=c(57465);let q=(0,j.A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]),r=(0,j.A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var s=c(3013);let t=(0,j.A)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);function u(){let[a,b]=(0,e.useState)("month"),[c,g]=(0,e.useState)(3e4),{data:j,isLoading:u}=(0,f.I)({queryKey:["admin-metrics",a],queryFn:()=>h.getMetrics(a),refetchInterval:c}),{data:v,isLoading:w}=(0,f.I)({queryKey:["system-health"],queryFn:()=>h.getSystemHealth(),refetchInterval:1e4}),{data:x}=(0,f.I)({queryKey:["recent-activities"],queryFn:()=>h.getRecentActivities(),refetchInterval:c}),y=a=>new Intl.NumberFormat("fr-MA",{style:"currency",currency:"MAD"}).format(a),z=a=>new Intl.NumberFormat("fr-MA").format(a),A=a=>{switch(a){case"operational":case"healthy":return"text-mbnb-teal-dark";case"degraded":return"text-warning-500";case"down":case"critical":return"text-mbnb-coral-dark";default:return"text-neutral-500"}};return u||w?(0,d.jsx)("div",{className:"min-h-screen bg-neutral-50 flex items-center justify-center",children:(0,d.jsx)("div",{className:"animate-pulse text-mbnb-coral text-lg",children:"Chargement du tableau de bord administrateur Mbnb..."})}):(0,d.jsxs)("div",{className:"min-h-screen bg-neutral-50",children:[(0,d.jsx)("div",{className:"bg-white shadow-sm border-b border-neutral-200",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-mbnb-navy",children:"Tableau de Bord Administrateur Mbnb"}),(0,d.jsx)("p",{className:"text-sm text-neutral-600 mt-1",children:"Supervision compl\xe8te de la plateforme"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsxs)("select",{value:a,onChange:a=>b(a.target.value),className:"px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral focus:border-transparent",children:[(0,d.jsx)("option",{value:"today",children:"Aujourd'hui"}),(0,d.jsx)("option",{value:"week",children:"Cette semaine"}),(0,d.jsx)("option",{value:"month",children:"Ce mois"}),(0,d.jsx)("option",{value:"year",children:"Cette ann\xe9e"})]}),(0,d.jsxs)("select",{value:c,onChange:a=>g(Number(a.target.value)),className:"px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral focus:border-transparent",children:[(0,d.jsx)("option",{value:"10000",children:"10s"}),(0,d.jsx)("option",{value:"30000",children:"30s"}),(0,d.jsx)("option",{value:"60000",children:"1min"}),(0,d.jsx)("option",{value:"300000",children:"5min"})]})]})]})})}),(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[v&&"operational"!==v.api.status&&(0,d.jsx)("div",{className:"mb-6 p-4 bg-warning-50 border border-warning-200 rounded-lg",children:(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(p.A,{className:"w-5 h-5 text-warning-600"}),(0,d.jsx)("span",{className:"font-medium text-warning-900",children:"Attention: Certains services sont d\xe9grad\xe9s"})]})}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-card p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)(k,{className:"w-8 h-8 text-mbnb-teal"}),(0,d.jsxs)("span",{className:"text-sm text-neutral-500",children:[j?.activeUsers||0," actifs"]})]}),(0,d.jsx)("div",{className:"text-2xl font-bold text-mbnb-navy",children:z(j?.totalUsers||0)}),(0,d.jsx)("div",{className:"text-sm text-neutral-600 mt-1",children:"Utilisateurs totaux"})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-card p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)(l,{className:"w-8 h-8 text-mbnb-navy"}),(0,d.jsxs)("span",{className:"text-sm text-neutral-500",children:[j?.verifiedProperties||0," v\xe9rifi\xe9es"]})]}),(0,d.jsx)("div",{className:"text-2xl font-bold text-mbnb-navy",children:z(j?.totalProperties||0)}),(0,d.jsx)("div",{className:"text-sm text-neutral-600 mt-1",children:"Propri\xe9t\xe9s totales"})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-card p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)(n,{className:"w-8 h-8 text-success-600"}),(0,d.jsx)("span",{className:"text-sm text-neutral-500",children:a})]}),(0,d.jsx)("div",{className:"text-2xl font-bold text-mbnb-navy",children:y(j?.totalRevenue||0)}),(0,d.jsx)("div",{className:"text-sm text-neutral-600 mt-1",children:"Revenus totaux"})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-card p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)(q,{className:"w-8 h-8 text-mbnb-coral"}),(0,d.jsx)("span",{className:"text-sm text-neutral-500",children:"14-15% model"})]}),(0,d.jsx)("div",{className:"text-2xl font-bold text-mbnb-navy",children:y(j?.commissionEarned||0)}),(0,d.jsx)("div",{className:"text-sm text-neutral-600 mt-1",children:"Commissions Mbnb"})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-card p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-mbnb-navy mb-4",children:"Sant\xe9 du Syst\xe8me"}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(r,{className:"w-5 h-5 text-neutral-500"}),(0,d.jsx)("span",{className:"text-neutral-700",children:"API Backend"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:`font-medium ${A(v?.api.status||"operational")}`,children:v?.api.status||"operational"}),(0,d.jsxs)("span",{className:"text-sm text-neutral-500",children:[v?.api.latency||0,"ms"]})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(s.A,{className:"w-5 h-5 text-neutral-500"}),(0,d.jsx)("span",{className:"text-neutral-700",children:"Database"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:`font-medium ${A(v?.database.status||"operational")}`,children:v?.database.status||"operational"}),(0,d.jsxs)("span",{className:"text-sm text-neutral-500",children:[v?.database.connections||0," conn"]})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(m,{className:"w-5 h-5 text-neutral-500"}),(0,d.jsx)("span",{className:"text-neutral-700",children:"Cache Redis"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:`font-medium ${A(v?.redis.status||"operational")}`,children:v?.redis.status||"operational"}),(0,d.jsxs)("span",{className:"text-sm text-neutral-500",children:[v?.redis.memory||0,"MB"]})]})]}),(0,d.jsxs)("div",{className:"pt-4 border-t border-neutral-200",children:[(0,d.jsx)("div",{className:"text-sm font-medium text-neutral-700 mb-2",children:"Int\xe9grations Tierces"}),(0,d.jsx)("div",{className:"grid grid-cols-3 gap-2",children:v?.integrations&&Object.entries(v.integrations).map(([a,b])=>(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[b?(0,d.jsx)(o.A,{className:"w-4 h-4 text-mbnb-teal-dark"}):(0,d.jsx)(p.A,{className:"w-4 h-4 text-mbnb-coral-dark"}),(0,d.jsx)("span",{className:"text-xs text-neutral-600 capitalize",children:a})]},a))})]})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-card p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-mbnb-navy mb-4",children:"Activit\xe9s R\xe9centes"}),(0,d.jsxs)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:[x?.map(a=>(0,d.jsxs)("div",{className:"flex items-start gap-3 pb-3 border-b border-neutral-100 last:border-0",children:[(a=>{switch(a){case"user_signup":return(0,d.jsx)(k,{className:"w-4 h-4 text-mbnb-teal"});case"property_listed":return(0,d.jsx)(l,{className:"w-4 h-4 text-mbnb-navy"});case"booking_created":return(0,d.jsx)(m,{className:"w-4 h-4 text-mbnb-coral"});case"payment_processed":return(0,d.jsx)(n,{className:"w-4 h-4 text-success-600"});case"review_posted":return(0,d.jsx)(o.A,{className:"w-4 h-4 text-mbnb-sky"});default:return(0,d.jsx)(p.A,{className:"w-4 h-4 text-neutral-400"})}})(a.type),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)("p",{className:"text-sm text-neutral-800 truncate",children:a.description}),(0,d.jsx)("p",{className:"text-xs text-neutral-500 mt-1",children:(0,i.Yq)(a.timestamp)})]})]},a.id)),(!x||0===x.length)&&(0,d.jsx)("p",{className:"text-center text-neutral-500 py-8",children:"Aucune activit\xe9 r\xe9cente"})]})]})]}),(0,d.jsxs)("div",{className:"mt-8 grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsxs)("button",{onClick:()=>window.location.href="/admin/users",className:"p-4 bg-white rounded-lg shadow-card hover:shadow-elevated transition-shadow flex items-center justify-between group",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)(k,{className:"w-6 h-6 text-mbnb-teal"}),(0,d.jsx)("span",{className:"font-medium text-neutral-700",children:"G\xe9rer les utilisateurs"})]}),(0,d.jsx)("span",{className:"text-mbnb-coral group-hover:translate-x-1 transition-transform",children:"→"})]}),(0,d.jsxs)("button",{onClick:()=>window.location.href="/admin/properties",className:"p-4 bg-white rounded-lg shadow-card hover:shadow-elevated transition-shadow flex items-center justify-between group",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)(l,{className:"w-6 h-6 text-mbnb-navy"}),(0,d.jsx)("span",{className:"font-medium text-neutral-700",children:"G\xe9rer les propri\xe9t\xe9s"})]}),(0,d.jsx)("span",{className:"text-mbnb-coral group-hover:translate-x-1 transition-transform",children:"→"})]}),(0,d.jsxs)("button",{onClick:()=>window.location.href="/admin/reports",className:"p-4 bg-white rounded-lg shadow-card hover:shadow-elevated transition-shadow flex items-center justify-between group",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)(t,{className:"w-6 h-6 text-mbnb-coral"}),(0,d.jsx)("span",{className:"font-medium text-neutral-700",children:"Rapports d\xe9taill\xe9s"})]}),(0,d.jsx)("span",{className:"text-mbnb-coral group-hover:translate-x-1 transition-transform",children:"→"})]})]})]})]})}},82827:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(25459).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/admin/dashboard/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/admin/dashboard/page.tsx","default")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},94384:(a,b,c)=>{Promise.resolve().then(c.bind(c,82827))},98551:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4768).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4635,2922,4380,1797,4367],()=>b(b.s=29957));module.exports=c})();
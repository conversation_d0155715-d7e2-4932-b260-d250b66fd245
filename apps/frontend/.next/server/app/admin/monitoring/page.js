(()=>{var a={};a.id=25,a.ids=[25],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},574:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m3.75 13.5 10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75Z"}))})},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16318:(a,b,c)=>{Promise.resolve().then(c.bind(c,62777))},16563:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29508:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(52661),e=c(7904),f=c(62188);let g=(0,d.vt)()((0,e.Zr)((0,f.D)((a,b)=>({user:null,token:null,refreshToken:null,isAuthenticated:!1,isLoading:!1,error:null,heritageInterests:[],activityPreferences:[],preferredLanguage:"fr",preferredCurrency:"MAD",loginToMbnb:async b=>{a(a=>{a.isLoading=!0,a.error=null});try{let c=await fetch("http://localhost:3001/api/v1/auth/login",{method:"POST",headers:{"Content-Type":"application/json","X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(b)});if(!c.ok){let a=await c.json();throw Error(a.message||"Authentification Mbnb \xe9chou\xe9e")}let d=await c.json();a(a=>{a.user=d.user,a.token=d.token,a.refreshToken=d.refreshToken||null,a.isAuthenticated=!0,a.isLoading=!1,a.preferredLanguage=d.user.profile.preferredLanguage,a.preferredCurrency=d.user.profile.preferredCurrency})}catch(c){let b=c instanceof Error?c.message:"Erreur de connexion Mbnb";throw a(a=>{a.error=b,a.isLoading=!1}),c}},registerToMbnb:async b=>{a(a=>{a.isLoading=!0,a.error=null});try{let c=await fetch("http://localhost:3001/api/v1/auth/register",{method:"POST",headers:{"Content-Type":"application/json","X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(b)});if(!c.ok){let a=await c.json();throw Error(a.message||"Inscription Mbnb \xe9chou\xe9e")}let d=await c.json();a(a=>{a.user=d.user,a.token=d.token,a.refreshToken=d.refreshToken||null,a.isAuthenticated=!0,a.isLoading=!1,a.preferredLanguage=d.user.profile.preferredLanguage,a.preferredCurrency=d.user.profile.preferredCurrency})}catch(c){let b=c instanceof Error?c.message:"Erreur d'inscription Mbnb";throw a(a=>{a.error=b,a.isLoading=!1}),c}},logoutFromMbnb:()=>{a(a=>{a.user=null,a.token=null,a.refreshToken=null,a.isAuthenticated=!1,a.error=null,a.heritageInterests=[],a.activityPreferences=[],a.preferredLanguage="fr",a.preferredCurrency="MAD"}),localStorage.removeItem("mbnb-auth-storage"),window.location.href="/"},refreshMbnbToken:async()=>{let c=b().refreshToken;if(!c)return void b().logoutFromMbnb();try{let b=await fetch("http://localhost:3001/api/v1/auth/refresh",{method:"POST",headers:{"Content-Type":"application/json","X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify({refreshToken:c})});if(!b.ok)throw Error("Refresh token Mbnb expir\xe9");let d=await b.json();a(a=>{a.token=d.token,a.refreshToken=d.refreshToken})}catch(a){b().logoutFromMbnb()}},updateMbnbProfile:async c=>{a(a=>{a.isLoading=!0,a.error=null});try{let d=await fetch("http://localhost:3001/api/v1/users/profile",{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${b().token}`,"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(c)});if(!d.ok){let a=await d.json();throw Error(a.message||"Mise \xe0 jour profil Mbnb \xe9chou\xe9e")}let e=await d.json();a(a=>{a.user=e,a.isLoading=!1,a.preferredLanguage=e.profile.preferredLanguage,a.preferredCurrency=e.profile.preferredCurrency})}catch(c){let b=c instanceof Error?c.message:"Erreur mise \xe0 jour profil Mbnb";throw a(a=>{a.error=b,a.isLoading=!1}),c}},updateMbnbPreferences:async c=>{a(a=>{a.isLoading=!0,a.error=null});try{let d=await fetch("http://localhost:3001/api/v1/users/preferences",{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${b().token}`,"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(c)});if(!d.ok){let a=await d.json();throw Error(a.message||"Mise \xe0 jour pr\xe9f\xe9rences Mbnb \xe9chou\xe9e")}a(a=>{c.heritageInterests&&(a.heritageInterests=c.heritageInterests),c.activityPreferences&&(a.activityPreferences=c.activityPreferences),c.preferredLanguage&&(a.preferredLanguage=c.preferredLanguage),c.preferredCurrency&&(a.preferredCurrency=c.preferredCurrency),a.isLoading=!1})}catch(c){let b=c instanceof Error?c.message:"Erreur mise \xe0 jour pr\xe9f\xe9rences Mbnb";throw a(a=>{a.error=b,a.isLoading=!1}),c}},verifyMbnbEmail:async c=>{let d=await fetch("http://localhost:3001/api/v1/auth/verify-email",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${b().token}`,"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify({code:c})});if(!d.ok)throw Error((await d.json()).message||"V\xe9rification email Mbnb \xe9chou\xe9e");a(a=>{a.user&&(a.user.verification.emailVerified=!0)})},verifyMbnbPhone:async c=>{let d=await fetch("http://localhost:3001/api/v1/auth/verify-phone",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${b().token}`,"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify({code:c})});if(!d.ok)throw Error((await d.json()).message||"V\xe9rification t\xe9l\xe9phone Mbnb \xe9chou\xe9e");a(a=>{a.user&&(a.user.verification.phoneVerified=!0)})},clearError:()=>a(a=>{a.error=null})})),{name:"mbnb-auth-storage",storage:(0,e.KU)(()=>localStorage),partialize:a=>({user:a.user,token:a.token,refreshToken:a.refreshToken,isAuthenticated:a.isAuthenticated,heritageInterests:a.heritageInterests,activityPreferences:a.activityPreferences,preferredLanguage:a.preferredLanguage,preferredCurrency:a.preferredCurrency})}))},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},44173:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},62777:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(25459).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/admin/monitoring/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/admin/monitoring/page.tsx","default")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66765:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.default,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(73653),e=c(97714),f=c(85250),g=c(37587),h=c(22369),i=c(1889),j=c(96232),k=c(22841),l=c(46537),m=c(46027),n=c(78559),o=c(75928),p=c(19374),q=c(65971),r=c(261),s=c(79898),t=c(32967),u=c(26713),v=c(40139),w=c(14248),x=c(59580),y=c(57749),z=c(53123),A=c(89745),B=c(86439),C=c(96133),D=c(18283),E=c(39818),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["admin",{children:["monitoring",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,62777)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/admin/monitoring/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,56035)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,74827)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/error.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,96133)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/global-error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,72993)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,15034,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,54693,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/admin/monitoring/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/admin/monitoring/page",pathname:"/admin/monitoring",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",relativeProjectDir:""});async function K(a,b,d){var F;let L="/admin/monitoring/page";"/index"===L&&(L="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await J.prepare(a,b,{srcPage:L,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(L),{isOnDemandRevalidate:ah}=O,ai=J.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(F=$.routes[ag]??$.dynamicRoutes[ag])?void 0:F.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===J.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&J.isDev&&(aB=aa),J.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...D,tree:G,pages:H,GlobalError:C.default,handler:K,routeModule:J,__next_app__:I};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:L,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=J.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:J,page:L,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),J.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!J.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===J.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await J.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await J.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!J.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&E.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:L,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},71159:(a,b,c)=>{"use strict";var d=c(30291);c.o(d,"useParams")&&c.d(b,{useParams:function(){return d.useParams}}),c.o(d,"usePathname")&&c.d(b,{usePathname:function(){return d.usePathname}}),c.o(d,"useRouter")&&c.d(b,{useRouter:function(){return d.useRouter}}),c.o(d,"useSearchParams")&&c.d(b,{useSearchParams:function(){return d.useSearchParams}})},73046:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>z});var d=c(78157),e=c(31768),f=c(71159),g=c(14196),h=c(16563),i=c(88082),j=c(15455),k=c(44173),l=c(574),m=c(96064),n=c(90025);let o=e.forwardRef(function({title:a,titleId:b,...c},d){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:d,"aria-labelledby":b},c),a?e.createElement("title",{id:b},a):null,e.createElement("path",{fillRule:"evenodd",d:"M11.47 2.47a.75.75 0 0 1 1.06 0l7.5 7.5a.75.75 0 1 1-1.06 1.06l-6.22-6.22V21a.75.75 0 0 1-1.5 0V4.81l-6.22 6.22a.75.75 0 1 1-1.06-1.06l7.5-7.5Z",clipRule:"evenodd"}))}),p=e.forwardRef(function({title:a,titleId:b,...c},d){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:d,"aria-labelledby":b},c),a?e.createElement("title",{id:b},a):null,e.createElement("path",{fillRule:"evenodd",d:"M12 2.25a.75.75 0 0 1 .75.75v16.19l6.22-6.22a.75.75 0 1 1 1.06 1.06l-7.5 7.5a.75.75 0 0 1-1.06 0l-7.5-7.5a.75.75 0 1 1 1.06-1.06l6.22 6.22V3a.75.75 0 0 1 .75-.75Z",clipRule:"evenodd"}))});var q=c(29508),r=c(52661),s=c(7904);let t={MAD:{code:"MAD",name:"Dirham Marocain",nameAr:"درهم مغربي",symbol:"DH",flag:"\uD83C\uDDF2\uD83C\uDDE6",rate:1,decimals:2,locale:"ar-MA",region:["MA","North Africa"],popular:!0},EUR:{code:"EUR",name:"Euro",nameAr:"يورو",symbol:"€",flag:"\uD83C\uDDEA\uD83C\uDDFA",rate:.094,decimals:2,locale:"fr-FR",region:["EU","Europe"],popular:!0},USD:{code:"USD",name:"Dollar Am\xe9ricain",nameAr:"دولار أمريكي",symbol:"$",flag:"\uD83C\uDDFA\uD83C\uDDF8",rate:.1,decimals:2,locale:"en-US",region:["US","Americas"],popular:!0},GBP:{code:"GBP",name:"Livre Sterling",nameAr:"جنيه إسترليني",symbol:"\xa3",flag:"\uD83C\uDDEC\uD83C\uDDE7",rate:.081,decimals:2,locale:"en-GB",region:["GB","Europe"],popular:!0},CAD:{code:"CAD",name:"Dollar Canadien",nameAr:"دولار كندي",symbol:"CA$",flag:"\uD83C\uDDE8\uD83C\uDDE6",rate:.135,decimals:2,locale:"en-CA",region:["CA","Americas"],popular:!0},AUD:{code:"AUD",name:"Dollar Australien",nameAr:"دولار أسترالي",symbol:"AU$",flag:"\uD83C\uDDE6\uD83C\uDDFA",rate:.15,decimals:2,locale:"en-AU",region:["AU","Oceania"],popular:!0},CNY:{code:"CNY",name:"Yuan Chinois",nameAr:"يوان صيني",symbol:"\xa5",flag:"\uD83C\uDDE8\uD83C\uDDF3",rate:.72,decimals:2,locale:"zh-CN",region:["CN","Asia"],popular:!0},RUB:{code:"RUB",name:"Rouble Russe",nameAr:"روبل روسي",symbol:"₽",flag:"\uD83C\uDDF7\uD83C\uDDFA",rate:9.2,decimals:2,locale:"ru-RU",region:["RU","Europe","Asia"],popular:!0}},u={Morocco:["MAD"],Europe:["EUR","GBP","RUB"],Americas:["USD","CAD"],Asia:["CNY","RUB"],Oceania:["AUD"],Africa:["MAD"]},v=["MAD","EUR","USD","GBP","CAD","AUD","CNY","RUB"];class w{static{this.rates={base:"MAD",rates:Object.fromEntries(Object.entries(t).map(([a,b])=>[a,b.rate])),lastUpdated:(0,g.MZ)()}}static convert(a,b,c){let d=this.rates.rates[b],e=this.rates.rates[c];if(!d||!e)throw Error(`Devise non support\xe9e: ${b} ou ${c}`);let f=a/d*e;return{from:b,to:c,amount:a,convertedAmount:Math.round(100*f)/100,rate:Math.round(e/d*1e6)/1e6,formatted:this.formatAmount(f,c)}}static formatAmount(a,b,c=!0){let d=t[b];if(!d)throw Error(`Devise non support\xe9e: ${b}`);let e=new Intl.NumberFormat(d.locale,{style:"decimal",minimumFractionDigits:d.decimals,maximumFractionDigits:d.decimals}).format(a);return c?`${e} ${d.symbol}`:e}static formatCurrency(a,b){let c=t[b];if(!c)throw Error(`Devise non support\xe9e: ${b}`);return new Intl.NumberFormat(c.locale,{style:"currency",currency:b,minimumFractionDigits:c.decimals,maximumFractionDigits:c.decimals}).format(a)}static getCurrencyInfo(a){let b=t[a];if(!b)throw Error(`Devise non support\xe9e: ${a}`);return b}static getAllCurrencies(){return Object.values(t)}static getPopularCurrencies(){return v.map(a=>t[a])}static getCurrenciesByRegion(a){return(u[a]||[]).map(a=>t[a])}static detectCurrencyByLocation(a){let b={MA:"MAD",FR:"EUR",ES:"EUR",IT:"EUR",DE:"EUR",NL:"EUR",BE:"EUR",US:"USD",GB:"GBP",CA:"CAD",AU:"AUD",CN:"CNY",RU:"RUB"};if(a&&b[a])return b[a];let c=Intl.DateTimeFormat().resolvedOptions().timeZone;if(c.includes("Casablanca")||c.includes("Africa"));else if(c.includes("Europe"))return"EUR";else if(c.includes("America/New_York")||c.includes("America/Los_Angeles"))return"USD";else if(c.includes("Asia/Shanghai")||c.includes("Asia/Hong_Kong"))return"CNY";else if(c.includes("Europe/Moscow"))return"RUB";return"MAD"}static async updateRates(){this.rates.lastUpdated=(0,g.MZ)(),Object.keys(this.rates.rates).forEach(a=>{if("MAD"!==a){let b=t[a].rate,c=(Math.random()-.5)*.02;this.rates.rates[a]=b*(1+c)}})}static getRates(){return{...this.rates}}static convertBulk(a){return a.map(({amount:a,from:b,to:c})=>this.convert(a,b,c))}static getExchangeRate(a,b){let c=this.rates.rates[a],d=this.rates.rates[b];if(!c||!d)throw Error(`Devise non support\xe9e: ${a} ou ${b}`);return d/c}static isValidCurrency(a){return a in t}}let x=(0,r.vt)()((0,s.Zr)((a,b)=>({selectedCurrency:"MAD",defaultCurrency:"MAD",rates:w.getRates(),isLoading:!1,lastUpdated:null,autoDetectLocation:!0,setCurrency:b=>{if(!w.isValidCurrency(b))return void console.warn(`[Currency Store] Devise invalide: ${b}`);a({selectedCurrency:b})},setDefaultCurrency:b=>{if(!w.isValidCurrency(b))return void console.warn(`[Currency Store] Devise par d\xe9faut invalide: ${b}`);a({defaultCurrency:b})},updateRates:async()=>{a({isLoading:!0});try{await w.updateRates();let b=w.getRates();a({rates:b,lastUpdated:(0,g.MZ)(),isLoading:!1}),console.log("[Currency Store] Taux de change mis \xe0 jour:",b.lastUpdated)}catch(b){console.error("[Currency Store] Erreur lors de la mise \xe0 jour des taux:",b),a({isLoading:!1})}},detectCurrencyByLocation:async()=>{if(b().autoDetectLocation)try{if(navigator.geolocation)navigator.geolocation.getCurrentPosition(async()=>{try{let c=w.detectCurrencyByLocation();c!==b().selectedCurrency&&(a({selectedCurrency:c}),console.log(`[Currency Store] Devise d\xe9tect\xe9e: ${c}`))}catch(a){console.warn("[Currency Store] Erreur lors de la d\xe9tection g\xe9ographique:",a)}},c=>{console.warn("[Currency Store] G\xe9olocalisation refus\xe9e:",c.message);let d=w.detectCurrencyByLocation();d!==b().selectedCurrency&&a({selectedCurrency:d})});else{let c=w.detectCurrencyByLocation();c!==b().selectedCurrency&&a({selectedCurrency:c})}}catch(a){console.error("[Currency Store] Erreur lors de la d\xe9tection:",a)}},resetToDefault:()=>{let{defaultCurrency:c}=b();a({selectedCurrency:c})},toggleAutoDetect:()=>{let c=!b().autoDetectLocation;a({autoDetectLocation:c}),c&&b().detectCurrencyByLocation()},convert:(a,c,d)=>{let{selectedCurrency:e}=b(),f=c||e,g=d||e;return w.convert(a,f,g)},formatAmount:(a,c,d=!0)=>{let{selectedCurrency:e}=b();return w.formatAmount(a,c||e,d)},formatCurrency:(a,c)=>{let{selectedCurrency:d}=b();return w.formatCurrency(a,c||d)},getExchangeRate:(a,b)=>w.getExchangeRate(a,b)}),{name:"mbnb-currency-store",version:1,migrate:(a,b)=>0===b?{...a,autoDetectLocation:!0,lastUpdated:null}:a,partialize:a=>({selectedCurrency:a.selectedCurrency,defaultCurrency:a.defaultCurrency,autoDetectLocation:a.autoDetectLocation})}));var y=c(21956);function z(){(0,f.useRouter)();let{user:a,isAuthenticated:b}=(0,q.A)(),{formatCurrency:c}=(()=>{let a=x(a=>a.convert),b=x(a=>a.formatAmount),c=x(a=>a.formatCurrency),d=x(a=>a.selectedCurrency);return{convert:a,formatAmount:b,formatCurrency:c,selectedCurrency:d,convertToSelected:(b,c)=>a(b,c,d),formatInSelected:(c,e)=>b(a(c,e,d).convertedAmount),convertFromMAD:b=>a(b,"MAD",d)}})(),{data:r,isLoading:s,error:t}=(0,y.Pi)(),{data:u,isLoading:v,error:w}=(0,y._)(),{data:z,isLoading:A,error:B}=(0,y.As)(),[C,D]=(0,e.useState)(!0);if(t||w||B)return(0,d.jsx)("div",{className:"min-h-screen bg-gray-50 p-6",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,d.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"text-red-800 font-semibold",children:"Erreur de chargement des donn\xe9es monitoring"}),(0,d.jsx)("p",{className:"text-red-600 text-sm mt-1",children:t?.message||w?.message||B?.message})]})})});let E=a=>{switch(a){case"healthy":case"good":return"text-green-600 bg-green-100";case"warning":case"degraded":return"text-yellow-600 bg-yellow-100";case"critical":case"down":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}},F=a=>{switch(a){case"healthy":case"good":return h.A;case"warning":case"degraded":return i.A;case"critical":case"down":return j.A;default:return k.A}};return s||v||A?(0,d.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-mbnb-coral"})}):(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,d.jsx)("div",{className:"px-4 sm:px-6 lg:px-8",children:(0,d.jsx)("div",{className:"py-6",children:(0,d.jsxs)("div",{className:"md:flex md:items-center md:justify-between",children:[(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate",children:"Monitoring Dashboard"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Surveillance en temps r\xe9el de la plateforme Mbnb"})]}),(0,d.jsxs)("div",{className:"mt-4 flex md:mt-0 md:ml-4 space-x-3",children:[(0,d.jsxs)("button",{onClick:()=>D(!C),className:`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium transition-colors ${C?"text-white bg-mbnb-coral border-mbnb-coral":"text-gray-700 bg-white hover:bg-gray-50"}`,children:[(0,d.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"Auto-refresh"]}),(0,d.jsxs)("div",{className:"text-sm text-gray-500 flex items-center",children:["Derni\xe8re mise \xe0 jour: ",(0,g.MZ)().toLocaleTimeString("fr-FR")]})]})]})})})}),(0,d.jsxs)("div",{className:"px-4 sm:px-6 lg:px-8 py-8",children:[(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"\xc9tat du syst\xe8me"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4",children:r?.map(a=>{let b=F(a.status);return(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsx)(l.A,{className:"h-6 w-6 text-gray-400"}),(0,d.jsx)(b,{className:`h-5 w-5 ${E(a.status).split(" ")[0]}`})]}),(0,d.jsxs)("div",{className:"space-y-1",children:[(0,d.jsx)("p",{className:"text-sm text-gray-500",children:a.name}),(0,d.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[a.value.toLocaleString()," ",a.unit]}),(0,d.jsxs)("div",{className:"flex items-center text-xs",children:[a.change>0?(0,d.jsx)(o,{className:"h-3 w-3 text-green-500 mr-1"}):(0,d.jsx)(p,{className:"h-3 w-3 text-red-500 mr-1"}),(0,d.jsx)("span",{className:a.change>0?"text-green-500":"text-red-500",children:Math.abs(a.change)}),(0,d.jsx)("span",{className:"text-gray-500 ml-1",children:"vs hier"})]})]})]},a.name)})})]}),(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Performance des APIs"}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,d.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Endpoint"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Temps de r\xe9ponse"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Requ\xeates (24h)"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Taux d'erreur"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Statut"})]})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:u?.map((a,b)=>{let c=F(a.status);return(0,d.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900",children:a.endpoint})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"text-sm text-gray-900",children:[a.avgResponseTime,"ms"]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("div",{className:"text-sm text-gray-900",children:a.requestCount.toLocaleString()})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"text-sm text-gray-900",children:[a.errorRate.toFixed(2),"%"]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${E(a.status)}`,children:[(0,d.jsx)(c,{className:"h-3 w-3 mr-1"}),a.status]})})]},b)})})]})})]}),(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"M\xe9triques Business"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:z?.map((a,b)=>(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsx)("h3",{className:"text-sm font-medium text-gray-500",children:a.label}),(0,d.jsx)("span",{className:"text-xs text-gray-400",children:a.period})]}),(0,d.jsxs)("div",{className:"flex items-baseline justify-between",children:[(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:((a,b)=>{switch(b){case"currency":return c(a,"MAD");case"percentage":return`${a}%`;default:return a.toLocaleString()}})(a.value,a.format)}),(0,d.jsxs)("div",{className:"flex items-center text-sm",children:[a.change>0?(0,d.jsx)(o,{className:"h-4 w-4 text-green-500 mr-1"}):(0,d.jsx)(p,{className:"h-4 w-4 text-red-500 mr-1"}),(0,d.jsxs)("span",{className:a.change>0?"text-green-500":"text-red-500",children:[Math.abs(a.change),"%"]})]})]})]},b))})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,d.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,d.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 flex items-center",children:[(0,d.jsx)(m.A,{className:"h-6 w-6 text-mbnb-coral mr-2"}),"S\xe9curit\xe9"]})}),(0,d.jsxs)("div",{className:"p-6 space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Certificat SSL"}),(0,d.jsx)(h.A,{className:"h-5 w-5 text-green-500"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"PCI-DSS Compliance"}),(0,d.jsx)(h.A,{className:"h-5 w-5 text-green-500"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"WAF Protection"}),(0,d.jsx)(h.A,{className:"h-5 w-5 text-green-500"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Rate Limiting"}),(0,d.jsx)(h.A,{className:"h-5 w-5 text-green-500"})]})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,d.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,d.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 flex items-center",children:[(0,d.jsx)(n.A,{className:"h-6 w-6 text-mbnb-teal mr-2"}),"Statistiques globales"]})}),(0,d.jsxs)("div",{className:"p-6 space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Propri\xe9t\xe9s actives"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"2,847"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Utilisateurs enregistr\xe9s"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"15,632"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Activit\xe9s TOP 100"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"100"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Devises support\xe9es"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"8"})]})]})]})]})]})]})}},84774:(a,b,c)=>{Promise.resolve().then(c.bind(c,73046))},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4635,2922,4380,3743,4367,1956],()=>b(b.s=66765));module.exports=c})();
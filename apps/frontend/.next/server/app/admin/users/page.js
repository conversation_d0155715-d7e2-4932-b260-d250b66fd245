(()=>{var a={};a.id=8733,a.ids=[8733],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3013:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4768).A)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4768:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(31768),e={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let f=(a,b)=>{let c=(0,d.forwardRef)(({color:c="currentColor",size:f=24,strokeWidth:g=2,absoluteStrokeWidth:h,className:i="",children:j,...k},l)=>(0,d.createElement)("svg",{ref:l,...e,width:f,height:f,stroke:c,strokeWidth:h?24*Number(g)/Number(f):g,className:["lucide",`lucide-${a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim()}`,i].join(" "),...k},[...b.map(([a,b])=>(0,d.createElement)(a,b)),...Array.isArray(j)?j:[j]]));return c.displayName=`${a}`,c}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27727:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>u});var d=c(78157),e=c(31768),f=c(32315),g=c(14380),h=c(86539),i=c(4768);let j=(0,i.A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]),k=(0,i.A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]),l=(0,i.A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);var m=c(42028),n=c(51383),o=c(53574),p=c(3013);let q=(0,i.A)("MoreVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]),r=(0,i.A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),s=(0,i.A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);var t=c(14196);function u(){let a=(0,f.jE)(),[b,c]=(0,e.useState)(1),[i,u]=(0,e.useState)([]),[v,w]=(0,e.useState)({sortBy:"joinedDate",sortOrder:"desc"}),[x,y]=(0,e.useState)(!1),{data:z}=(0,g.I)({queryKey:["admin-users",b,v],queryFn:async()=>{let a=new URLSearchParams({page:b.toString(),limit:"20",...Object.fromEntries(Object.entries(v).filter(([a,b])=>void 0!==b))}),c=await fetch(`/api/v1/admin/users?${a}`,{headers:{Authorization:`Bearer ${localStorage.getItem("mbnb-admin-token")}`}});if(!c.ok)throw Error("Failed to fetch users");return c.json()}}),A=(0,h.n)({mutationFn:async({userId:a,status:b})=>{let c=await fetch(`/api/v1/admin/users/${a}/status`,{method:"PATCH",headers:{Authorization:`Bearer ${localStorage.getItem("mbnb-admin-token")}`,"Content-Type":"application/json"},body:JSON.stringify({status:b})});if(!c.ok)throw Error("Failed to update user status");return c.json()},onSuccess:()=>{a.invalidateQueries({queryKey:["admin-users"]})}}),B=(0,h.n)({mutationFn:async({userId:a,verificationType:b})=>{let c=await fetch(`/api/v1/admin/users/${a}/verify`,{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("mbnb-admin-token")}`,"Content-Type":"application/json"},body:JSON.stringify({type:b})});if(!c.ok)throw Error("Failed to verify user");return c.json()},onSuccess:()=>{a.invalidateQueries({queryKey:["admin-users"]})}}),C=async()=>{let a=await fetch("/api/v1/admin/users/export",{headers:{Authorization:`Bearer ${localStorage.getItem("mbnb-admin-token")}`}});if(a.ok){let b=await a.blob(),c=window.URL.createObjectURL(b),d=document.createElement("a");d.href=c,d.download=`mbnb-users-${(0,t.MZ)().toISOString().split("T")[0]}.csv`,document.body.appendChild(d),d.click(),document.body.removeChild(d)}},D=async b=>{0!==i.length&&(await fetch("/api/v1/admin/users/bulk-action",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("mbnb-admin-token")}`,"Content-Type":"application/json"},body:JSON.stringify({userIds:i,action:b})})).ok&&(u([]),a.invalidateQueries({queryKey:["admin-users"]}))},E=a=>new Intl.NumberFormat("fr-MA",{style:"currency",currency:"MAD"}).format(a);return(0,d.jsxs)("div",{className:"min-h-screen bg-neutral-50",children:[(0,d.jsx)("div",{className:"bg-white shadow-sm border-b border-neutral-200",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-mbnb-navy",children:"Gestion des Utilisateurs Mbnb"}),(0,d.jsxs)("p",{className:"text-sm text-neutral-600 mt-1",children:[z?.total||0," utilisateurs totaux"]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsxs)("button",{onClick:C,className:"px-4 py-2 border border-neutral-300 rounded-lg hover:bg-neutral-50 flex items-center gap-2",children:[(0,d.jsx)(j,{className:"w-4 h-4"}),"Exporter"]}),(0,d.jsxs)("button",{onClick:()=>y(!x),className:"px-4 py-2 border border-neutral-300 rounded-lg hover:bg-neutral-50 flex items-center gap-2",children:[(0,d.jsx)(k,{className:"w-4 h-4"}),"Filtres"]})]})]})})}),x&&(0,d.jsx)("div",{className:"bg-white border-b border-neutral-200 px-4 py-4",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-6 gap-4",children:[(0,d.jsx)("div",{className:"md:col-span-2",children:(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(l,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-neutral-400"}),(0,d.jsx)("input",{type:"text",placeholder:"Rechercher par nom, email...",value:v.search||"",onChange:a=>w({...v,search:a.target.value}),className:"pl-10 pr-4 py-2 w-full border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral focus:border-transparent"})]})}),(0,d.jsxs)("select",{value:v.role||"",onChange:a=>w({...v,role:a.target.value}),className:"px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral",children:[(0,d.jsx)("option",{value:"",children:"Tous les r\xf4les"}),(0,d.jsx)("option",{value:"TRAVELER",children:"Voyageur"}),(0,d.jsx)("option",{value:"HOST",children:"H\xf4te"}),(0,d.jsx)("option",{value:"BOTH",children:"Voyageur & H\xf4te"}),(0,d.jsx)("option",{value:"ADMIN",children:"Administrateur"})]}),(0,d.jsxs)("select",{value:v.status||"",onChange:a=>w({...v,status:a.target.value}),className:"px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral",children:[(0,d.jsx)("option",{value:"",children:"Tous les statuts"}),(0,d.jsx)("option",{value:"active",children:"Actif"}),(0,d.jsx)("option",{value:"suspended",children:"Suspendu"}),(0,d.jsx)("option",{value:"pending",children:"En attente"}),(0,d.jsx)("option",{value:"banned",children:"Banni"})]}),(0,d.jsxs)("select",{value:v.loyaltyTier||"",onChange:a=>w({...v,loyaltyTier:a.target.value}),className:"px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral",children:[(0,d.jsx)("option",{value:"",children:"Tous les niveaux"}),(0,d.jsx)("option",{value:"EXPLORATEUR",children:"Explorateur"}),(0,d.jsx)("option",{value:"AVENTURIER",children:"Aventurier"}),(0,d.jsx)("option",{value:"NOMADE",children:"Nomade"}),(0,d.jsx)("option",{value:"AMBASSADEUR",children:"Ambassadeur"}),(0,d.jsx)("option",{value:"LEGENDE",children:"L\xe9gende"})]}),(0,d.jsxs)("select",{value:v.sortBy||"joinedDate",onChange:a=>w({...v,sortBy:a.target.value}),className:"px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral",children:[(0,d.jsx)("option",{value:"joinedDate",children:"Date d'inscription"}),(0,d.jsx)("option",{value:"lastLogin",children:"Derni\xe8re connexion"}),(0,d.jsx)("option",{value:"totalSpent",children:"Total d\xe9pens\xe9"}),(0,d.jsx)("option",{value:"totalBookings",children:"Total r\xe9servations"})]})]})}),i.length>0&&(0,d.jsx)("div",{className:"bg-mbnb-coral-light px-4 py-3",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto flex items-center justify-between",children:[(0,d.jsxs)("span",{className:"text-sm font-medium text-mbnb-navy",children:[i.length," utilisateur(s) s\xe9lectionn\xe9(s)"]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("button",{onClick:()=>D("activate"),className:"px-3 py-1 bg-mbnb-teal text-white rounded-md text-sm hover:bg-mbnb-teal-dark",children:"Activer"}),(0,d.jsx)("button",{onClick:()=>D("suspend"),className:"px-3 py-1 bg-warning-500 text-white rounded-md text-sm hover:bg-warning-600",children:"Suspendre"}),(0,d.jsx)("button",{onClick:()=>D("delete"),className:"px-3 py-1 bg-mbnb-coral-dark text-white rounded-md text-sm hover:bg-error-700",children:"Supprimer"})]})]})}),(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:(0,d.jsxs)("div",{className:"bg-white shadow-card rounded-lg overflow-hidden",children:[(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"min-w-full divide-y divide-neutral-200",children:[(0,d.jsx)("thead",{className:"bg-neutral-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider",children:(0,d.jsx)("input",{type:"checkbox",onChange:a=>{a.target.checked?u(z?.users.map(a=>a.id)||[]):u([])},checked:i.length===z?.users.length&&z?.users.length>0,className:"rounded border-neutral-300 text-mbnb-coral focus:ring-mbnb-coral"})}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider",children:"Utilisateur"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider",children:"R\xf4le"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider",children:"Statut"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider",children:"V\xe9rifications"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider",children:"Loyaut\xe9"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider",children:"Activit\xe9"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider",children:"Actions"})]})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-neutral-200",children:z?.users.map(a=>(0,d.jsxs)("tr",{className:"hover:bg-neutral-50",children:[(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("input",{type:"checkbox",checked:i.includes(a.id),onChange:b=>{b.target.checked?u([...i,a.id]):u(i.filter(b=>b!==a.id))},className:"rounded border-neutral-300 text-mbnb-coral focus:ring-mbnb-coral"})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"text-sm font-medium text-neutral-900",children:[a.firstName," ",a.lastName]}),(0,d.jsx)("div",{className:"text-sm text-neutral-500",children:a.email}),a.phone&&(0,d.jsx)("div",{className:"text-xs text-neutral-400",children:a.phone})]})}),(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,d.jsx)("span",{className:`px-2 py-1 text-xs rounded-full ${(a=>{switch(a){case"ADMIN":return"bg-mbnb-coral text-white";case"HOST":return"bg-mbnb-navy text-white";case"BOTH":return"bg-mbnb-teal text-white";default:return"bg-mbnb-sky text-mbnb-navy"}})(a.role)}`,children:a.role}),a.isSuperHost&&(0,d.jsxs)("div",{className:"flex items-center gap-1 mt-1",children:[(0,d.jsx)(m.A,{className:"w-3 h-3 text-mbnb-coral",fill:"currentColor"}),(0,d.jsx)("span",{className:"text-xs text-mbnb-coral",children:"SuperHost"})]})]}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("span",{className:`font-medium ${(a=>{switch(a){case"active":return"text-mbnb-teal-dark";case"suspended":return"text-warning-600";case"banned":return"text-mbnb-coral-dark";default:return"text-neutral-500"}})(a.status)}`,children:a.status})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[a.emailVerified?(0,d.jsx)(n.A,{className:"w-4 h-4 text-mbnb-teal-dark"}):(0,d.jsx)(n.A,{className:"w-4 h-4 text-neutral-300"}),a.phoneVerified?(0,d.jsx)(o.A,{className:"w-4 h-4 text-mbnb-teal-dark"}):(0,d.jsx)(o.A,{className:"w-4 h-4 text-neutral-300"}),a.identityVerified?(0,d.jsx)(p.A,{className:"w-4 h-4 text-mbnb-teal-dark"}):(0,d.jsx)(p.A,{className:"w-4 h-4 text-neutral-300"})]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:(a=>{switch(a){case"LEGENDE":return"text-mbnb-coral font-bold";case"AMBASSADEUR":return"text-mbnb-navy font-semibold";case"AVENTURIER":return"text-mbnb-steel";case"EXPLORATEUR":return"text-mbnb-teal";default:return"text-neutral-600"}})(a.loyaltyTier),children:a.loyaltyTier}),(0,d.jsxs)("div",{className:"text-xs text-neutral-500",children:[a.loyaltyPoints," pts • Phase ",a.commissionPhase]})]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-neutral-500",children:(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{children:[a.totalBookings," r\xe9servations"]}),(0,d.jsx)("div",{children:E(a.totalSpent)}),a.totalEarned&&(0,d.jsxs)("div",{className:"text-xs",children:["Gagn\xe9: ",E(a.totalEarned)]})]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,d.jsxs)("div",{className:"relative group",children:[(0,d.jsx)("button",{className:"text-neutral-400 hover:text-neutral-600",children:(0,d.jsx)(q,{className:"w-5 h-5"})}),(0,d.jsx)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 hidden group-hover:block",children:(0,d.jsxs)("div",{className:"py-1",children:[(0,d.jsx)("button",{onClick:()=>window.location.href=`/admin/users/${a.id}`,className:"block px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 w-full text-left",children:"Voir d\xe9tails"}),!a.emailVerified&&(0,d.jsx)("button",{onClick:()=>B.mutate({userId:a.id,verificationType:"email"}),className:"block px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 w-full text-left",children:"V\xe9rifier email"}),!a.identityVerified&&(0,d.jsx)("button",{onClick:()=>B.mutate({userId:a.id,verificationType:"identity"}),className:"block px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 w-full text-left",children:"V\xe9rifier identit\xe9"}),"active"===a.status?(0,d.jsx)("button",{onClick:()=>A.mutate({userId:a.id,status:"suspended"}),className:"block px-4 py-2 text-sm text-warning-600 hover:bg-warning-50 w-full text-left",children:"Suspendre"}):(0,d.jsx)("button",{onClick:()=>A.mutate({userId:a.id,status:"active"}),className:"block px-4 py-2 text-sm text-mbnb-teal-dark hover:bg-mbnb-teal-light/10 w-full text-left",children:"Activer"}),(0,d.jsx)("button",{onClick:()=>A.mutate({userId:a.id,status:"banned"}),className:"block px-4 py-2 text-sm text-mbnb-coral-dark hover:bg-mbnb-coral-light/10 w-full text-left",children:"Bannir"})]})})]})})]},a.id))})]})}),(0,d.jsxs)("div",{className:"bg-white px-4 py-3 flex items-center justify-between border-t border-neutral-200 sm:px-6",children:[(0,d.jsxs)("div",{className:"flex-1 flex justify-between sm:hidden",children:[(0,d.jsx)("button",{onClick:()=>c(Math.max(1,b-1)),disabled:1===b,className:"relative inline-flex items-center px-4 py-2 border border-neutral-300 text-sm font-medium rounded-md text-neutral-700 bg-white hover:bg-neutral-50 disabled:opacity-50",children:"Pr\xe9c\xe9dent"}),(0,d.jsx)("button",{onClick:()=>c(Math.min(z?.pages||1,b+1)),disabled:b===z?.pages,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-neutral-300 text-sm font-medium rounded-md text-neutral-700 bg-white hover:bg-neutral-50 disabled:opacity-50",children:"Suivant"})]}),(0,d.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,d.jsx)("div",{children:(0,d.jsxs)("p",{className:"text-sm text-neutral-700",children:["Affichage de"," ",(0,d.jsx)("span",{className:"font-medium",children:(b-1)*20+1})," \xe0"," ",(0,d.jsx)("span",{className:"font-medium",children:Math.min(20*b,z?.total||0)})," ","sur ",(0,d.jsx)("span",{className:"font-medium",children:z?.total||0})," r\xe9sultats"]})}),(0,d.jsx)("div",{children:(0,d.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px",children:[(0,d.jsx)("button",{onClick:()=>c(Math.max(1,b-1)),disabled:1===b,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-neutral-300 bg-white text-sm font-medium text-neutral-500 hover:bg-neutral-50 disabled:opacity-50",children:(0,d.jsx)(r,{className:"h-5 w-5"})}),[...Array(Math.min(5,z?.pages||1))].map((a,e)=>{let f=e+1;return(0,d.jsx)("button",{onClick:()=>c(f),className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${b===f?"z-10 bg-mbnb-coral border-mbnb-coral text-white":"bg-white border-neutral-300 text-neutral-500 hover:bg-neutral-50"}`,children:f},f)}),(0,d.jsx)("button",{onClick:()=>c(Math.min(z?.pages||1,b+1)),disabled:b===z?.pages,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-neutral-300 bg-white text-sm font-medium text-neutral-500 hover:bg-neutral-50 disabled:opacity-50",children:(0,d.jsx)(s,{className:"h-5 w-5"})})]})})]})]})]})})]})}},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30299:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(25459).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/admin/users/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/admin/users/page.tsx","default")},33873:a=>{"use strict";a.exports=require("path")},38868:(a,b,c)=>{Promise.resolve().then(c.bind(c,27727))},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},42028:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4768).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},51383:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4768).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},53574:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4768).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},86132:(a,b,c)=>{Promise.resolve().then(c.bind(c,30299))},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86539:(a,b,c)=>{"use strict";c.d(b,{n:()=>k});var d=c(31768),e=c(15916),f=c(36795),g=c(83690),h=c(8306),i=class extends g.Q{#a;#b=void 0;#c;#d;constructor(a,b){super(),this.#a=a,this.setOptions(b),this.bindMethods(),this.#e()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(a){let b=this.options;this.options=this.#a.defaultMutationOptions(a),(0,h.f8)(this.options,b)||this.#a.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#c,observer:this}),b?.mutationKey&&this.options.mutationKey&&(0,h.EN)(b.mutationKey)!==(0,h.EN)(this.options.mutationKey)?this.reset():this.#c?.state.status==="pending"&&this.#c.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#c?.removeObserver(this)}onMutationUpdate(a){this.#e(),this.#f(a)}getCurrentResult(){return this.#b}reset(){this.#c?.removeObserver(this),this.#c=void 0,this.#e(),this.#f()}mutate(a,b){return this.#d=b,this.#c?.removeObserver(this),this.#c=this.#a.getMutationCache().build(this.#a,this.options),this.#c.addObserver(this),this.#c.execute(a)}#e(){let a=this.#c?.state??(0,e.$)();this.#b={...a,isPending:"pending"===a.status,isSuccess:"success"===a.status,isError:"error"===a.status,isIdle:"idle"===a.status,mutate:this.mutate,reset:this.reset}}#f(a){f.jG.batch(()=>{if(this.#d&&this.hasListeners()){let b=this.#b.variables,c=this.#b.context;a?.type==="success"?(this.#d.onSuccess?.(a.data,b,c),this.#d.onSettled?.(a.data,null,b,c)):a?.type==="error"&&(this.#d.onError?.(a.error,b,c),this.#d.onSettled?.(void 0,a.error,b,c))}this.listeners.forEach(a=>{a(this.#b)})})}},j=c(32315);function k(a,b){let c=(0,j.jE)(b),[e]=d.useState(()=>new i(c,a));d.useEffect(()=>{e.setOptions(a)},[e,a]);let g=d.useSyncExternalStore(d.useCallback(a=>e.subscribe(f.jG.batchCalls(a)),[e]),()=>e.getCurrentResult(),()=>e.getCurrentResult()),k=d.useCallback((a,b)=>{e.mutate(a,b).catch(h.lQ)},[e]);if(g.error&&(0,h.GU)(e.options.throwOnError,[g.error]))throw g.error;return{...g,mutate:k,mutateAsync:g.mutate}}},92337:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.default,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(73653),e=c(97714),f=c(85250),g=c(37587),h=c(22369),i=c(1889),j=c(96232),k=c(22841),l=c(46537),m=c(46027),n=c(78559),o=c(75928),p=c(19374),q=c(65971),r=c(261),s=c(79898),t=c(32967),u=c(26713),v=c(40139),w=c(14248),x=c(59580),y=c(57749),z=c(53123),A=c(89745),B=c(86439),C=c(96133),D=c(18283),E=c(39818),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["admin",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,30299)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/admin/users/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,56035)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,74827)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/error.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,96133)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/global-error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,72993)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,15034,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,54693,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/admin/users/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/admin/users/page",pathname:"/admin/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",relativeProjectDir:""});async function K(a,b,d){var F;let L="/admin/users/page";"/index"===L&&(L="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await J.prepare(a,b,{srcPage:L,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(L),{isOnDemandRevalidate:ah}=O,ai=J.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(F=$.routes[ag]??$.dynamicRoutes[ag])?void 0:F.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===J.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&J.isDev&&(aB=aa),J.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...D,tree:G,pages:H,GlobalError:C.default,handler:K,routeModule:J,__next_app__:I};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:L,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=J.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:J,page:L,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),J.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!J.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===J.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await J.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await J.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!J.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&E.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:L,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4635,2922,4380,4367],()=>b(b.s=92337));module.exports=c})();
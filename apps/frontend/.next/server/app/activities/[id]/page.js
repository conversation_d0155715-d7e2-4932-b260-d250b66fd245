(()=>{var a={};a.id=4020,a.ids=[4020],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},2176:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>v});var d=c(78157),e=c(31768),f=c(71159),g=c(87817),h=c(21956),i=c(14196),j=c(91795),k=c(24971),l=c(20320),m=c(86272),n=c(44173),o=c(57843),p=c(9711),q=c(16563),r=c(74654),s=c(38059),t=c(91534),u=c(68322);function v(){let a=(0,f.useParams)(),b=(0,f.useRouter)(),c=a.id,{data:v,isLoading:w,error:x}=(0,h.oE)(c),[y,z]=(0,e.useState)(""),[A,B]=(0,e.useState)(1),[C,D]=(0,e.useState)(0),[E,F]=(0,e.useState)(!1),[G,H]=(0,e.useState)(!1),[I,J]=(0,e.useState)(!1);if(w)return(0,d.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-mbnb-coral mx-auto"}),(0,d.jsx)("p",{className:"mt-4 text-gray-600",children:"Chargement de l'activit\xe9..."})]})});if(x||!v)return(0,d.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("p",{className:"text-gray-600",children:"Activit\xe9 non trouv\xe9e"}),(0,d.jsx)("button",{onClick:()=>b.back(),className:"mt-4 px-4 py-2 bg-mbnb-coral text-white rounded-lg",children:"Retour"})]})});let K=v.pricing.adult*A,L=Math.round(.18*K);return(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)("div",{className:"bg-white border-b sticky top-0 z-40",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 py-4",children:(0,d.jsxs)("button",{onClick:()=>b.back(),className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[(0,d.jsx)(j.A,{className:"w-5 h-5 mr-2"}),"Retour aux activit\xe9s"]})})}),(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 py-8",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-8",children:[(0,d.jsxs)("div",{className:"lg:col-span-2",children:[(0,d.jsx)("div",{className:"mb-8",children:(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsxs)("div",{className:"relative aspect-[16/9] rounded-2xl overflow-hidden mb-4 group",children:[(0,d.jsx)(g.XF,{baseName:v.media.images[C].baseName,folder:v.media.images[C].folder,alt:v.name,className:"absolute inset-0 w-full h-full object-cover",priority:!0}),(0,d.jsxs)("div",{className:"absolute bottom-4 left-4 bg-black/60 text-white px-3 py-1 rounded-full text-sm",children:[C+1," / ",v.media.images.length]}),(0,d.jsx)("button",{onClick:()=>F(!E),className:"absolute top-4 right-4 p-3 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-all transform hover:scale-110",children:E?(0,d.jsx)(t.A,{className:"w-6 h-6 text-mbnb-coral"}):(0,d.jsx)(k.A,{className:"w-6 h-6 text-gray-700"})}),(0,d.jsx)("button",{className:"absolute top-4 right-20 p-3 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-all transform hover:scale-110",children:(0,d.jsx)(l.A,{className:"w-6 h-6 text-gray-700"})}),C>0&&(0,d.jsx)("button",{onClick:()=>D(a=>a-1),className:"absolute left-4 top-1/2 -translate-y-1/2 p-2 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-all",children:(0,d.jsx)(j.A,{className:"w-5 h-5"})}),C<v.media.images.length-1&&(0,d.jsx)("button",{onClick:()=>D(a=>a+1),className:"absolute right-4 top-1/2 -translate-y-1/2 p-2 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-all",children:(0,d.jsx)(j.A,{className:"w-5 h-5 rotate-180"})}),(0,d.jsxs)("button",{onClick:()=>J(!0),className:"absolute bottom-4 right-4 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-lg hover:bg-white transition-all flex items-center gap-2",children:[(0,d.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 10h16M4 14h16M4 18h16"})}),"Voir toutes les photos"]})]}),v.media.images.length>1&&(0,d.jsx)("div",{className:"grid grid-cols-4 md:grid-cols-6 gap-2",children:v.media.images.slice(0,6).map((a,b)=>(0,d.jsxs)("button",{onClick:()=>D(b),className:`relative aspect-[4/3] rounded-lg overflow-hidden transition-all ${C===b?"ring-2 ring-mbnb-coral scale-95":"hover:opacity-80"} ${5===b&&v.media.images.length>6?"relative":""}`,children:[(0,d.jsx)(g.XF,{baseName:a.baseName,folder:a.folder,alt:`${v.name} ${b+1}`,className:"absolute inset-0 w-full h-full object-cover"}),5===b&&v.media.images.length>6&&(0,d.jsxs)("div",{className:"absolute inset-0 bg-black/60 flex items-center justify-center text-white font-semibold",onClick:()=>J(!0),children:["+",v.media.images.length-6]})]},b))})]})}),(0,d.jsxs)("div",{className:"mb-6 lg:mb-8",children:[(0,d.jsx)("h1",{className:"text-2xl lg:text-3xl font-bold text-gray-900 mb-3 lg:mb-4",children:v.name}),(0,d.jsxs)("div",{className:"flex flex-wrap gap-2 lg:gap-4 text-sm lg:text-base text-gray-600 mb-4 lg:mb-6",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(m.A,{className:"w-5 h-5 mr-2 text-mbnb-coral"}),v.location.address||`${v.location.city}, ${v.location.region}`]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(n.A,{className:"w-5 h-5 mr-2 text-mbnb-coral"}),v.duration]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(o.A,{className:"w-5 h-5 mr-2 text-mbnb-coral"}),"Max ",v.pricing.group?.size||8," personnes"]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(u.A,{className:"w-5 h-5 mr-1 text-mbnb-coral"}),(0,d.jsx)("span",{className:"font-semibold",children:v.rating}),(0,d.jsxs)("span",{className:"ml-1",children:["(",v.reviews," avis)"]})]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-6",children:[(0,d.jsx)(p.A,{className:"w-5 h-5 text-gray-500"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:["Fran\xe7ais","Arabe","Anglais"].map(a=>(0,d.jsx)("span",{className:"px-3 py-1 bg-gray-100 rounded-full text-sm",children:a},a))})]}),(0,d.jsxs)("div",{className:"prose max-w-none mb-8",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold mb-3",children:"Description"}),(0,d.jsx)("p",{className:"text-gray-700 leading-relaxed",children:v.description})]}),(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Points forts"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:["Points forts disponibles","Exp\xe9rience authentique","Guide local"].map(a=>(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)(q.A,{className:"w-5 h-5 text-mbnb-teal-dark mr-2 mt-0.5 flex-shrink-0"}),(0,d.jsx)("span",{className:"text-gray-700",children:a})]},a))})]}),(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Ce qui est inclus"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[v.includes?.map(a=>(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)(q.A,{className:"w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0"}),(0,d.jsx)("span",{className:"text-gray-700",children:a})]},a)),v.excludes?.map(a=>(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)(r.A,{className:"w-5 h-5 text-red-500 mr-2 mt-0.5 flex-shrink-0"}),(0,d.jsx)("span",{className:"text-gray-700",children:a})]},a))]})]}),(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold mb-3",children:"Point de rencontre"}),(0,d.jsxs)("div",{className:"flex items-start p-4 bg-gray-50 rounded-xl",children:[(0,d.jsx)(m.A,{className:"w-5 h-5 text-mbnb-coral mr-3 mt-0.5"}),(0,d.jsx)("p",{className:"text-gray-700",children:"Point de rendez-vous \xe0 confirmer"})]})]}),(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold mb-3",children:"Politique d'annulation"}),(0,d.jsx)("p",{className:"text-gray-700",children:"Annulation gratuite jusqu'\xe0 24h avant"})]}),v.reviews&&v.reviews>0&&(0,d.jsxs)("div",{className:"border-t pt-8 mb-8",children:[(0,d.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-xl font-semibold",children:"Avis des voyageurs"}),(0,d.jsxs)("div",{className:"flex items-center mt-2",children:[(0,d.jsx)(u.A,{className:"w-5 h-5 text-mbnb-coral mr-1"}),(0,d.jsx)("span",{className:"font-semibold text-lg",children:v.rating}),(0,d.jsxs)("span",{className:"text-gray-500 ml-2",children:["(",v.reviews||0," avis v\xe9rifi\xe9s)"]})]})]})}),(0,d.jsxs)("div",{className:"text-center py-8 bg-gray-50 rounded-xl",children:[(0,d.jsxs)("p",{className:"text-gray-600",children:["Cette activit\xe9 a re\xe7u ",v.reviews," avis avec une note moyenne de ",v.rating,"/5"]}),(0,d.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Les avis d\xe9taill\xe9s seront disponibles prochainement"})]})]})]})]}),(0,d.jsx)("div",{className:"lg:col-span-1",children:(0,d.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-4 sm:p-6 sticky top-20 lg:top-24",children:[(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsxs)("div",{className:"flex items-baseline justify-between mb-2",children:[(0,d.jsxs)("span",{className:"text-3xl font-bold text-gray-900",children:[v.pricing.adult," MAD"]}),(0,d.jsx)("span",{className:"text-gray-500",children:"/ personne"})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(u.A,{className:"w-5 h-5 text-mbnb-coral mr-1"}),(0,d.jsx)("span",{className:"font-semibold",children:v.rating}),(0,d.jsxs)("span",{className:"text-gray-500 ml-1",children:["(",v.reviews," avis)"]})]})]}),(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsx)("label",{className:"block text-sm font-semibold text-gray-700 mb-2",children:"Date"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(s.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none"}),(0,d.jsx)("input",{type:"date",value:y,onChange:a=>z(a.target.value),min:(0,i.MZ)().toISOString().split("T")[0],className:"w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-mbnb-coral focus:border-transparent transition-all"})]})]}),(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsx)("label",{className:"block text-sm font-semibold text-gray-700 mb-2",children:"Nombre de personnes"}),(0,d.jsxs)("div",{className:"flex items-center justify-between border border-gray-300 rounded-xl p-3",children:[(0,d.jsx)("button",{onClick:()=>B(Math.max(1,A-1)),disabled:A<=1,className:"w-8 h-8 rounded-full border border-gray-400 flex items-center justify-center hover:border-mbnb-coral hover:text-mbnb-coral disabled:border-gray-200 disabled:text-gray-300 transition-colors",children:"−"}),(0,d.jsx)("span",{className:"font-semibold text-lg",children:A}),(0,d.jsx)("button",{onClick:()=>B(Math.min(v.pricing.group?.size||8,A+1)),disabled:A>=(v.pricing.group?.size||8),className:"w-8 h-8 rounded-full border border-gray-400 flex items-center justify-center hover:border-mbnb-coral hover:text-mbnb-coral disabled:border-gray-200 disabled:text-gray-300 transition-colors",children:"+"})]})]}),(0,d.jsxs)("div",{className:"border-t pt-4 mb-6",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,d.jsxs)("span",{children:[v.pricing.adult," MAD \xd7 ",A," personne",A>1?"s":""]}),(0,d.jsxs)("span",{children:[K," MAD"]})]}),(0,d.jsxs)("div",{className:"flex justify-between text-sm text-gray-500",children:[(0,d.jsx)("span",{children:"Commission Mbnb (18%)"}),(0,d.jsxs)("span",{children:[L," MAD"]})]})]}),(0,d.jsx)("div",{className:"border-t pt-4 mt-4",children:(0,d.jsxs)("div",{className:"flex justify-between text-lg font-bold",children:[(0,d.jsx)("span",{children:"Total"}),(0,d.jsxs)("span",{className:"text-mbnb-coral",children:[K," MAD"]})]})})]}),(0,d.jsx)("button",{onClick:()=>{H(!0)},disabled:!y,className:"w-full bg-mbnb-coral hover:bg-mbnb-coral-dark disabled:bg-gray-300 text-white py-4 rounded-xl font-semibold transition-colors",children:"R\xe9server cette activit\xe9"}),(0,d.jsx)("p",{className:"text-center text-sm text-gray-500 mt-3",children:"Vous ne serez d\xe9bit\xe9 qu'apr\xe8s confirmation"})]})})]})}),I&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black z-50 overflow-y-auto",children:(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("button",{onClick:()=>J(!1),className:"fixed top-4 right-4 z-50 p-3 bg-white/10 backdrop-blur-sm rounded-full text-white hover:bg-white/20 transition-all",children:(0,d.jsx)(r.A,{className:"w-6 h-6"})}),(0,d.jsxs)("div",{className:"container mx-auto px-4 py-20",children:[(0,d.jsxs)("h2",{className:"text-white text-2xl font-bold mb-6",children:["Galerie photos (",v.media.images.length," photos)"]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:v.media.images.map((a,b)=>(0,d.jsx)("div",{className:"relative aspect-[16/9] rounded-xl overflow-hidden",children:(0,d.jsx)(g.XF,{baseName:a.baseName,folder:a.folder,alt:`${v.name} ${b+1}`,className:"absolute inset-0 w-full h-full object-cover"})},b))})]})]})}),G&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-2xl max-w-md w-full p-4 sm:p-6 animate-fadeIn",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Confirmer la r\xe9servation"}),(0,d.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,d.jsxs)("div",{className:"p-3 bg-gray-50 rounded-xl",children:[(0,d.jsx)("p",{className:"font-semibold text-gray-900",children:v.name}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:v.location.address||`${v.location.city}, ${v.location.region}`})]}),(0,d.jsxs)("div",{className:"p-3 bg-gray-50 rounded-xl",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Date"}),(0,d.jsx)("span",{className:"font-medium",children:y})]}),(0,d.jsxs)("div",{className:"flex justify-between mt-2",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Participants"}),(0,d.jsxs)("span",{className:"font-medium",children:[A," personne",A>1?"s":""]})]})]}),(0,d.jsx)("div",{className:"p-3 bg-mbnb-coral/10 rounded-xl",children:(0,d.jsxs)("div",{className:"flex justify-between text-lg font-bold text-mbnb-coral",children:[(0,d.jsx)("span",{children:"Total \xe0 payer"}),(0,d.jsxs)("span",{children:[K," MAD"]})]})})]}),(0,d.jsxs)("div",{className:"flex gap-3",children:[(0,d.jsx)("button",{onClick:()=>H(!1),className:"flex-1 py-3 border border-gray-300 rounded-xl font-semibold hover:bg-gray-50 transition-colors",children:"Annuler"}),(0,d.jsx)("button",{onClick:()=>{b.push(`/booking/activity/${c}?date=${y}&guests=${A}`)},className:"flex-1 py-3 bg-mbnb-coral hover:bg-mbnb-coral-dark text-white rounded-xl font-semibold transition-colors",children:"Confirmer"})]})]})})]})}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9711:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m10.5 21 5.25-11.25L21 21m-9-3h7.5M3 5.621a48.474 48.474 0 0 1 6-.371m0 0c1.12 0 2.233.038 3.334.114M9 5.25V3m3.334 2.364C11.176 10.658 7.69 15.08 3 17.502m9.334-12.138c.896.061 1.785.147 2.666.257m-4.589 8.495a18.023 18.023 0 0 1-3.827-5.802"}))})},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15417:(a,b,c)=>{"use strict";c.d(b,{MbnbImageGallery:()=>i,MbnbResponsiveImage:()=>g,useMbnbImagePreload:()=>h});var d=c(78157),e=c(31768),f=c.n(e);function g({baseName:a,folder:b,alt:c,className:e="",priority:f=!1,onClick:g}){let h=`/Storytelling/optimized/${b}/${a}`;return(0,d.jsxs)("picture",{className:e,onClick:g,children:[(0,d.jsx)("source",{srcSet:`${h}_mobile_375w.avif`,media:"(max-width: 375px)",type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_mobile_375w.webp`,media:"(max-width: 375px)",type:"image/webp"}),(0,d.jsx)("source",{srcSet:`${h}_mobile_375w.jpg`,media:"(max-width: 375px)",type:"image/jpeg"}),(0,d.jsx)("source",{srcSet:`${h}_mobile_414w.avif`,media:"(max-width: 414px)",type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_mobile_414w.webp`,media:"(max-width: 414px)",type:"image/webp"}),(0,d.jsx)("source",{srcSet:`${h}_mobile_414w.jpg`,media:"(max-width: 414px)",type:"image/jpeg"}),(0,d.jsx)("source",{srcSet:`${h}_mobile_480w.avif`,media:"(max-width: 480px)",type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_mobile_480w.webp`,media:"(max-width: 480px)",type:"image/webp"}),(0,d.jsx)("source",{srcSet:`${h}_mobile_480w.jpg`,media:"(max-width: 480px)",type:"image/jpeg"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_768w.avif`,media:"(max-width: 768px)",type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_768w.webp`,media:"(max-width: 768px)",type:"image/webp"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_768w.jpg`,media:"(max-width: 768px)",type:"image/jpeg"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_834w.avif`,media:"(max-width: 834px)",type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_834w.webp`,media:"(max-width: 834px)",type:"image/webp"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_834w.jpg`,media:"(max-width: 834px)",type:"image/jpeg"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_1024w.avif`,media:"(max-width: 1024px)",type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_1024w.webp`,media:"(max-width: 1024px)",type:"image/webp"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_1024w.jpg`,media:"(max-width: 1024px)",type:"image/jpeg"}),(0,d.jsx)("source",{srcSet:`${h}_desktop_1200w.avif`,media:"(max-width: 1200px)",type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_desktop_1200w.webp`,media:"(max-width: 1200px)",type:"image/webp"}),(0,d.jsx)("source",{srcSet:`${h}_desktop_1200w.jpg`,media:"(max-width: 1200px)",type:"image/jpeg"}),(0,d.jsx)("source",{srcSet:`${h}_desktop_1440w.avif`,media:"(max-width: 1440px)",type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_desktop_1440w.webp`,media:"(max-width: 1440px)",type:"image/webp"}),(0,d.jsx)("source",{srcSet:`${h}_desktop_1440w.jpg`,media:"(max-width: 1440px)",type:"image/jpeg"}),(0,d.jsx)("source",{srcSet:`${h}_desktop_1920w.avif`,type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_desktop_1920w.webp`,type:"image/webp"}),(0,d.jsx)("img",{src:`${h}_desktop_1920w.jpg`,alt:c,loading:f?"eager":"lazy",className:`${e} w-full`,decoding:"async"})]})}function h(a,b){}function i({images:a,className:b=""}){let[c,e]=f().useState(null);return(0,d.jsxs)("div",{className:`grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 ${b}`,children:[a.map((a,b)=>(0,d.jsxs)("div",{className:"relative group cursor-pointer overflow-hidden rounded-lg",onClick:()=>e(b),children:[(0,d.jsx)(g,{baseName:a.baseName,folder:a.folder,alt:a.alt,className:"transition-transform duration-300 group-hover:scale-110",priority:b<4}),(0,d.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity duration-300"})]},`${a.folder}-${a.baseName}`)),null!==c&&(0,d.jsx)("div",{className:"fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center p-4",onClick:()=>e(null),children:(0,d.jsx)(g,{baseName:a[c].baseName,folder:a[c].folder,alt:a[c].alt,className:"max-w-full max-h-full object-contain",priority:!0})})]})}},15731:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.default,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(73653),e=c(97714),f=c(85250),g=c(37587),h=c(22369),i=c(1889),j=c(96232),k=c(22841),l=c(46537),m=c(46027),n=c(78559),o=c(75928),p=c(19374),q=c(65971),r=c(261),s=c(79898),t=c(32967),u=c(26713),v=c(40139),w=c(14248),x=c(59580),y=c(57749),z=c(53123),A=c(89745),B=c(86439),C=c(96133),D=c(18283),E=c(39818),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["activities",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,87966)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/activities/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,56035)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,74827)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/error.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,96133)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/global-error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,72993)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,15034,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,54693,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/activities/[id]/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/activities/[id]/page",pathname:"/activities/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",relativeProjectDir:""});async function K(a,b,d){var F;let L="/activities/[id]/page";"/index"===L&&(L="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await J.prepare(a,b,{srcPage:L,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(L),{isOnDemandRevalidate:ah}=O,ai=J.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(F=$.routes[ag]??$.dynamicRoutes[ag])?void 0:F.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===J.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&J.isDev&&(aB=aa),J.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...D,tree:G,pages:H,GlobalError:C.default,handler:K,routeModule:J,__next_app__:I};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:L,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=J.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:J,page:L,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),J.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!J.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===J.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await J.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await J.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!J.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&E.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:L,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},16563:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20320:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M7.217 10.907a2.25 2.25 0 1 0 0 2.186m0-2.186c.18.324.283.696.283 1.093s-.103.77-.283 1.093m0-2.186 9.566-5.314m-9.566 7.5 9.566 5.314m0 0a2.25 2.25 0 1 0 3.935 2.186 2.25 2.25 0 0 0-3.935-2.186Zm0-12.814a2.25 2.25 0 1 0 3.933-2.185 2.25 2.25 0 0 0-3.933 2.185Z"}))})},26503:(a,b,c)=>{Promise.resolve().then(c.bind(c,87966))},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},38059:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))})},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},44173:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},56767:(a,b,c)=>{Promise.resolve().then(c.bind(c,2176))},57843:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))})},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68322:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.006 5.404.434c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.434 2.082-5.005Z",clipRule:"evenodd"}))})},71159:(a,b,c)=>{"use strict";var d=c(30291);c.o(d,"useParams")&&c.d(b,{useParams:function(){return d.useParams}}),c.o(d,"usePathname")&&c.d(b,{usePathname:function(){return d.usePathname}}),c.o(d,"useRouter")&&c.d(b,{useRouter:function(){return d.useRouter}}),c.o(d,"useSearchParams")&&c.d(b,{useSearchParams:function(){return d.useSearchParams}})},86272:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}),d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"}))})},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},87817:(a,b,c)=>{"use strict";c.d(b,{XF:()=>d.MbnbResponsiveImage});var d=c(15417)},87966:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(25459).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/activities/[id]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/activities/[id]/page.tsx","default")},91534:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{d:"m11.645 20.91-.007-.003-.022-.012a15.247 15.247 0 0 1-.383-.218 25.18 25.18 0 0 1-4.244-3.17C4.688 15.36 2.25 12.174 2.25 8.25 2.25 5.322 4.714 3 7.688 3A5.5 5.5 0 0 1 12 5.052 5.5 5.5 0 0 1 16.313 3c2.973 0 5.437 2.322 5.437 5.25 0 3.925-2.438 7.111-4.739 9.256a25.175 25.175 0 0 1-4.244 3.17 15.247 15.247 0 0 1-.383.219l-.022.012-.007.004-.003.001a.752.752 0 0 1-.704 0l-.003-.001Z"}))})},91795:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))})}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4635,2922,4380,4367,1956],()=>b(b.s=15731));module.exports=c})();
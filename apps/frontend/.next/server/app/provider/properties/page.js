(()=>{var a={};a.id=7126,a.ids=[7126],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21499:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.default,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(73653),e=c(97714),f=c(85250),g=c(37587),h=c(22369),i=c(1889),j=c(96232),k=c(22841),l=c(46537),m=c(46027),n=c(78559),o=c(75928),p=c(19374),q=c(65971),r=c(261),s=c(79898),t=c(32967),u=c(26713),v=c(40139),w=c(14248),x=c(59580),y=c(57749),z=c(53123),A=c(89745),B=c(86439),C=c(96133),D=c(18283),E=c(39818),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["provider",{children:["properties",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,84916)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/provider/properties/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,69669)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/provider/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,56035)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,74827)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/error.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,96133)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/global-error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,72993)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,15034,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,54693,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/provider/properties/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/provider/properties/page",pathname:"/provider/properties",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",relativeProjectDir:""});async function K(a,b,d){var F;let L="/provider/properties/page";"/index"===L&&(L="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await J.prepare(a,b,{srcPage:L,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(L),{isOnDemandRevalidate:ah}=O,ai=J.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(F=$.routes[ag]??$.dynamicRoutes[ag])?void 0:F.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===J.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&J.isDev&&(aB=aa),J.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...D,tree:G,pages:H,GlobalError:C.default,handler:K,routeModule:J,__next_app__:I};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:L,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=J.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:J,page:L,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),J.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!J.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===J.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await J.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await J.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!J.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&E.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:L,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32795:(a,b,c)=>{Promise.resolve().then(c.bind(c,84916))},33873:a=>{"use strict";a.exports=require("path")},39901:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))})},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},43411:(a,b,c)=>{Promise.resolve().then(c.bind(c,81499))},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66124:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))})},71896:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z"}))})},81368:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))})},81499:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>x});var d=c(78157),e=c(31768),f=c(94496),g=c.n(f),h=c(91712),i=c(39901),j=c(66124),k=c(82314),l=c(71896),m=c(51301),n=c(86272),o=c(20249),p=c(95046),q=c(38500),r=c(16888),s=c(81368),t=c(98340),u=c(86654);let v=e.forwardRef(function({title:a,titleId:b,...c},d){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:d,"aria-labelledby":b},c),a?e.createElement("title",{id:b},a):null,e.createElement("path",{fillRule:"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z",clipRule:"evenodd"}))});var w=c(21956);function x(){let{data:a,isLoading:b,error:c}=(0,w.Zm)(),[f,x]=(0,e.useState)(""),[y,z]=(0,e.useState)("all"),[A,B]=(0,e.useState)([]);if(c)return(0,d.jsx)("div",{className:"min-h-screen bg-gray-50 p-6",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,d.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"text-red-800 font-semibold",children:"Erreur de chargement des propri\xe9t\xe9s"}),(0,d.jsx)("p",{className:"text-red-600 text-sm mt-1",children:c.message})]})})});let C=(a||[]).filter(a=>{let b=a.title.toLowerCase().includes(f.toLowerCase())||a.location.city.toLowerCase().includes(f.toLowerCase()),c="all"===y||a.status===y;return b&&c}),D=async a=>{console.log(`Bulk action: ${a} for properties:`,A)};return b?(0,d.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-mbnb-coral"})}):(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,d.jsx)("div",{className:"px-4 sm:px-6 lg:px-8",children:(0,d.jsx)("div",{className:"py-6",children:(0,d.jsxs)("div",{className:"md:flex md:items-center md:justify-between",children:[(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate",children:"Mes propri\xe9t\xe9s"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"G\xe9rez vos propri\xe9t\xe9s et optimisez vos revenus Mbnb"})]}),(0,d.jsx)("div",{className:"mt-4 flex md:mt-0 md:ml-4",children:(0,d.jsxs)(g(),{href:"/provider/properties/new",className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-mbnb-coral hover:bg-mbnb-coral-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-mbnb-coral",children:[(0,d.jsx)(i.A,{className:"h-4 w-4 mr-2"}),"Ajouter une propri\xe9t\xe9"]})})]})})})}),(0,d.jsxs)("div",{className:"px-4 sm:px-6 lg:px-8 py-8",children:[(0,d.jsxs)("div",{className:"mb-6 flex flex-col sm:flex-row gap-4",children:[(0,d.jsxs)("div",{className:"flex-1 relative",children:[(0,d.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,d.jsx)(j.A,{className:"h-5 w-5 text-gray-400"})}),(0,d.jsx)("input",{type:"text",placeholder:"Rechercher par nom ou ville...",value:f,onChange:a=>x(a.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-mbnb-coral focus:border-mbnb-coral"})]}),(0,d.jsxs)("select",{value:y,onChange:a=>z(a.target.value),className:"block w-full sm:w-48 px-3 py-2 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-1 focus:ring-mbnb-coral focus:border-mbnb-coral",children:[(0,d.jsx)("option",{value:"all",children:"Tous les statuts"}),(0,d.jsx)("option",{value:"active",children:"Actives"}),(0,d.jsx)("option",{value:"inactive",children:"Inactives"}),(0,d.jsx)("option",{value:"under_review",children:"En r\xe9vision"})]})]}),A.length>0&&(0,d.jsx)("div",{className:"mb-4 p-4 bg-blue-50 border border-blue-200 rounded-md",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("p",{className:"text-sm text-blue-700",children:[A.length," propri\xe9t\xe9(s) s\xe9lectionn\xe9e(s)"]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)("button",{onClick:()=>D("activate"),className:"text-sm text-blue-600 hover:text-blue-800",children:"Activer"}),(0,d.jsx)("button",{onClick:()=>D("deactivate"),className:"text-sm text-blue-600 hover:text-blue-800",children:"D\xe9sactiver"}),(0,d.jsx)("button",{onClick:()=>D("archive"),className:"text-sm text-red-600 hover:text-red-800",children:"Archiver"})]})]})}),0===C.length?(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)(k.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,d.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"Aucune propri\xe9t\xe9"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:a?.length===0?"Commencez par ajouter votre premi\xe8re propri\xe9t\xe9.":"Aucune propri\xe9t\xe9 ne correspond \xe0 vos crit\xe8res de recherche."}),a?.length===0&&(0,d.jsx)("div",{className:"mt-6",children:(0,d.jsxs)(g(),{href:"/provider/properties/new",className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-mbnb-coral hover:bg-mbnb-coral-dark",children:[(0,d.jsx)(i.A,{className:"h-4 w-4 mr-2"}),"Ajouter une propri\xe9t\xe9"]})})]}):(0,d.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3",children:C.map(a=>{let b;return(0,d.jsxs)("div",{className:"relative bg-white rounded-lg shadow hover:shadow-md transition-shadow duration-200",children:[(0,d.jsx)("div",{className:"absolute top-3 left-3 z-10",children:(0,d.jsx)("input",{type:"checkbox",checked:A.includes(a.mbnbId),onChange:()=>{var b;return b=a.mbnbId,void B(a=>a.includes(b)?a.filter(a=>a!==b):[...a,b])},className:"h-4 w-4 text-mbnb-coral focus:ring-mbnb-coral border-gray-300 rounded"})}),(0,d.jsx)("div",{className:"absolute top-3 right-3 z-10",children:(0,d.jsx)("div",{className:"relative group",children:(0,d.jsx)("button",{className:"p-1 bg-white bg-opacity-80 rounded-full shadow",children:(0,d.jsx)(l.A,{className:"h-4 w-4 text-gray-600"})})})}),(0,d.jsx)(g(),{href:`/provider/properties/${a.mbnbId}`,children:(0,d.jsx)("div",{className:"aspect-w-16 aspect-h-10",children:(0,d.jsx)(h.default,{src:("string"==typeof a.media?.images?.[0]?a.media.images[0]:a.media?.images?.[0]?.url)||"/placeholder-property.jpg",alt:a.title,fill:!0,className:"object-cover rounded-t-lg"})})}),(0,d.jsxs)("div",{className:"p-4",children:[(0,d.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[((a,b)=>{if(!b)return(0,d.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:[(0,d.jsx)(t.A,{className:"h-3 w-3 mr-1"}),"Brouillon"]});let c={active:{label:"Active",icon:u.A,className:"bg-green-100 text-green-800"},inactive:{label:"Inactive",icon:v,className:"bg-red-100 text-red-800"},under_review:{label:"En r\xe9vision",icon:t.A,className:"bg-yellow-100 text-yellow-800"}},e=c[a]||c.inactive,f=e.icon;return(0,d.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${e.className}`,children:[(0,d.jsx)(f,{className:"h-3 w-3 mr-1"}),e.label]})})(a.status||"inactive",!0),(0,d.jsxs)("div",{className:"flex items-center text-sm text-gray-500",children:[(0,d.jsx)(m.A,{className:"h-4 w-4 text-yellow-400 mr-1"}),a.averageRating||"Nouveau"]})]}),(0,d.jsx)(g(),{href:`/provider/properties/${a.mbnbId}`,children:(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 hover:text-mbnb-coral line-clamp-2 mb-2",children:a.title})}),(0,d.jsxs)("div",{className:"flex items-center text-sm text-gray-500 mb-3",children:[(0,d.jsx)(n.A,{className:"h-4 w-4 mr-1"}),a.location.city,", ",a.location.region]}),(0,d.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-xl font-bold text-gray-900",children:(b=a.price||0,new Intl.NumberFormat("fr-MA",{style:"currency",currency:"MAD"}).format(b))}),(0,d.jsx)("span",{className:"text-sm text-gray-500 ml-1",children:"/nuit"})]}),(0,d.jsxs)("div",{className:"flex items-center text-sm text-gray-500",children:[(0,d.jsx)(o.A,{className:"h-4 w-4 mr-1"}),a.capacity||a.specifications?.guests||1," voyageurs"]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-3 gap-2 text-center border-t pt-3",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(p.A,{className:"h-4 w-4 mx-auto text-gray-400 mb-1"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Vues"}),(0,d.jsx)("p",{className:"text-sm font-medium",children:Math.floor(500*Math.random())+50})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(q.A,{className:"h-4 w-4 mx-auto text-gray-400 mb-1"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"R\xe9servations"}),(0,d.jsx)("p",{className:"text-sm font-medium",children:Math.floor(20*Math.random())+5})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(r.A,{className:"h-4 w-4 mx-auto text-gray-400 mb-1"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Taux"}),(0,d.jsxs)("p",{className:"text-sm font-medium",children:[Math.floor(40*Math.random())+60,"%"]})]})]}),(0,d.jsxs)("div",{className:"flex space-x-2 mt-4",children:[(0,d.jsxs)(g(),{href:`/provider/properties/${a.mbnbId}/edit`,className:"flex-1 inline-flex items-center justify-center px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[(0,d.jsx)(s.A,{className:"h-4 w-4 mr-1"}),"Modifier"]}),(0,d.jsxs)(g(),{href:`/properties/${a.mbnbId}`,className:"flex-1 inline-flex items-center justify-center px-3 py-1 border border-transparent rounded-md text-sm font-medium text-white bg-mbnb-coral hover:bg-mbnb-coral-dark",children:[(0,d.jsx)(p.A,{className:"h-4 w-4 mr-1"}),"Voir"]})]})]})]},a.mbnbId)})})]})]})}},84916:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(25459).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/provider/properties/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/provider/properties/page.tsx","default")},86272:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}),d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"}))})},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86654:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{fillRule:"evenodd",d:"M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z",clipRule:"evenodd"}))})},95046:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},98340:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{fillRule:"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM12.75 6a.75.75 0 0 0-1.5 0v6c0 .414.336.75.75.75h4.5a.75.75 0 0 0 0-1.5h-3.75V6Z",clipRule:"evenodd"}))})}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4635,2922,4380,3743,1712,4367,1956,3260],()=>b(b.s=21499));module.exports=c})();
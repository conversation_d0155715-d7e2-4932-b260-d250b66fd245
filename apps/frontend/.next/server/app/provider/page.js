"use strict";(()=>{var a={};a.id=9322,a.ids=[9322],a.modules={261:a=>{a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{a.exports=require("util")},29294:a=>{a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32047:(a,b,c)=>{c.r(b),c.d(b,{default:()=>t,metadata:()=>p});var d=c(5939),e=c(3498),f=c.n(e),g=c(43828);let h=(0,g.A)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]]);var i=c(95249),j=c(56988),k=c(70679);let l=(0,g.A)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);var m=c(90249),n=c(89869);let o=(0,g.A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),p={title:"Provider Partner | Mbnb",description:"Devenez partenaire Provider Mbnb. G\xe9rez plusieurs propri\xe9t\xe9s et acc\xe9l\xe9rez votre croissance avec nos outils professionnels."},q=[{icon:h,title:"Gestion multi-propri\xe9t\xe9s",description:"G\xe9rez facilement des dizaines de propri\xe9t\xe9s depuis un tableau de bord unifi\xe9"},{icon:i.A,title:"Analytics avanc\xe9s",description:"Acc\xe9dez \xe0 des donn\xe9es d\xe9taill\xe9es et pr\xe9dictions IA pour optimiser vos revenus"},{icon:j.A,title:"Account Manager d\xe9di\xe9",description:"B\xe9n\xe9ficiez d'un support prioritaire avec un gestionnaire de compte personnel"},{icon:k.A,title:"Assurance premium",description:"Couverture \xe9tendue jusqu'\xe0 5 millions MAD pour toutes vos propri\xe9t\xe9s"}],r=[{value:"500+",label:"Providers actifs"},{value:"15,000+",label:"Propri\xe9t\xe9s g\xe9r\xe9es"},{value:"35%",label:"Augmentation revenus"},{value:"4.9/5",label:"Satisfaction providers"}],s=[{name:"Karim Benjelloun",company:"Atlas Properties",quote:"Mbnb nous a permis de doubler notre portfolio en 18 mois. Les outils d'analyse sont exceptionnels.",properties:67,location:"Marrakech"},{name:"Sarah El Fassi",company:"Riad Collection",quote:"Le support Provider est incomparable. Notre taux d'occupation a augment\xe9 de 40%.",properties:23,location:"F\xe8s"}];function t(){return(0,d.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,d.jsx)("section",{className:"bg-gradient-to-br from-mbnb-navy to-mbnb-navy-light text-white py-20",children:(0,d.jsx)("div",{className:"container mx-auto px-4",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,d.jsx)("span",{className:"inline-block px-4 py-2 bg-white/20 rounded-full text-sm font-medium mb-6",children:"Programme Provider Partner"}),(0,d.jsx)("h1",{className:"text-5xl font-bold mb-6",children:"Faites cro\xeetre votre business avec Mbnb"}),(0,d.jsx)("p",{className:"text-xl text-white/90 mb-8",children:"Rejoignez le r\xe9seau des plus grands gestionnaires de propri\xe9t\xe9s au Maroc. Outils professionnels, support d\xe9di\xe9, commissions r\xe9duites."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)(f(),{href:"/provider/signup",className:"px-8 py-4 bg-white text-mbnb-navy rounded-lg hover:bg-gray-100 transition-colors font-medium",children:"Devenir Provider"}),(0,d.jsx)(f(),{href:"/provider/dashboard",className:"px-8 py-4 bg-white/20 text-white rounded-lg hover:bg-white/30 transition-colors font-medium",children:"Acc\xe9der au Dashboard"})]})]})})}),(0,d.jsx)("section",{className:"py-16 border-b",children:(0,d.jsx)("div",{className:"container mx-auto px-4",children:(0,d.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-8 text-center",children:r.map((a,b)=>(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-3xl md:text-4xl font-bold text-mbnb-navy mb-2",children:a.value}),(0,d.jsx)("p",{className:"text-gray-600",children:a.label})]},b))})})}),(0,d.jsx)("section",{className:"py-20",children:(0,d.jsxs)("div",{className:"container mx-auto px-4",children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Avantages exclusifs Provider"}),(0,d.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Des outils et services con\xe7us pour les professionnels de la location"})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:q.map((a,b)=>{let c=a.icon;return(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-mbnb-coral/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(c,{className:"w-8 h-8 text-mbnb-coral"})}),(0,d.jsx)("h3",{className:"font-bold text-gray-900 mb-2",children:a.title}),(0,d.jsx)("p",{className:"text-gray-600",children:a.description})]},b)})})]})}),(0,d.jsx)("section",{className:"py-20 bg-gray-50",children:(0,d.jsx)("div",{className:"container mx-auto px-4",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Dashboard professionnel ultra-performant"}),(0,d.jsxs)("ul",{className:"space-y-4",children:[(0,d.jsxs)("li",{className:"flex items-start",children:[(0,d.jsx)(l,{className:"w-6 h-6 text-mbnb-coral mr-3 flex-shrink-0 mt-0.5"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-bold text-gray-900",children:"Analytics en temps r\xe9el"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Suivez vos performances avec des m\xe9triques d\xe9taill\xe9es"})]})]}),(0,d.jsxs)("li",{className:"flex items-start",children:[(0,d.jsx)(m.A,{className:"w-6 h-6 text-mbnb-coral mr-3 flex-shrink-0 mt-0.5"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-bold text-gray-900",children:"Channel Manager int\xe9gr\xe9"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Synchronisez avec Booking, Airbnb et 20+ plateformes"})]})]}),(0,d.jsxs)("li",{className:"flex items-start",children:[(0,d.jsx)(n.A,{className:"w-6 h-6 text-mbnb-coral mr-3 flex-shrink-0 mt-0.5"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-bold text-gray-900",children:"Certification Provider"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Badge de confiance et visibilit\xe9 accrue"})]})]})]}),(0,d.jsxs)(f(),{href:"/provider/features",className:"inline-flex items-center mt-6 text-mbnb-coral hover:text-mbnb-coral-dark font-medium",children:["D\xe9couvrir toutes les fonctionnalit\xe9s",(0,d.jsx)(o,{className:"w-5 h-5 ml-2"})]})]}),(0,d.jsx)("div",{className:"bg-gradient-to-br from-gray-200 to-gray-300 rounded-2xl h-96"})]})})}),(0,d.jsx)("section",{className:"py-20",children:(0,d.jsxs)("div",{className:"container mx-auto px-4",children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Ils nous font confiance"}),(0,d.jsx)("p",{className:"text-lg text-gray-600",children:"D\xe9couvrez les succ\xe8s de nos providers partenaires"})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:s.map((a,b)=>(0,d.jsxs)("div",{className:"bg-white border rounded-xl p-8",children:[(0,d.jsxs)("p",{className:"text-gray-700 mb-6 italic",children:['"',a.quote,'"']}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-bold text-gray-900",children:a.name}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:a.company}),(0,d.jsxs)("p",{className:"text-sm text-gray-500",children:[a.properties," propri\xe9t\xe9s • ",a.location]})]}),(0,d.jsx)("div",{className:"flex -space-x-1",children:[void 0,void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsx)("span",{className:"inline-block w-8 h-8 bg-yellow-400 rounded-full border-2 border-white"},b))})]})]},b))})]})}),(0,d.jsx)("section",{className:"py-20 bg-gradient-to-r from-mbnb-coral to-mbnb-coral-dark",children:(0,d.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,d.jsx)("h2",{className:"text-4xl font-bold text-white mb-4",children:"Pr\xeat \xe0 acc\xe9l\xe9rer votre croissance?"}),(0,d.jsx)("p",{className:"text-xl text-white/90 mb-8 max-w-2xl mx-auto",children:"Rejoignez le programme Provider et b\xe9n\xe9ficiez d'avantages exclusifs d\xe8s aujourd'hui."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)(f(),{href:"/provider/signup",className:"px-8 py-4 bg-white text-mbnb-coral rounded-lg hover:bg-gray-100 transition-colors font-medium",children:"Commencer maintenant"}),(0,d.jsx)(f(),{href:"/contact",className:"px-8 py-4 bg-white/20 text-white rounded-lg hover:bg-white/30 transition-colors font-medium",children:"Parler \xe0 un conseiller"})]}),(0,d.jsx)("p",{className:"mt-8 text-white/80 text-sm",children:"Commission competitive: 14% sur h\xe9bergements, 18% sur activit\xe9s"})]})})]})}},33873:a=>{a.exports=require("path")},41025:a=>{a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},43828:(a,b,c)=>{c.d(b,{A:()=>f});var d=c(11110),e={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let f=(a,b)=>{let c=(0,d.forwardRef)(({color:c="currentColor",size:f=24,strokeWidth:g=2,absoluteStrokeWidth:h,className:i="",children:j,...k},l)=>(0,d.createElement)("svg",{ref:l,...e,width:f,height:f,stroke:c,strokeWidth:h?24*Number(g)/Number(f):g,className:["lucide",`lucide-${a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim()}`,i].join(" "),...k},[...b.map(([a,b])=>(0,d.createElement)(a,b)),...Array.isArray(j)?j:[j]]));return c.displayName=`${a}`,c}},56988:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(43828).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},63033:a=>{a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70679:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(43828).A)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},71911:(a,b,c)=>{c.r(b),c.d(b,{GlobalError:()=>C.default,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(73653),e=c(97714),f=c(85250),g=c(37587),h=c(22369),i=c(1889),j=c(96232),k=c(22841),l=c(46537),m=c(46027),n=c(78559),o=c(75928),p=c(19374),q=c(65971),r=c(261),s=c(79898),t=c(32967),u=c(26713),v=c(40139),w=c(14248),x=c(59580),y=c(57749),z=c(53123),A=c(89745),B=c(86439),C=c(96133),D=c(18283),E=c(39818),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["provider",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,32047)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/provider/page.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,69669)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/provider/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,56035)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,74827)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/error.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,96133)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/global-error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,72993)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,15034,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,54693,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/provider/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/provider/page",pathname:"/provider",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",relativeProjectDir:""});async function K(a,b,d){var F;let L="/provider/page";"/index"===L&&(L="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await J.prepare(a,b,{srcPage:L,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(L),{isOnDemandRevalidate:ah}=O,ai=J.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(F=$.routes[ag]??$.dynamicRoutes[ag])?void 0:F.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===J.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&J.isDev&&(aB=aa),J.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...D,tree:G,pages:H,GlobalError:C.default,handler:K,routeModule:J,__next_app__:I};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:L,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=J.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:J,page:L,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),J.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!J.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===J.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await J.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await J.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!J.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&E.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:L,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},86439:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external")},89869:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(43828).A)("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]])},90249:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(43828).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},95249:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(43828).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[4635,2922,3743,4367,3260],()=>b(b.s=71911));module.exports=c})();
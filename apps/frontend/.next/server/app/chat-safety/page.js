"use strict";(()=>{var a={};a.id=7838,a.ids=[7838],a.modules={261:a=>{a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1332:(a,b,c)=>{c.r(b),c.d(b,{default:()=>v,metadata:()=>r});var d=c(5939),e=c(3498),f=c.n(e),g=c(10393),h=c(96449),i=c(14368),j=c(51974),k=c(92176),l=c(24316),m=c(54716),n=c(77133),o=c(66404),p=c(49942),q=c(99151);let r={title:"S\xe9curit\xe9 Messagerie | Mbnb",description:"R\xe8gles de s\xe9curit\xe9 et bonnes pratiques pour la messagerie Mbnb."},s=[{title:"Communications autoris\xe9es",icon:g.A,color:"text-green-600 bg-green-50",items:["Questions sur l'h\xe9bergement et \xe9quipements","Coordination heure d'arriv\xe9e/d\xe9part","Demandes d'informations touristiques locales","Clarifications sur r\xe8glement int\xe9rieur","Recommandations restaurants/activit\xe9s","Instructions d'acc\xe8s au logement","Signalement de probl\xe8mes pendant s\xe9jour","\xc9changes courtois et professionnels"]},{title:"Strictement interdit",icon:h.A,color:"text-red-600 bg-red-50",items:["Demander paiement hors plateforme Mbnb","Partager coordonn\xe9es bancaires","Envoyer liens externes suspects","Demander documents d'identit\xe9 par message","Harc\xe8lement ou messages inappropri\xe9s","Spam ou publicit\xe9 non sollicit\xe9e","Discrimination ou propos offensants","Tentatives de contournement des frais Mbnb"]}],t=[{signal:"Demande de paiement direct",description:"L'h\xf4te demande virement bancaire ou esp\xe8ces",action:"Refusez et signalez imm\xe9diatement",severity:"critical"},{signal:"Prix diff\xe9rent de l'annonce",description:"Tentative de modifier le tarif apr\xe8s r\xe9servation",action:"Gardez preuve et contactez support",severity:"high"},{signal:"Communication hors plateforme",description:"Demande d'\xe9changer via WhatsApp/Email personnel",action:"Restez sur messagerie Mbnb uniquement",severity:"high"},{signal:"Demande informations sensibles",description:"Num\xe9ro carte bancaire, mot de passe, etc.",action:"Ne jamais partager, signalez le profil",severity:"critical"},{signal:"Annulation forc\xe9e",description:"Pression pour annuler la r\xe9servation",action:"Documentez et appelez support urgence",severity:"critical"},{signal:"H\xe9bergement diff\xe9rent",description:"Proposition d'un autre bien \xe0 l'arriv\xe9e",action:"Refusez, exigez remboursement complet",severity:"high"}],u=[{feature:"Traduction automatique",description:"Messages traduits en temps r\xe9el dans 7 langues",icon:"\uD83C\uDF0D"},{feature:"D\xe9tection IA fraude",description:"Analyse automatique messages suspects",icon:"\uD83E\uDD16"},{feature:"Chiffrement bout-en-bout",description:"Messages crypt\xe9s et s\xe9curis\xe9s",icon:"\uD83D\uDD10"},{feature:"Historique permanent",description:"Conservation 5 ans pour protection",icon:"\uD83D\uDCDA"},{feature:"Blocage utilisateur",description:"Bloquez contacts ind\xe9sirables en 1 clic",icon:"\uD83D\uDEAB"},{feature:"Support int\xe9gr\xe9",description:"Escalade rapide vers \xe9quipe Mbnb",icon:"\uD83C\uDD98"}];function v(){return(0,d.jsx)("main",{className:"min-h-screen py-16",children:(0,d.jsxs)("div",{className:"container-mbnb",children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsx)("div",{className:"w-20 h-20 bg-mbnb-coral rounded-full flex items-center justify-center mx-auto mb-6",children:(0,d.jsx)(i.A,{className:"w-10 h-10 text-white"})}),(0,d.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-4",children:"S\xe9curit\xe9 Messagerie Mbnb"}),(0,d.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Communiquez en toute s\xe9curit\xe9 avec nos r\xe8gles et protections avanc\xe9es"})]}),(0,d.jsxs)("div",{className:"bg-mbnb-coral rounded-2xl p-8 mb-12 text-white",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center gap-4 mb-4",children:[(0,d.jsx)(j.A,{className:"w-8 h-8"}),(0,d.jsx)("h2",{className:"text-2xl font-bold",children:"R\xe8gle d'Or Mbnb"}),(0,d.jsx)(j.A,{className:"w-8 h-8"})]}),(0,d.jsx)("p",{className:"text-center text-lg",children:"Toutes les communications et paiements doivent OBLIGATOIREMENT passer par la plateforme Mbnb. C'est votre garantie de protection juridique et financi\xe8re."})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-12",children:s.map(a=>{let b=a.icon;return(0,d.jsxs)("div",{className:`rounded-2xl p-8 ${a.color.split(" ")[1]}`,children:[(0,d.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,d.jsx)(b,{className:`w-7 h-7 ${a.color.split(" ")[0]}`}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:a.title})]}),(0,d.jsx)("ul",{className:"space-y-3",children:a.items.map((b,c)=>(0,d.jsxs)("li",{className:"flex items-start gap-3",children:[(0,d.jsx)("span",{className:`mt-1 w-2 h-2 rounded-full flex-shrink-0 ${"Communications autoris\xe9es"===a.title?"bg-green-500":"bg-red-500"}`}),(0,d.jsx)("span",{className:"text-gray-700",children:b})]},c))})]},a.title)})}),(0,d.jsxs)("div",{className:"bg-red-50 rounded-2xl p-8 mb-12",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,d.jsx)(k.A,{className:"w-8 h-8 text-red-600"}),(0,d.jsx)("h2",{className:"text-2xl font-semibold text-gray-900",children:"Signaux d'alerte \xe0 reconna\xeetre"})]}),(0,d.jsx)("div",{className:"space-y-4",children:t.map((a,b)=>(0,d.jsx)("div",{className:`bg-white rounded-xl p-6 border-l-4 ${"critical"===a.severity?"border-red-500":"border-orange-500"}`,children:(0,d.jsx)("div",{className:"flex items-start justify-between",children:(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,d.jsx)("h4",{className:"font-semibold text-gray-900",children:a.signal}),"critical"===a.severity&&(0,d.jsx)("span",{className:"bg-red-100 text-red-700 px-2 py-0.5 rounded-full text-xs font-medium",children:"CRITIQUE"})]}),(0,d.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:a.description}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(l.A,{className:"w-4 h-4 text-mbnb-coral"}),(0,d.jsx)("span",{className:"text-mbnb-coral font-medium text-sm",children:a.action})]})]})})},b))})]}),(0,d.jsxs)("div",{className:"mb-12",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-8 text-center",children:"Protection int\xe9gr\xe9e \xe0 la messagerie"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:u.map(a=>(0,d.jsxs)("div",{className:"bg-white border rounded-xl p-6 hover:shadow-md transition-shadow",children:[(0,d.jsx)("div",{className:"text-3xl mb-4",children:a.icon}),(0,d.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:a.feature}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:a.description})]},a.feature))})]}),(0,d.jsxs)("div",{className:"bg-mbnb-navy rounded-2xl p-8 mb-12 text-white",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold mb-6 text-center",children:"Bonnes pratiques de communication"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"bg-white/10 backdrop-blur rounded-xl p-6",children:[(0,d.jsx)(m.A,{className:"w-8 h-8 mb-3"}),(0,d.jsx)("h3",{className:"font-semibold mb-3",children:"V\xe9rifiez le profil"}),(0,d.jsxs)("ul",{className:"space-y-2 text-sm opacity-90",children:[(0,d.jsx)("li",{children:'• Badge "Identit\xe9 v\xe9rifi\xe9e"'}),(0,d.jsx)("li",{children:"• Historique des \xe9valuations"}),(0,d.jsx)("li",{children:"• Date d'inscription"}),(0,d.jsx)("li",{children:"• Taux de r\xe9ponse"})]})]}),(0,d.jsxs)("div",{className:"bg-white/10 backdrop-blur rounded-xl p-6",children:[(0,d.jsx)(n.A,{className:"w-8 h-8 mb-3"}),(0,d.jsx)("h3",{className:"font-semibold mb-3",children:"Documentez tout"}),(0,d.jsxs)("ul",{className:"space-y-2 text-sm opacity-90",children:[(0,d.jsx)("li",{children:"• Captures d'\xe9cran des \xe9changes"}),(0,d.jsx)("li",{children:"• Photos \xe9tat des lieux"}),(0,d.jsx)("li",{children:"• Confirmations \xe9crites"}),(0,d.jsx)("li",{children:"• Dates et heures pr\xe9cises"})]})]}),(0,d.jsxs)("div",{className:"bg-white/10 backdrop-blur rounded-xl p-6",children:[(0,d.jsx)(o.A,{className:"w-8 h-8 mb-3"}),(0,d.jsx)("h3",{className:"font-semibold mb-3",children:"Paiements s\xe9curis\xe9s"}),(0,d.jsxs)("ul",{className:"space-y-2 text-sm opacity-90",children:[(0,d.jsx)("li",{children:"• Uniquement via Mbnb"}),(0,d.jsx)("li",{children:"• Jamais en esp\xe8ces direct"}),(0,d.jsx)("li",{children:"• Refusez virements externes"}),(0,d.jsx)("li",{children:"• Gardez re\xe7us Mbnb"})]})]}),(0,d.jsxs)("div",{className:"bg-white/10 backdrop-blur rounded-xl p-6",children:[(0,d.jsx)(p.A,{className:"w-8 h-8 mb-3"}),(0,d.jsx)("h3",{className:"font-semibold mb-3",children:"Signalez rapidement"}),(0,d.jsxs)("ul",{className:"space-y-2 text-sm opacity-90",children:[(0,d.jsx)("li",{children:"• Comportements suspects"}),(0,d.jsx)("li",{children:"• Demandes inappropri\xe9es"}),(0,d.jsx)("li",{children:"• Tentatives d'arnaque"}),(0,d.jsx)("li",{children:"• Violations des r\xe8gles"})]})]})]})]}),(0,d.jsxs)("div",{className:"bg-white border-2 border-mbnb-coral rounded-2xl p-8 text-center mb-12",children:[(0,d.jsx)(p.A,{className:"w-12 h-12 text-mbnb-coral mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Signaler un probl\xe8me"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:"Message suspect ? Comportement inappropri\xe9 ? Signalez imm\xe9diatement."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)("button",{className:"bg-mbnb-coral text-white px-8 py-3 rounded-full font-semibold hover:bg-mbnb-coral/90 transition-all transform hover:scale-105",children:"Signaler maintenant"}),(0,d.jsx)(f(),{href:"/help/safety",className:"bg-transparent border border-mbnb-coral text-mbnb-coral px-8 py-3 rounded-full font-semibold hover:bg-mbnb-coral hover:text-white transition-all",children:"Guide s\xe9curit\xe9 complet"})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,d.jsxs)(f(),{href:"/help",className:"bg-gray-50 rounded-xl p-6 hover:shadow-md transition-shadow text-center",children:[(0,d.jsx)(q.A,{className:"w-8 h-8 text-mbnb-coral mx-auto mb-3"}),(0,d.jsx)("h4",{className:"font-semibold text-gray-900 mb-1",children:"Centre d'aide"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Toutes les r\xe9ponses"})]}),(0,d.jsxs)(f(),{href:"/trust-safety",className:"bg-gray-50 rounded-xl p-6 hover:shadow-md transition-shadow text-center",children:[(0,d.jsx)(j.A,{className:"w-8 h-8 text-mbnb-coral mx-auto mb-3"}),(0,d.jsx)("h4",{className:"font-semibold text-gray-900 mb-1",children:"Confiance & S\xe9curit\xe9"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Nos engagements"})]}),(0,d.jsxs)(f(),{href:"/contact",className:"bg-gray-50 rounded-xl p-6 hover:shadow-md transition-shadow text-center",children:[(0,d.jsx)(i.A,{className:"w-8 h-8 text-mbnb-coral mx-auto mb-3"}),(0,d.jsx)("h4",{className:"font-semibold text-gray-900 mb-1",children:"Contact Support"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Assistance 24/7"})]})]})]})})}},3295:a=>{a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10393:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},10846:a=>{a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14368:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"}))})},19121:a=>{a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24316:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))})},26713:a=>{a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{a.exports=require("util")},29294:a=>{a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{a.exports=require("path")},41025:a=>{a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},42191:(a,b,c)=>{c.r(b),c.d(b,{GlobalError:()=>C.default,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(73653),e=c(97714),f=c(85250),g=c(37587),h=c(22369),i=c(1889),j=c(96232),k=c(22841),l=c(46537),m=c(46027),n=c(78559),o=c(75928),p=c(19374),q=c(65971),r=c(261),s=c(79898),t=c(32967),u=c(26713),v=c(40139),w=c(14248),x=c(59580),y=c(57749),z=c(53123),A=c(89745),B=c(86439),C=c(96133),D=c(18283),E=c(39818),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["chat-safety",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,1332)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/chat-safety/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,56035)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,74827)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/error.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,96133)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/global-error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,72993)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,15034,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,54693,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/chat-safety/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/chat-safety/page",pathname:"/chat-safety",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",relativeProjectDir:""});async function K(a,b,d){var F;let L="/chat-safety/page";"/index"===L&&(L="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await J.prepare(a,b,{srcPage:L,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(L),{isOnDemandRevalidate:ah}=O,ai=J.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(F=$.routes[ag]??$.dynamicRoutes[ag])?void 0:F.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===J.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&J.isDev&&(aB=aa),J.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...D,tree:G,pages:H,GlobalError:C.default,handler:K,routeModule:J,__next_app__:I};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:L,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=J.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:J,page:L,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),J.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!J.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===J.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await J.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await J.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!J.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&E.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:L,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},49942:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 3v1.5M3 21v-6m0 0 2.77-.693a9 9 0 0 1 6.208.682l.108.054a9 9 0 0 0 6.086.71l3.114-.732a48.524 48.524 0 0 1-.005-10.499l-3.11.732a9 9 0 0 1-6.085-.711l-.108-.054a9 9 0 0 0-6.208-.682L3 4.5M3 15V4.5"}))})},51974:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))})},54716:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))})},63033:a=>{a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66404:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"}))})},77133:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))})},86439:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external")},92176:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))})},96449:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},99151:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"}))})}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[4635,2922,4367],()=>b(b.s=42191));module.exports=c})();
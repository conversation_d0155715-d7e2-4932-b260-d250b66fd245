"use strict";(()=>{var a={};a.id=5420,a.ids=[5420],a.modules={261:a=>{a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1691:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"}))})},2688:(a,b,c)=>{c.r(b),c.d(b,{default:()=>w,metadata:()=>r});var d=c(5939),e=c(3498),f=c.n(e),g=c(23633),h=c(36839),i=c(15600),j=c(1691),k=c(51974),l=c(77133),m=c(92176),n=c(15915),o=c(96449),p=c(10393),q=c(14368);let r={title:"Centre de confiance et s\xe9curit\xe9 Mbnb | Standards communaut\xe9",description:"Centre de confiance Mbnb : standards communaut\xe9, signalement, mod\xe9ration 24h/24. Construisons ensemble une communaut\xe9 s\xfbre et respectueuse.",keywords:"confiance, s\xe9curit\xe9, standards communaut\xe9, signalement, mod\xe9ration, respect, Mbnb",openGraph:{title:"Centre de confiance et s\xe9curit\xe9 - Mbnb",description:"Standards et outils pour une communaut\xe9 s\xfbre et respectueuse",images:["/images/og/trust-safety.jpg"]}},s=[{icon:j.A,title:"Respect mutuel",description:"Traiter tous les membres avec dignit\xe9 et courtoisie",rules:["Communication respectueuse en toutes circonstances","Aucune discrimination bas\xe9e sur origine, religion, genre","Respect des diff\xe9rences culturelles et linguistiques","Tol\xe9rance z\xe9ro pour harc\xe8lement ou intimidation","Promotion d'un environnement inclusif et bienveillant"],violations:["Insultes","Discrimination","Harc\xe8lement","Menaces"]},{icon:k.A,title:"S\xe9curit\xe9 physique",description:"Pr\xe9server l'int\xe9grit\xe9 et la s\xe9curit\xe9 de tous",rules:["Respecter capacit\xe9 maximum du logement d\xe9clar\xe9e","Interdiction d'acc\xe8s aux personnes non autoris\xe9es","Respect des r\xe8gles de s\xe9curit\xe9 incendie","Signalement imm\xe9diat de dangers ou probl\xe8mes","Coop\xe9ration avec les autorit\xe9s si n\xe9cessaire"],violations:["Suroccupation","Acc\xe8s non autoris\xe9","N\xe9gligence s\xe9curit\xe9","Non-coop\xe9ration"]},{icon:l.A,title:"Transparence et honn\xeatet\xe9",description:"Fournir des informations exactes et compl\xe8tes",rules:["Descriptions de logements pr\xe9cises et \xe0 jour","Photos r\xe9centes et repr\xe9sentatives r\xe9ellement","D\xe9claration exacte de tous frais et conditions","Identit\xe9 v\xe9rifi\xe9e et informations personnelles exactes","Avis et \xe9valuations honn\xeates et constructifs"],violations:["Fausses informations","Photos trompeuses","Frais cach\xe9s","Identit\xe9 falsifi\xe9e"]},{icon:m.A,title:"Activit\xe9s interdites",description:"Comportements et usages strictement proscrits",rules:["Aucune activit\xe9 ill\xe9gale dans les logements","Interdiction de f\xeates non autoris\xe9es ou nuisances","Respect de la r\xe9glementation locale marocaine","Aucune sous-location sans accord Host","Interdiction de drogues et substances illicites"],violations:["Activit\xe9s ill\xe9gales","Nuisances","Sous-location","Substances interdites"]}],t=[{step:1,title:"Signalement imm\xe9diat",description:"Utilisez nos outils de signalement int\xe9gr\xe9s",details:["Bouton signalement dans chaque conversation","Formulaire d\xe9taill\xe9 avec cat\xe9gories pr\xe9d\xe9finies","Possibilit\xe9 d'ajouter photos/captures d'\xe9cran","Option signalement anonyme disponible","Accus\xe9 r\xe9ception automatique avec num\xe9ro suivi"],timeframe:"Imm\xe9diat"},{step:2,title:"\xc9valuation initiale",description:"Notre \xe9quipe \xe9value la gravit\xe9 du signalement",details:["Analyse dans les 2h pour urgences s\xe9curit\xe9","Classification selon gravit\xe9 et type d'incident","Mesures conservatoires imm\xe9diates si n\xe9cessaire","Contact des parties concern\xe9es selon protocole","Documentation compl\xe8te du dossier"],timeframe:"2-24h selon urgence"},{step:3,title:"Enqu\xeate approfondie",description:"Investigation d\xe9taill\xe9e par nos experts",details:["Collecte de preuves et t\xe9moignages","Entretiens avec toutes les parties impliqu\xe9es","V\xe9rification crois\xe9e des informations","Consultation d'experts externes si n\xe9cessaire","Respect des proc\xe9dures l\xe9gales marocaines"],timeframe:"3-10 jours ouvr\xe9s"},{step:4,title:"D\xe9cision et sanctions",description:"Application de mesures proportionnelles",details:["D\xe9cision bas\xe9e sur preuves et standards communaut\xe9","Sanctions gradu\xe9es selon gravit\xe9","Notification \xe9crite des d\xe9cisions prises","Droit de recours dans les 15 jours","Suivi des mesures correctives"],timeframe:"1-3 jours apr\xe8s enqu\xeate"}],u=[{level:"Avertissement",icon:m.A,color:"text-yellow-600 bg-yellow-100",description:"Premier rappel des r\xe8gles communautaires",triggers:["Premier manquement mineur","Communication inappropri\xe9e","Retard communication"],consequences:["Notification dans profil","Rappel des r\xe8gles","Sensibilisation"],duration:"30 jours"},{level:"Restriction temporaire",icon:n.A,color:"text-orange-600 bg-orange-100",description:"Limitation d'acc\xe8s aux fonctionnalit\xe9s",triggers:["Manquements r\xe9p\xe9t\xe9s","Violation mod\xe9r\xe9e","Non-respect avertissement"],consequences:["Limitation r\xe9servations","Restrictions messagerie","Supervision accrue"],duration:"7-90 jours"},{level:"Suspension temporaire",icon:o.A,color:"text-red-600 bg-red-100",description:"Interdiction temporaire d'utilisation",triggers:["Violation grave","R\xe9cidive","Mise en danger s\xe9curit\xe9"],consequences:["Compte d\xe9sactiv\xe9","Annulation r\xe9servations","Remboursements"],duration:"30 jours \xe0 1 an"},{level:"Exclusion d\xe9finitive",icon:k.A,color:"text-gray-600 bg-gray-100",description:"Bannissement permanent de la plateforme",triggers:["Activit\xe9s ill\xe9gales","Mise en danger grave","Violations multiples graves"],consequences:["Compte supprim\xe9","Interdiction cr\xe9ation nouveau compte","Signalement autorit\xe9s"],duration:"Permanente"}],v=[{title:"Centre d'aide s\xe9curit\xe9",description:"Guides d\xe9taill\xe9s et FAQ sur tous les aspects s\xe9curit\xe9",link:"/help/safety",languages:"Fran\xe7ais, Arabe, Anglais, Espagnol"},{title:"Chat support prioritaire",description:"Assistance imm\xe9diate pour probl\xe8mes s\xe9curit\xe9",link:"/chat-safety",languages:"24h/24 - Support multilingue"},{title:"Hotline urgence",description:"Ligne t\xe9l\xe9phonique directe pour urgences",link:"tel:+************",languages:"+212 5 22 00 00 00"},{title:"Email confidentiel",description:"Signalement confidentiel pour cas sensibles",link:"mailto:<EMAIL>",languages:"<EMAIL>"}];function w(){return(0,d.jsxs)("div",{className:"min-h-screen bg-neutral-50",children:[(0,d.jsx)("section",{className:"bg-mbnb-navy text-white py-16",children:(0,d.jsx)("div",{className:"container mx-auto px-4",children:(0,d.jsxs)("div",{className:"text-center max-w-4xl mx-auto",children:[(0,d.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-6",children:"Centre de confiance et s\xe9curit\xe9"}),(0,d.jsx)("p",{className:"text-xl md:text-2xl mb-8 text-white/90",children:"Ensemble, construisons une communaut\xe9 Mbnb s\xfbre et respectueuse"}),(0,d.jsxs)("div",{className:"flex flex-wrap justify-center gap-4",children:[(0,d.jsx)(i.E,{variant:"default",className:"text-sm px-4 py-2",children:"Mod\xe9ration 24h/24"}),(0,d.jsx)(i.E,{variant:"outline",className:"text-sm px-4 py-2 border-white text-white",children:"Signalement instantan\xe9"}),(0,d.jsx)(i.E,{variant:"outline",className:"text-sm px-4 py-2 border-white text-white",children:"Standards stricts"})]})]})})}),(0,d.jsx)("section",{className:"py-16",children:(0,d.jsx)("div",{className:"container mx-auto px-4",children:(0,d.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-neutral-900 mb-4",children:"Standards de notre communaut\xe9"}),(0,d.jsx)("p",{className:"text-xl text-neutral-600 max-w-3xl mx-auto",children:"Ces r\xe8gles garantissent une exp\xe9rience positive et s\xe9curis\xe9e pour tous les membres Mbnb"})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:s.map((a,b)=>{let c=a.icon;return(0,d.jsxs)(h.Zp,{className:"hover:shadow-lg transition-shadow",children:[(0,d.jsx)(h.aR,{children:(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-3 bg-primary-100 rounded-lg",children:(0,d.jsx)(c,{className:"h-6 w-6 text-primary-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)(h.ZB,{className:"text-xl text-neutral-900",children:a.title}),(0,d.jsx)("p",{className:"text-sm text-neutral-600",children:a.description})]})]})}),(0,d.jsxs)(h.Wu,{children:[(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsx)("h4",{className:"font-medium text-neutral-900 mb-2",children:"Ce que nous attendons :"}),(0,d.jsx)("ul",{className:"space-y-1",children:a.rules.map((a,b)=>(0,d.jsxs)("li",{className:"text-sm text-neutral-600 flex items-start",children:[(0,d.jsx)(p.A,{className:"w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0"}),a]},b))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-neutral-900 mb-2",children:"Violations courantes :"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:a.violations.map((a,b)=>(0,d.jsx)(i.E,{variant:"outline",className:"text-xs text-red-600 border-red-200",children:a},b))})]})]})]},b)})})]})})}),(0,d.jsx)("section",{className:"py-16 bg-white",children:(0,d.jsx)("div",{className:"container mx-auto px-4",children:(0,d.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-neutral-900 mb-4",children:"Processus de signalement"}),(0,d.jsx)("p",{className:"text-xl text-neutral-600 max-w-3xl mx-auto",children:"Votre s\xe9curit\xe9 est notre priorit\xe9. Voici comment nous traitons vos signalements"})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:t.map((a,b)=>(0,d.jsxs)(h.Zp,{className:"relative hover:shadow-lg transition-shadow",children:[(0,d.jsxs)(h.Wu,{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,d.jsx)("div",{className:"bg-primary-500 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold",children:a.step}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-neutral-900",children:a.title})]}),(0,d.jsx)("p",{className:"text-neutral-600 mb-4",children:a.description}),(0,d.jsx)("ul",{className:"space-y-1 mb-4",children:a.details.map((a,b)=>(0,d.jsxs)("li",{className:"text-xs text-neutral-600 flex items-start",children:[(0,d.jsx)("span",{className:"text-primary-500 mr-2",children:"•"}),a]},b))}),(0,d.jsxs)(i.E,{variant:"outline",className:"text-xs",children:[(0,d.jsx)(n.A,{className:"w-3 h-3 mr-1"}),a.timeframe]})]}),b<t.length-1&&(0,d.jsx)("div",{className:"hidden lg:block absolute top-1/2 -right-3 transform -translate-y-1/2",children:(0,d.jsx)("div",{className:"w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center",children:(0,d.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"})})})]},b))})]})})}),(0,d.jsx)("section",{className:"py-16",children:(0,d.jsx)("div",{className:"container mx-auto px-4",children:(0,d.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-neutral-900 mb-4",children:"Syst\xe8me de sanctions"}),(0,d.jsx)("p",{className:"text-xl text-neutral-600 max-w-3xl mx-auto",children:"Sanctions proportionnelles et gradu\xe9es selon la gravit\xe9 des violations"})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:u.map((a,b)=>{let c=a.icon;return(0,d.jsx)(h.Zp,{className:"hover:shadow-lg transition-shadow",children:(0,d.jsxs)(h.Wu,{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,d.jsx)("div",{className:`p-2 rounded-lg ${a.color}`,children:(0,d.jsx)(c,{className:"h-5 w-5"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-neutral-900",children:a.level}),(0,d.jsx)("p",{className:"text-sm text-neutral-600",children:a.description})]})]}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-neutral-900 mb-1",children:"D\xe9clencheurs :"}),(0,d.jsx)("ul",{className:"space-y-1",children:a.triggers.map((a,b)=>(0,d.jsxs)("li",{className:"text-xs text-neutral-600 flex items-start",children:[(0,d.jsx)("span",{className:"text-neutral-400 mr-2",children:"•"}),a]},b))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-neutral-900 mb-1",children:"Cons\xe9quences :"}),(0,d.jsx)("ul",{className:"space-y-1",children:a.consequences.map((a,b)=>(0,d.jsxs)("li",{className:"text-xs text-neutral-600 flex items-start",children:[(0,d.jsx)("span",{className:"text-neutral-400 mr-2",children:"•"}),a]},b))})]}),(0,d.jsx)("div",{className:"pt-2 border-t",children:(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-xs font-medium text-neutral-700",children:"Dur\xe9e :"}),(0,d.jsx)(i.E,{variant:"outline",className:"text-xs",children:a.duration})]})})]})]})},b)})})]})})}),(0,d.jsx)("section",{className:"py-16 bg-white",children:(0,d.jsx)("div",{className:"container mx-auto px-4",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-neutral-900 mb-4",children:"Ressources d'aide"}),(0,d.jsx)("p",{className:"text-xl text-neutral-600",children:"Plusieurs canaux pour obtenir de l'aide selon vos besoins"})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:v.map((a,b)=>(0,d.jsx)(h.Zp,{className:"hover:shadow-lg transition-shadow",children:(0,d.jsx)(h.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,d.jsx)("div",{className:"p-3 bg-secondary-100 rounded-lg",children:(0,d.jsx)(q.A,{className:"h-6 w-6 text-secondary-600"})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-neutral-900 mb-2",children:a.title}),(0,d.jsx)("p",{className:"text-neutral-600 mb-3",children:a.description}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-neutral-500",children:a.languages}),(0,d.jsx)(f(),{href:a.link,children:(0,d.jsx)(g.A,{size:"sm",variant:"outline",children:"Acc\xe9der"})})]})]})]})})},b))})]})})}),(0,d.jsx)("section",{className:"py-12 bg-red-50",children:(0,d.jsx)("div",{className:"container mx-auto px-4",children:(0,d.jsx)("div",{className:"max-w-3xl mx-auto",children:(0,d.jsx)(h.Zp,{className:"border-red-200",children:(0,d.jsxs)(h.Wu,{className:"p-8 text-center",children:[(0,d.jsx)(m.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,d.jsx)("h2",{className:"text-2xl font-bold text-neutral-900 mb-4",children:"Urgence s\xe9curit\xe9 imm\xe9diate ?"}),(0,d.jsx)("p",{className:"text-neutral-600 mb-6",children:"Si vous \xeates en danger imm\xe9diat ou t\xe9moin d'une situation d'urgence, contactez imm\xe9diatement les autorit\xe9s locales puis notre \xe9quipe."}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsx)(f(),{href:"tel:19",children:(0,d.jsx)(g.A,{className:"w-full bg-red-500 hover:bg-red-600",children:"Police Maroc: 19"})}),(0,d.jsx)(f(),{href:"tel:+************",children:(0,d.jsx)(g.A,{variant:"outline",className:"w-full border-red-500 text-red-500 hover:bg-red-50",children:"Urgence Mbnb: +212 5 22 00 00 00"})})]}),(0,d.jsx)("p",{className:"text-xs text-neutral-500",children:"Lignes disponibles 24h/24, 7j/7"})]})]})})})})}),(0,d.jsx)("section",{className:"py-16 bg-mbnb-navy text-white",children:(0,d.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-bold mb-6",children:"Signaler un probl\xe8me ?"}),(0,d.jsx)("p",{className:"text-xl mb-8 text-white/90",children:"Votre signalement nous aide \xe0 maintenir une communaut\xe9 s\xfbre pour tous"}),(0,d.jsxs)("div",{className:"flex flex-wrap justify-center gap-4",children:[(0,d.jsx)(f(),{href:"/report",children:(0,d.jsx)(g.A,{size:"lg",variant:"primary",className:"px-8",children:"Faire un signalement"})}),(0,d.jsx)(f(),{href:"/safety",children:(0,d.jsx)(g.A,{size:"lg",variant:"outline",className:"px-8 border-white text-white hover:bg-white hover:text-primary-600",children:"Guide s\xe9curit\xe9"})})]})]})})]})}},3295:a=>{a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10393:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},10846:a=>{a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14368:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"}))})},15600:(a,b,c)=>{c.d(b,{E:()=>h});var d=c(5939),e=c(11110),f=c.n(e),g=c(56818);let h=f().forwardRef(({className:a,variant:b="default",size:c="md",rounded:e="md",icon:f,removable:h=!1,onRemove:i,children:j,...k},l)=>(0,d.jsxs)("span",{ref:l,className:(0,g.cn)("inline-flex items-center gap-1.5 font-medium border",{default:"bg-gray-100 text-gray-dark border-gray-200",success:"bg-mbnb-teal-dark bg-opacity-10 text-mbnb-teal-dark border-mbnb-teal-dark",warning:"bg-orange-100 text-orange-800 border-orange-200",danger:"bg-red-100 text-red-800 border-red-200",info:"bg-blue-100 text-blue-800 border-blue-200",outline:"bg-transparent text-gray-dark border-gray-dark"}[b],{sm:"px-2 py-0.5 text-xs",md:"px-2.5 py-1 text-sm",lg:"px-3 py-1.5 text-base"}[c],{sm:"rounded",md:"rounded-md",lg:"rounded-lg",full:"rounded-full"}[e],a),...k,children:[f&&(0,d.jsx)("span",{className:"flex-shrink-0",children:f}),j,h&&(0,d.jsx)("button",{onClick:a=>{a.stopPropagation(),i?.()},className:"ml-1 -mr-1 hover:opacity-75 transition-opacity","aria-label":"Supprimer",children:(0,d.jsx)("svg",{className:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}));h.displayName="Badge"},15915:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},19121:a=>{a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23633:(a,b,c)=>{c.d(b,{A:()=>i});var d=c(5939),e=c(11110),f=c.n(e),g=c(56818);let h=f().forwardRef(({className:a,variant:b="primary",size:c="md",fullWidth:e=!1,loading:f=!1,icon:h,iconPosition:i="left",disabled:j,children:k,...l},m)=>(0,d.jsxs)("button",{ref:m,className:(0,g.cn)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-mbnb-coral focus:ring-offset-2","disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-mbnb-coral hover:bg-mbnb-coral-dark text-white shadow-sm",secondary:"bg-neutral-100 hover:bg-neutral-200 text-gray-dark",outline:"border-2 border-mbnb-coral text-mbnb-coral hover:bg-mbnb-coral hover:text-white",ghost:"text-gray-dark hover:bg-gray-100",danger:"bg-red-600 hover:bg-red-700 text-white",success:"bg-mbnb-teal-dark hover:bg-mbnb-teal-dark text-white"}[b],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg",xl:"px-8 py-4 text-xl"}[c],e&&"w-full",f&&"cursor-wait",a),disabled:j||f,...l,children:[f?(0,d.jsxs)("svg",{className:"animate-spin h-5 w-5 mr-2",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,d.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,d.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"})]}):h&&"left"===i?(0,d.jsx)("span",{className:"mr-2",children:h}):null,k,!f&&h&&"right"===i&&(0,d.jsx)("span",{className:"ml-2",children:h})]}));h.displayName="Button";let i=h},26713:a=>{a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{a.exports=require("util")},29294:a=>{a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{a.exports=require("path")},36839:(a,b,c)=>{c.d(b,{Wu:()=>k,ZB:()=>j,Zp:()=>h,aR:()=>i});var d=c(5939),e=c(11110),f=c.n(e),g=c(56818);let h=f().forwardRef(({className:a,variant:b="default",padding:c="md",fullHeight:e=!1,hover:f=!1,children:h,...i},j)=>(0,d.jsx)("div",{ref:j,className:(0,g.cn)("rounded-xl overflow-hidden transition-all duration-300",{default:"bg-white border border-gray-200",elevated:"bg-white shadow-lg",outlined:"bg-transparent border-2 border-gray-medium",interactive:"bg-white border border-gray-200 cursor-pointer"}[b],{none:"",sm:"p-3",md:"p-5",lg:"p-8"}[c],e&&"h-full",f&&"hover:shadow-xl hover:-translate-y-1","interactive"===b&&"hover:border-mbnb-coral",a),...i,children:h}));h.displayName="Card";let i=f().forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,g.cn)("flex flex-col space-y-1.5",a),...b}));i.displayName="CardHeader";let j=f().forwardRef(({className:a,...b},c)=>(0,d.jsx)("h3",{ref:c,className:(0,g.cn)("text-xl font-semibold text-gray-dark leading-none tracking-tight",a),...b}));j.displayName="CardTitle",f().forwardRef(({className:a,...b},c)=>(0,d.jsx)("p",{ref:c,className:(0,g.cn)("text-sm text-gray-medium",a),...b})).displayName="CardDescription";let k=f().forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,g.cn)("",a),...b}));k.displayName="CardContent",f().forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,g.cn)("flex items-center",a),...b})).displayName="CardFooter"},41025:a=>{a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},50939:(a,b,c)=>{c.r(b),c.d(b,{GlobalError:()=>C.default,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(73653),e=c(97714),f=c(85250),g=c(37587),h=c(22369),i=c(1889),j=c(96232),k=c(22841),l=c(46537),m=c(46027),n=c(78559),o=c(75928),p=c(19374),q=c(65971),r=c(261),s=c(79898),t=c(32967),u=c(26713),v=c(40139),w=c(14248),x=c(59580),y=c(57749),z=c(53123),A=c(89745),B=c(86439),C=c(96133),D=c(18283),E=c(39818),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["trust-safety",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,2688)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/trust-safety/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,56035)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,74827)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/error.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,96133)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/global-error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,72993)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,15034,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,54693,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/trust-safety/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/trust-safety/page",pathname:"/trust-safety",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",relativeProjectDir:""});async function K(a,b,d){var F;let L="/trust-safety/page";"/index"===L&&(L="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await J.prepare(a,b,{srcPage:L,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(L),{isOnDemandRevalidate:ah}=O,ai=J.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(F=$.routes[ag]??$.dynamicRoutes[ag])?void 0:F.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===J.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&J.isDev&&(aB=aa),J.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...D,tree:G,pages:H,GlobalError:C.default,handler:K,routeModule:J,__next_app__:I};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:L,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=J.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:J,page:L,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),J.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!J.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===J.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await J.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await J.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!J.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&E.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:L,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},51974:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))})},56818:(a,b,c)=>{c.d(b,{cn:()=>f});var d=c(42168),e=c(20956);function f(...a){return(0,e.QP)((0,d.$)(a))}},63033:a=>{a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77133:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))})},86439:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external")},92176:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))})},96449:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(11110);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[4635,2922,1997,4367],()=>b(b.s=50939));module.exports=c})();
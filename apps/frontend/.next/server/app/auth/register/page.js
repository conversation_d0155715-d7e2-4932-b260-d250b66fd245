(()=>{var a={};a.id=983,a.ids=[983],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8647:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>o});var d=c(78157),e=c(31768),f=c(71159),g=c(94496),h=c.n(g),i=c(29508),j=c(97807),k=c(98839),l=c(82339);let m=[{value:"fr",label:"Fran\xe7ais"},{value:"ar",label:"العربية"},{value:"en",label:"English"},{value:"es",label:"<PERSON>spa\xf1ol"},{value:"zh",label:"中文"},{value:"ru",label:"Русский"},{value:"ar-ma",label:"الدارجة المغربية"}],n=["Marocaine","Fran\xe7aise","Espagnole","Allemande","Italienne","Britannique","Am\xe9ricaine","Canadienne","Belge","N\xe9erlandaise","Suisse","Russe","Chinoise","Japonaise","Autre"];function o(){let a=(0,f.useRouter)(),{registerToMbnb:b,isLoading:c,error:g,clearError:o}=(0,i.A)(),[p,q]=(0,e.useState)({email:"",password:"",firstName:"",lastName:"",phone:"",nationality:"",preferredLanguage:"fr",acceptTerms:!1,acceptPrivacy:!1,marketingConsent:!1}),[r,s]=(0,e.useState)(""),[t,u]=(0,e.useState)(!1),[v,w]=(0,e.useState)(!1),[x,y]=(0,e.useState)({}),z=(a,b)=>{q(c=>({...c,[a]:b})),x[a]&&y(b=>{let c={...b};return delete c[a],c}),g&&o()},A=async c=>{if(c.preventDefault(),!(()=>{let a={};return p.email?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(p.email)||(a.email="Adresse email invalide"):a.email="L'adresse email est requise",p.password?p.password.length<8?a.password="Le mot de passe doit contenir au moins 8 caract\xe8res":/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/.test(p.password)||(a.password="Le mot de passe doit contenir au moins une majuscule, une minuscule, un chiffre et un caract\xe8re sp\xe9cial"):a.password="Le mot de passe est requis",r?p.password!==r&&(a.confirmPassword="Les mots de passe ne correspondent pas"):a.confirmPassword="Veuillez confirmer votre mot de passe",(!p.firstName||p.firstName.trim().length<2)&&(a.firstName="Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),(!p.lastName||p.lastName.trim().length<2)&&(a.lastName="Le nom doit contenir au moins 2 caract\xe8res"),p.phone&&!/^(\+\d{1,3}[- ]?)?\d{8,15}$/.test(p.phone)&&(a.phone="Num\xe9ro de t\xe9l\xe9phone invalide"),p.acceptTerms||(a.acceptTerms="Vous devez accepter les conditions d'utilisation"),p.acceptPrivacy||(a.acceptPrivacy="Vous devez accepter la politique de confidentialit\xe9"),y(a),0===Object.keys(a).length})())return void l.oR.error("Veuillez corriger les erreurs du formulaire");try{await b(p),l.oR.success("Inscription r\xe9ussie - Bienvenue sur Mbnb!"),a.push("/auth/login?registered=true")}catch(b){let a=b instanceof Error?b.message:"Erreur lors de l'inscription";l.oR.error(a)}};return(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-mbnb-sky/5 via-white to-mbnb-teal/5 flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12",children:(0,d.jsxs)("div",{className:"max-w-lg w-full space-y-8",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"mx-auto h-24 w-24 rounded-full bg-mbnb-coral flex items-center justify-center shadow-lg",children:(0,d.jsx)("div",{className:"text-white text-4xl font-bold",children:"M"})}),(0,d.jsx)("h2",{className:"mt-6 text-center text-3xl font-bold text-mbnb-navy",children:"Rejoignez la communaut\xe9 Mbnb"}),(0,d.jsx)("p",{className:"mt-2 text-center text-sm text-mbnb-steel",children:"D\xe9couvrez et partagez le patrimoine authentique du Maroc"})]}),(0,d.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:A,children:[(0,d.jsx)("div",{className:"bg-white rounded-xl shadow-card p-8 border border-gray-100",children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-mbnb-navy mb-2",children:"Pr\xe9nom"}),(0,d.jsx)(k.A,{id:"firstName",name:"firstName",type:"text",autoComplete:"given-name",required:!0,className:`appearance-none relative block w-full px-4 py-3 border rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-mbnb-coral focus:border-mbnb-coral transition-colors duration-200 ${x.firstName?"border-red-300":"border-gray-200"}`,placeholder:"Votre pr\xe9nom",value:p.firstName,onChange:a=>z("firstName",a.target.value),disabled:c}),x.firstName&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:x.firstName})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-mbnb-navy mb-2",children:"Nom de famille"}),(0,d.jsx)(k.A,{id:"lastName",name:"lastName",type:"text",autoComplete:"family-name",required:!0,className:`appearance-none relative block w-full px-4 py-3 border rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-mbnb-coral focus:border-mbnb-coral transition-colors duration-200 ${x.lastName?"border-red-300":"border-gray-200"}`,placeholder:"Votre nom",value:p.lastName,onChange:a=>z("lastName",a.target.value),disabled:c}),x.lastName&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:x.lastName})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-mbnb-navy mb-2",children:"Adresse email"}),(0,d.jsx)(k.A,{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:`appearance-none relative block w-full px-4 py-3 border rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-mbnb-coral focus:border-mbnb-coral transition-colors duration-200 ${x.email?"border-red-300":"border-gray-200"}`,placeholder:"<EMAIL>",value:p.email,onChange:a=>z("email",a.target.value),disabled:c}),x.email&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:x.email})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-mbnb-navy mb-2",children:"T\xe9l\xe9phone (optionnel)"}),(0,d.jsx)(k.A,{id:"phone",name:"phone",type:"tel",autoComplete:"tel",className:`appearance-none relative block w-full px-4 py-3 border rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-mbnb-coral focus:border-mbnb-coral transition-colors duration-200 ${x.phone?"border-red-300":"border-gray-200"}`,placeholder:"+212 6XX XX XX XX",value:p.phone,onChange:a=>z("phone",a.target.value),disabled:c}),x.phone&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:x.phone})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"nationality",className:"block text-sm font-medium text-mbnb-navy mb-2",children:"Nationalit\xe9"}),(0,d.jsxs)("select",{id:"nationality",name:"nationality",className:"appearance-none relative block w-full px-4 py-3 border border-gray-200 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-mbnb-coral focus:border-mbnb-coral transition-colors duration-200 bg-white",value:p.nationality,onChange:a=>z("nationality",a.target.value),disabled:c,children:[(0,d.jsx)("option",{value:"",children:"S\xe9lectionnez"}),n.map(a=>(0,d.jsx)("option",{value:a,children:a},a))]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"language",className:"block text-sm font-medium text-mbnb-navy mb-2",children:"Langue pr\xe9f\xe9r\xe9e"}),(0,d.jsx)("select",{id:"language",name:"language",className:"appearance-none relative block w-full px-4 py-3 border border-gray-200 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-mbnb-coral focus:border-mbnb-coral transition-colors duration-200 bg-white",value:p.preferredLanguage,onChange:a=>z("preferredLanguage",a.target.value),disabled:c,children:m.map(a=>(0,d.jsx)("option",{value:a.value,children:a.label},a.value))})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-mbnb-navy mb-2",children:"Mot de passe"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(k.A,{id:"password",name:"password",type:t?"text":"password",autoComplete:"new-password",required:!0,className:`appearance-none relative block w-full px-4 py-3 pr-12 border rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-mbnb-coral focus:border-mbnb-coral transition-colors duration-200 ${x.password?"border-red-300":"border-gray-200"}`,placeholder:"Mot de passe",value:p.password,onChange:a=>z("password",a.target.value),disabled:c}),(0,d.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>u(!t),children:t?(0,d.jsx)("svg",{className:"h-5 w-5 text-mbnb-steel hover:text-mbnb-coral transition-colors cursor-pointer",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464m1.414 1.414L8.464 8.464m5.656 5.656L15.536 15.536m-1.414-1.414L15.536 15.536"})}):(0,d.jsxs)("svg",{className:"h-5 w-5 text-mbnb-steel hover:text-mbnb-coral transition-colors cursor-pointer",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})})]}),x.password&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:x.password})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-mbnb-navy mb-2",children:"Confirmer le mot de passe"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(k.A,{id:"confirmPassword",name:"confirmPassword",type:v?"text":"password",autoComplete:"new-password",required:!0,className:`appearance-none relative block w-full px-4 py-3 pr-12 border rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-mbnb-coral focus:border-mbnb-coral transition-colors duration-200 ${x.confirmPassword?"border-red-300":"border-gray-200"}`,placeholder:"Confirmer",value:r,onChange:a=>{s(a.target.value),x.confirmPassword&&y(a=>{let b={...a};return delete b.confirmPassword,b})},disabled:c}),(0,d.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>w(!v),children:v?(0,d.jsx)("svg",{className:"h-5 w-5 text-mbnb-steel hover:text-mbnb-coral transition-colors cursor-pointer",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464m1.414 1.414L8.464 8.464m5.656 5.656L15.536 15.536m-1.414-1.414L15.536 15.536"})}):(0,d.jsxs)("svg",{className:"h-5 w-5 text-mbnb-steel hover:text-mbnb-coral transition-colors cursor-pointer",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})})]}),x.confirmPassword&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:x.confirmPassword})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("input",{id:"acceptTerms",name:"acceptTerms",type:"checkbox",className:"h-4 w-4 text-mbnb-coral focus:ring-mbnb-coral border-gray-300 rounded transition-colors mt-1",checked:p.acceptTerms,onChange:a=>z("acceptTerms",a.target.checked),required:!0}),(0,d.jsxs)("label",{htmlFor:"acceptTerms",className:"ml-3 block text-sm text-mbnb-navy",children:["J'accepte les"," ",(0,d.jsx)(h(),{href:"/legal/terms",className:"font-medium text-mbnb-coral hover:text-mbnb-teal transition-colors",children:"conditions d'utilisation"})," ","de Mbnb"]})]}),x.acceptTerms&&(0,d.jsx)("p",{className:"ml-7 text-sm text-red-600",children:x.acceptTerms}),(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("input",{id:"acceptPrivacy",name:"acceptPrivacy",type:"checkbox",className:"h-4 w-4 text-mbnb-coral focus:ring-mbnb-coral border-gray-300 rounded transition-colors mt-1",checked:p.acceptPrivacy,onChange:a=>z("acceptPrivacy",a.target.checked),required:!0}),(0,d.jsxs)("label",{htmlFor:"acceptPrivacy",className:"ml-3 block text-sm text-mbnb-navy",children:["J'accepte la"," ",(0,d.jsx)(h(),{href:"/legal/privacy",className:"font-medium text-mbnb-coral hover:text-mbnb-teal transition-colors",children:"politique de confidentialit\xe9"})," ","et le traitement de mes donn\xe9es personnelles"]})]}),x.acceptPrivacy&&(0,d.jsx)("p",{className:"ml-7 text-sm text-red-600",children:x.acceptPrivacy}),(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("input",{id:"marketingConsent",name:"marketingConsent",type:"checkbox",className:"h-4 w-4 text-mbnb-coral focus:ring-mbnb-coral border-gray-300 rounded transition-colors mt-1",checked:p.marketingConsent,onChange:a=>z("marketingConsent",a.target.checked)}),(0,d.jsx)("label",{htmlFor:"marketingConsent",className:"ml-3 block text-sm text-mbnb-steel",children:"Je souhaite recevoir les offres sp\xe9ciales et actualit\xe9s Mbnb par email (optionnel)"})]})]}),g&&(0,d.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,d.jsx)("div",{className:"ml-3",children:(0,d.jsx)("p",{className:"text-sm text-red-700",children:g})})]})}),(0,d.jsx)("div",{children:(0,d.jsx)(j.A,{type:"submit",disabled:c,className:"group relative w-full flex justify-center py-3 px-4 border border-transparent text-white font-medium rounded-lg bg-mbnb-coral hover:bg-mbnb-coral-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-mbnb-coral transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-[1.02] active:scale-[0.98]",children:c?(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"animate-spin -ml-1 mr-3 h-5 w-5 border-2 border-white border-t-transparent rounded-full"}),"Inscription en cours..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("span",{className:"absolute left-0 inset-y-0 flex items-center pl-3",children:(0,d.jsx)("svg",{className:"h-5 w-5 text-white/80 group-hover:text-white transition-colors",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"})})}),"Cr\xe9er mon compte Mbnb"]})})})]})}),(0,d.jsx)("div",{className:"text-center",children:(0,d.jsxs)("p",{className:"text-sm text-mbnb-steel",children:["Vous avez d\xe9j\xe0 un compte Mbnb?"," ",(0,d.jsx)(h(),{href:"/auth/login",className:"font-medium text-mbnb-coral hover:text-mbnb-teal transition-colors",children:"Connectez-vous ici"})]})}),(0,d.jsx)("div",{className:"mt-8 bg-mbnb-teal/5 rounded-xl p-6 border border-mbnb-teal/10",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-mbnb-navy mb-2",children:"\uD83C\uDFDB️ Rejoignez la communaut\xe9 Mbnb"}),(0,d.jsx)("p",{className:"text-sm text-mbnb-steel mb-4",children:"D\xe9couvrez le patrimoine authentique du Maroc et partagez vos exp\xe9riences uniques"}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-xs text-mbnb-steel/80",children:[(0,d.jsxs)("div",{className:"space-y-1",children:[(0,d.jsx)("div",{children:"\uD83D\uDD4C Riads authentiques"}),(0,d.jsx)("div",{children:"\uD83C\uDFF0 Kasbahs historiques"}),(0,d.jsx)("div",{children:"\uD83C\uDFE1 Dars traditionnels"})]}),(0,d.jsxs)("div",{className:"space-y-1",children:[(0,d.jsx)("div",{children:"\uD83C\uDFA8 Artisanat local"}),(0,d.jsx)("div",{children:"\uD83C\uDF7D️ Gastronomie"}),(0,d.jsx)("div",{children:"⭐ TOP 100 activit\xe9s"})]})]})]})})]})]})})}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16910:(a,b,c)=>{Promise.resolve().then(c.bind(c,24573))},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24573:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(25459).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/auth/register/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/auth/register/page.tsx","default")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},51741:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.default,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(73653),e=c(97714),f=c(85250),g=c(37587),h=c(22369),i=c(1889),j=c(96232),k=c(22841),l=c(46537),m=c(46027),n=c(78559),o=c(75928),p=c(19374),q=c(65971),r=c(261),s=c(79898),t=c(32967),u=c(26713),v=c(40139),w=c(14248),x=c(59580),y=c(57749),z=c(53123),A=c(89745),B=c(86439),C=c(96133),D=c(18283),E=c(39818),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["auth",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,24573)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/auth/register/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,56035)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,74827)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/error.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,96133)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/global-error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,72993)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,15034,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,54693,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/auth/register/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/auth/register/page",pathname:"/auth/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",relativeProjectDir:""});async function K(a,b,d){var F;let L="/auth/register/page";"/index"===L&&(L="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await J.prepare(a,b,{srcPage:L,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(L),{isOnDemandRevalidate:ah}=O,ai=J.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(F=$.routes[ag]??$.dynamicRoutes[ag])?void 0:F.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===J.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&J.isDev&&(aB=aa),J.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...D,tree:G,pages:H,GlobalError:C.default,handler:K,routeModule:J,__next_app__:I};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:L,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=J.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:J,page:L,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),J.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!J.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===J.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await J.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await J.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!J.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&E.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:L,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},56662:(a,b,c)=>{Promise.resolve().then(c.bind(c,8647))},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4635,2922,3743,1797,4367,5246],()=>b(b.s=51741));module.exports=c})();
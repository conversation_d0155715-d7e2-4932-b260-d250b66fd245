"use strict";(()=>{var a={};a.id=4136,a.ids=[4136],a.modules={261:a=>{a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4226:(a,b,c)=>{c.r(b),c.d(b,{default:()=>s,metadata:()=>o});var d=c(5939),e=c(3498),f=c.n(e),g=c(45256),h=c(64140),i=c(43828);let j=(0,i.A)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),k=(0,i.A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),l=(0,i.A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]),m=(0,i.A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var n=c(77420);let o={title:"Espace Voyageur | Mbnb",description:"G\xe9rez vos r\xe9servations, favoris et profil voyageur sur Mbnb."},p=[{icon:g.A,title:"Mes r\xe9servations",description:"Consultez et g\xe9rez vos voyages",href:"/traveler/reservations",count:3},{icon:h.A,title:"Mes favoris",description:"Vos h\xe9bergements et activit\xe9s pr\xe9f\xe9r\xe9s",href:"/traveler/wishlists",count:12},{icon:j,title:"Messages",description:"Communiquez avec vos h\xf4tes",href:"/traveler/messages",count:2},{icon:k,title:"Mon profil",description:"Informations personnelles et pr\xe9f\xe9rences",href:"/traveler/profile"},{icon:l,title:"Paiements",description:"M\xe9thodes de paiement et factures",href:"/traveler/payments"},{icon:m,title:"Param\xe8tres",description:"Notifications et confidentialit\xe9",href:"/traveler/settings"}],q=[{id:1,title:"Riad Dar Zaman",location:"Marrakech",dates:"15-20 Mars 2024",status:"confirm\xe9",image:"/images/riad-1.jpg"},{id:2,title:"Villa Vue Oc\xe9an",location:"Essaouira",dates:"5-8 Avril 2024",status:"confirm\xe9",image:"/images/villa-1.jpg"}],r=[{type:"booking",title:"R\xe9servation confirm\xe9e",description:"Riad Dar Zaman \xe0 Marrakech",date:"Il y a 2 jours"},{type:"review",title:"Avis publi\xe9",description:"Villa Palmiers \xe0 Agadir - 5 \xe9toiles",date:"Il y a 1 semaine"},{type:"message",title:"Nouveau message",description:"Fatima vous a r\xe9pondu",date:"Il y a 2 semaines"}];function s(){return(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)("div",{className:"bg-gradient-to-r from-mbnb-teal to-mbnb-teal-dark text-white",children:(0,d.jsx)("div",{className:"container mx-auto px-4 py-12",children:(0,d.jsxs)("div",{className:"flex items-center gap-6",children:[(0,d.jsx)("div",{className:"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center text-3xl font-bold",children:"A"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"Bienvenue, Ahmed"}),(0,d.jsx)("p",{className:"text-white/90",children:"Membre depuis 2021 • Voyageur v\xe9rifi\xe9"})]})]})})}),(0,d.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,d.jsxs)("div",{className:"lg:col-span-2",children:[(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-8",children:p.map((a,b)=>{let c=a.icon;return(0,d.jsx)(f(),{href:a.href,className:"bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow",children:(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"flex items-start gap-4",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-mbnb-teal/10 rounded-lg flex items-center justify-center",children:(0,d.jsx)(c,{className:"w-6 h-6 text-mbnb-teal"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-bold text-gray-900",children:a.title}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:a.description})]})]}),a.count&&(0,d.jsx)("span",{className:"px-2 py-1 bg-mbnb-coral text-white text-xs rounded-full",children:a.count})]})},b)})}),(0,d.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Voyages \xe0 venir"}),(0,d.jsx)(f(),{href:"/traveler/reservations",className:"text-mbnb-coral hover:text-mbnb-coral-dark font-medium",children:"Voir tout →"})]}),(0,d.jsx)("div",{className:"space-y-4",children:q.map(a=>(0,d.jsxs)("div",{className:"flex items-center gap-4 p-4 border rounded-lg hover:bg-gray-50 transition-colors",children:[(0,d.jsx)("div",{className:"w-20 h-20 bg-gray-200 rounded-lg flex-shrink-0"}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h3",{className:"font-bold text-gray-900",children:a.title}),(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,d.jsx)(n.A,{className:"w-3 h-3 inline mr-1"}),a.location]}),(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,d.jsx)(g.A,{className:"w-3 h-3 inline mr-1"}),a.dates]})]}),(0,d.jsx)("span",{className:"px-3 py-1 bg-green-100 text-green-700 text-xs rounded-full",children:a.status})]},a.id))})]})]}),(0,d.jsxs)("div",{className:"lg:col-span-1",children:[(0,d.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm mb-6",children:[(0,d.jsx)("h3",{className:"font-bold text-gray-900 mb-4",children:"Compl\xe9tez votre profil"}),(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Progression"}),(0,d.jsx)("span",{className:"font-medium",children:"75%"})]}),(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,d.jsx)("div",{className:"bg-mbnb-teal h-2 rounded-full",style:{width:"75%"}})})]}),(0,d.jsxs)("ul",{className:"space-y-2",children:[(0,d.jsxs)("li",{className:"flex items-center text-sm",children:[(0,d.jsx)("span",{className:"w-5 h-5 bg-green-500 rounded-full flex items-center justify-center text-white text-xs mr-2",children:"✓"}),(0,d.jsx)("span",{className:"text-gray-600",children:"Email v\xe9rifi\xe9"})]}),(0,d.jsxs)("li",{className:"flex items-center text-sm",children:[(0,d.jsx)("span",{className:"w-5 h-5 bg-green-500 rounded-full flex items-center justify-center text-white text-xs mr-2",children:"✓"}),(0,d.jsx)("span",{className:"text-gray-600",children:"Photo de profil"})]}),(0,d.jsxs)("li",{className:"flex items-center text-sm",children:[(0,d.jsx)("span",{className:"w-5 h-5 bg-gray-300 rounded-full flex items-center justify-center text-white text-xs mr-2",children:"!"}),(0,d.jsx)("span",{className:"text-gray-600",children:"T\xe9l\xe9phone v\xe9rifi\xe9"})]})]}),(0,d.jsx)(f(),{href:"/traveler/profile",className:"block mt-4 text-center px-4 py-2 bg-mbnb-teal text-white rounded-lg hover:bg-mbnb-teal-dark transition-colors",children:"Compl\xe9ter le profil"})]}),(0,d.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm mb-6",children:[(0,d.jsx)("h3",{className:"font-bold text-gray-900 mb-4",children:"Activit\xe9 r\xe9cente"}),(0,d.jsx)("div",{className:"space-y-4",children:r.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-start gap-3",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-mbnb-coral rounded-full mt-2"}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-900",children:a.title}),(0,d.jsx)("p",{className:"text-xs text-gray-600",children:a.description}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:a.date})]})]},b))})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-mbnb-coral to-mbnb-coral-dark rounded-xl p-6 text-white",children:[(0,d.jsx)("h3",{className:"font-bold text-xl mb-2",children:"Programme Fid\xe9lit\xe9"}),(0,d.jsx)("p",{className:"text-white/90 text-sm mb-4",children:"Vous \xeates \xe0 2 r\xe9servations du statut Gold!"}),(0,d.jsxs)("div",{className:"bg-white/20 rounded-lg p-3 mb-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium",children:"Points accumul\xe9s"}),(0,d.jsx)("p",{className:"text-2xl font-bold",children:"1,250 pts"})]}),(0,d.jsx)(f(),{href:"/loyalty",className:"block text-center px-4 py-2 bg-white text-mbnb-coral rounded-lg hover:bg-gray-100 transition-colors font-medium",children:"D\xe9couvrir les avantages"})]})]})]})})]})}},10846:a=>{a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{a.exports=require("util")},29294:a=>{a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{a.exports=require("path")},41025:a=>{a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},43828:(a,b,c)=>{c.d(b,{A:()=>f});var d=c(11110),e={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let f=(a,b)=>{let c=(0,d.forwardRef)(({color:c="currentColor",size:f=24,strokeWidth:g=2,absoluteStrokeWidth:h,className:i="",children:j,...k},l)=>(0,d.createElement)("svg",{ref:l,...e,width:f,height:f,stroke:c,strokeWidth:h?24*Number(g)/Number(f):g,className:["lucide",`lucide-${a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim()}`,i].join(" "),...k},[...b.map(([a,b])=>(0,d.createElement)(a,b)),...Array.isArray(j)?j:[j]]));return c.displayName=`${a}`,c}},45256:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(43828).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},63033:a=>{a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64140:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(43828).A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},77420:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(43828).A)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},83383:(a,b,c)=>{c.r(b),c.d(b,{GlobalError:()=>C.default,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(73653),e=c(97714),f=c(85250),g=c(37587),h=c(22369),i=c(1889),j=c(96232),k=c(22841),l=c(46537),m=c(46027),n=c(78559),o=c(75928),p=c(19374),q=c(65971),r=c(261),s=c(79898),t=c(32967),u=c(26713),v=c(40139),w=c(14248),x=c(59580),y=c(57749),z=c(53123),A=c(89745),B=c(86439),C=c(96133),D=c(18283),E=c(39818),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["traveler",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,4226)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/traveler/page.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,41627)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/traveler/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,56035)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,74827)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/error.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,96133)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/global-error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,72993)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,15034,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,54693,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/traveler/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/traveler/page",pathname:"/traveler",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",relativeProjectDir:""});async function K(a,b,d){var F;let L="/traveler/page";"/index"===L&&(L="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await J.prepare(a,b,{srcPage:L,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(L),{isOnDemandRevalidate:ah}=O,ai=J.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(F=$.routes[ag]??$.dynamicRoutes[ag])?void 0:F.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===J.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&J.isDev&&(aB=aa),J.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...D,tree:G,pages:H,GlobalError:C.default,handler:K,routeModule:J,__next_app__:I};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:L,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=J.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:J,page:L,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),J.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!J.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===J.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await J.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await J.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!J.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&E.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:L,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},86439:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[4635,2922,3743,4367,8839],()=>b(b.s=83383));module.exports=c})();
(()=>{var a={};a.id=5399,a.ids=[5399],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4761:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>A});var d=c(78157),e=c(31768),f=c(71159),g=c(91712),h=c(94496),i=c.n(h),j=c(14196),k=c(75704),l=c(44173),m=c(66124),n=c(90025),o=c(74338),p=c(38500),q=c(86272),r=c(96542),s=c(40110),t=c(24971),u=c(51301);let v=e.forwardRef(function({title:a,titleId:b,...c},d){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:d,"aria-labelledby":b},c),a?e.createElement("title",{id:b},a):null,e.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003ZM12 8.25a.75.75 0 0 1 .75.75v3.75a.75.75 0 0 1-1.5 0V9a.75.75 0 0 1 .75-.75Zm0 8.25a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z",clipRule:"evenodd"}))});var w=c(86654),x=c(29508),y=c(21956),z=c(20318);function A(){(0,f.useRouter)();let{user:a,isAuthenticated:b}=(0,x.A)(),{data:c,isLoading:e,error:h}=(0,y.cL)(a?.mbnbId),{data:A,isLoading:B,error:C}=(0,y.rB)(),{data:D,isLoading:E,error:F}=(0,y.t$)(a?.mbnbId),{data:G,isLoading:H,error:I}=(0,y.Pe)(),J=(0,j.MZ)(),K=c?.filter(a=>(0,j.Io)(a.dates.checkIn)>J)||[],L=c?.filter(a=>(0,j.Io)(a.dates.checkOut)<=J).slice(0,5)||[];if(h||C||F||I)return(0,d.jsx)("div",{className:"min-h-screen bg-gray-50 p-6",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,d.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"text-red-800 font-semibold",children:"Erreur de chargement des donn\xe9es dashboard"}),(0,d.jsx)("p",{className:"text-red-600 text-sm mt-1",children:h?.message||C?.message||F?.message||I?.message})]})})});let M=a=>new Intl.NumberFormat("fr-MA",{style:"currency",currency:"MAD"}).format(a);return e||B||E||H?(0,d.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-mbnb-teal"})}):(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)("header",{className:"bg-white border-b border-gray-200",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,d.jsx)("div",{className:"flex items-center",children:(0,d.jsxs)("h1",{className:"text-2xl font-bold text-mbnb-teal",children:["Bonjour, ",a?.profile?.firstName,"!"]})}),(0,d.jsx)("div",{className:"flex items-center space-x-4",children:(0,d.jsxs)(i(),{href:"/search",className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-mbnb-coral hover:bg-mbnb-coral-dark",children:[(0,d.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Rechercher"]})})]})})}),(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,d.jsxs)(i(),{href:"/search",className:"bg-gradient-to-r from-mbnb-coral to-mbnb-coral-dark p-6 rounded-lg text-white hover:shadow-lg transition-shadow",children:[(0,d.jsx)(m.A,{className:"h-8 w-8 mb-3"}),(0,d.jsx)("h3",{className:"text-lg font-semibold",children:"Nouvelle recherche"}),(0,d.jsx)("p",{className:"text-sm opacity-90",children:"Trouvez votre prochaine destination"})]}),(0,d.jsxs)(i(),{href:"/traveler/discover",className:"bg-mbnb-teal p-6 rounded-lg text-white hover:shadow-lg transition-shadow",children:[(0,d.jsx)(n.A,{className:"h-8 w-8 mb-3"}),(0,d.jsx)("h3",{className:"text-lg font-semibold",children:"D\xe9couvrir"}),(0,d.jsx)("p",{className:"text-sm opacity-90",children:"Explorez les tr\xe9sors du Maroc"})]}),(0,d.jsxs)(i(),{href:"/traveler/activities",className:"bg-mbnb-navy p-6 rounded-lg text-white hover:shadow-lg transition-shadow",children:[(0,d.jsx)(o.A,{className:"h-8 w-8 mb-3"}),(0,d.jsx)("h3",{className:"text-lg font-semibold",children:"TOP 100 Activit\xe9s"}),(0,d.jsx)("p",{className:"text-sm opacity-90",children:"Exp\xe9riences authentiques"})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,d.jsxs)("div",{className:"lg:col-span-2 space-y-8",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,d.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Voyages \xe0 venir"}),(0,d.jsx)(i(),{href:"/traveler/trips",className:"text-sm text-mbnb-teal hover:underline",children:"Voir tous"})]})}),(0,d.jsx)("div",{className:"divide-y divide-gray-200",children:0===K.length?(0,d.jsxs)("div",{className:"p-8 text-center text-gray-500",children:[(0,d.jsx)(p.A,{className:"h-12 w-12 mx-auto mb-3 text-gray-300"}),(0,d.jsx)("p",{children:"Aucun voyage pr\xe9vu"}),(0,d.jsx)(i(),{href:"/search",className:"mt-3 inline-block text-mbnb-teal hover:underline",children:"Planifier votre prochain voyage"})]}):K.map(a=>(0,d.jsxs)(i(),{href:`/reservation/${a.mbnbId}`,className:"p-6 hover:bg-gray-50 flex items-center space-x-4",children:[(0,d.jsx)(g.default,{src:"/placeholder-property.jpg",alt:`Propri\xe9t\xe9 ${a.propertyId}`,width:80,height:80,className:"rounded-lg object-cover"}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,d.jsxs)("h3",{className:"font-semibold text-gray-900",children:["Propri\xe9t\xe9 ",a.propertyId]}),(a=>{let b=(0,j.MZ)(),c=(0,j.Io)(a.dates.checkIn),e=(0,j.Io)(a.dates.checkOut);return a.status===z.cg.CANCELLED?(0,d.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:[(0,d.jsx)(v,{className:"h-3 w-3 mr-1"}),"Annul\xe9"]}):b<c?(0,d.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:[(0,d.jsx)(l.A,{className:"h-3 w-3 mr-1"}),"\xc0 venir"]}):b>=c&&b<=e?(0,d.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[(0,d.jsx)(w.A,{className:"h-3 w-3 mr-1"}),"En cours"]}):(0,d.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:[(0,d.jsx)(w.A,{className:"h-3 w-3 mr-1"}),"Termin\xe9"]})})(a)]}),(0,d.jsxs)("div",{className:"flex items-center text-sm text-gray-600 mb-1",children:[(0,d.jsx)(q.A,{className:"h-4 w-4 mr-1"}),"Localisation \xe0 charger"]}),(0,d.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,d.jsx)(p.A,{className:"h-4 w-4 mr-1"}),(0,k.Yq)(a.dates.checkIn,"short")," -",(0,k.Yq)(a.dates.checkOut,"short")]})]}),(0,d.jsx)(r.A,{className:"h-5 w-5 text-gray-400"})]},a.mbnbId))})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,d.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Voyages r\xe9cents"})}),(0,d.jsx)("div",{className:"divide-y divide-gray-200",children:0===L.length?(0,d.jsxs)("div",{className:"p-8 text-center text-gray-500",children:[(0,d.jsx)(s.A,{className:"h-12 w-12 mx-auto mb-3 text-gray-300"}),(0,d.jsx)("p",{children:"Aucun voyage r\xe9cent"})]}):L.map(a=>(0,d.jsxs)("div",{className:"p-4 flex items-center space-x-4",children:[(0,d.jsx)(g.default,{src:"/placeholder-property.jpg",alt:`Propri\xe9t\xe9 ${a.propertyId}`,width:60,height:60,className:"rounded-lg object-cover"}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("h4",{className:"font-medium text-gray-900",children:["Propri\xe9t\xe9 ",a.propertyId]}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:(0,k.Yq)(a.dates.checkOut,"short")})]}),(0,d.jsx)(i(),{href:`/property/${a.propertyId}/review`,className:"text-sm text-mbnb-teal hover:underline",children:"Laisser un avis"})]},a.mbnbId))})]})]}),(0,d.jsxs)("div",{className:"space-y-8",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,d.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Favoris"}),(0,d.jsx)(i(),{href:"/traveler/favorites",className:"text-sm text-mbnb-teal hover:underline",children:"Voir tous"})]})}),(0,d.jsx)("div",{className:"p-4 space-y-4",children:A&&0!==A.length?A.slice(0,6).map(a=>(0,d.jsxs)(i(),{href:`/property/${a.mbnbId}`,className:"flex items-center space-x-3 hover:bg-gray-50 p-2 rounded-lg",children:[(0,d.jsx)(g.default,{src:("string"==typeof a.media?.images?.[0]?a.media.images[0]:a.media?.images?.[0]?.url)||"/placeholder-property.jpg",alt:a.title,width:50,height:50,className:"rounded-lg object-cover"}),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-gray-900 truncate",children:a.title}),(0,d.jsxs)("p",{className:"text-xs text-gray-600",children:[M(a.price||0),"/nuit"]})]})]},a.mbnbId)):(0,d.jsxs)("div",{className:"text-center text-gray-500 py-6",children:[(0,d.jsx)(t.A,{className:"h-8 w-8 mx-auto mb-2 text-gray-300"}),(0,d.jsx)("p",{className:"text-sm",children:"Aucun favori"})]})})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,d.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Recommand\xe9 pour vous"})}),(0,d.jsx)("div",{className:"p-4 space-y-4",children:D&&0!==D.length?D.slice(0,4).map(a=>(0,d.jsxs)(i(),{href:`/property/${a.mbnbId}`,className:"block hover:bg-gray-50 p-2 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,d.jsx)(g.default,{src:("string"==typeof a.media?.images?.[0]?a.media.images[0]:a.media?.images?.[0]?.url)||"/placeholder-property.jpg",alt:a.title,width:50,height:50,className:"rounded-lg object-cover"}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-gray-900 line-clamp-1",children:a.title}),(0,d.jsxs)("div",{className:"flex items-center text-xs text-gray-600",children:[(0,d.jsx)(q.A,{className:"h-3 w-3 mr-1"}),a.location.city]})]})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center text-xs",children:[(0,d.jsxs)("span",{className:"font-medium",children:[M(a.price||0),"/nuit"]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(u.A,{className:"h-3 w-3 text-yellow-400 mr-1"}),"4.8"]})]})]},a.mbnbId)):(0,d.jsxs)("div",{className:"text-center text-gray-500 py-6",children:[(0,d.jsx)(o.A,{className:"h-8 w-8 mx-auto mb-2 text-gray-300"}),(0,d.jsx)("p",{className:"text-sm",children:"Aucune recommandation"})]})})]}),(0,d.jsx)("div",{className:"bg-mbnb-teal rounded-lg shadow text-white",children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold mb-3",children:"TOP 100 Activit\xe9s"}),(0,d.jsx)("div",{className:"space-y-3",children:G&&0!==G.length?G.slice(0,3).map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"bg-white bg-opacity-20 rounded-full w-6 h-6 flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-xs font-bold",children:b+1})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"text-sm font-medium",children:a.activity.name}),(0,d.jsx)("p",{className:"text-xs opacity-75",children:a.activity.location.city})]})]},a.activity.activityId)):(0,d.jsxs)("div",{className:"text-center py-6",children:[(0,d.jsx)(o.A,{className:"h-8 w-8 mx-auto mb-2 text-white opacity-75"}),(0,d.jsx)("p",{className:"text-sm opacity-75",children:"Aucune activit\xe9 disponible"})]})}),(0,d.jsx)(i(),{href:"/traveler/activities",className:"mt-4 inline-block text-sm bg-white bg-opacity-20 px-3 py-1 rounded-full hover:bg-opacity-30 transition-colors",children:"Voir toutes les activit\xe9s →"})]})})]})]})]})]})}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20318:(a,b,c)=>{"use strict";c.d(b,{$p:()=>k,U8:()=>e,Zg:()=>d,Zv:()=>i,a0:()=>f,bQ:()=>g,cg:()=>j,eo:()=>h,k5:()=>l,y$:()=>m});var d=function(a){return a.CMI="CMI",a.WAFACASH="WAFACASH",a.CASH_PLUS="CASH_PLUS",a.ALI_PAY="ALI_PAY",a.GOOGLE_PAY="GOOGLE_PAY",a.APPLE_PAY="APPLE_PAY",a}({}),e=function(a){return a.MAD="MAD",a.EUR="EUR",a.USD="USD",a.GBP="GBP",a.CAD="CAD",a.AUD="AUD",a.CNY="CNY",a.RUB="RUB",a}({}),f=function(a){return a.RIAD_AUTHENTIQUE="RIAD_AUTHENTIQUE",a.VILLA_COLONIALE="VILLA_COLONIALE",a.MAISON_TRADITIONNELLE="MAISON_TRADITIONNELLE",a.APPARTEMENT_MODERNE="APPARTEMENT_MODERNE",a.KASBAH_HISTORIQUE="KASBAH_HISTORIQUE",a.DAR_FAMILIAL="DAR_FAMILIAL",a}({}),g=function(a){return a.CULTURAL_HERITAGE="CULTURAL_HERITAGE",a.ADVENTURE_SPORTS="ADVENTURE_SPORTS",a.CULINARY_EXPERIENCE="CULINARY_EXPERIENCE",a.DESERT_EXPEDITION="DESERT_EXPEDITION",a.MOUNTAIN_TREKKING="MOUNTAIN_TREKKING",a.COASTAL_ACTIVITIES="COASTAL_ACTIVITIES",a.ARTISAN_WORKSHOPS="ARTISAN_WORKSHOPS",a.SPIRITUAL_WELLNESS="SPIRITUAL_WELLNESS",a}({}),h=function(a){return a.EXPLORATEUR="EXPLORATEUR",a.AVENTURIER="AVENTURIER",a.AMBASSADEUR="AMBASSADEUR",a.LEGENDE="LEGENDE",a}({}),i=function(a){return a.DISCOUNT_FUTURE_BOOKING="DISCOUNT_FUTURE_BOOKING",a.UPGRADE="UPGRADE",a.EXPERIENCE="EXPERIENCE",a.CASHBACK="CASHBACK",a}({}),j=function(a){return a.PENDING="PENDING",a.CONFIRMED="CONFIRMED",a.CANCELLED="CANCELLED",a.COMPLETED="COMPLETED",a.DISPUTED="DISPUTED",a.CHECK_IN="CHECK_IN",a.CHECK_OUT="CHECK_OUT",a.REFUNDED="REFUNDED",a.NO_SHOW="NO_SHOW",a}({}),k=function(a){return a.COMPLETED="completed",a.PENDING="pending",a.PROCESSING="processing",a.FAILED="failed",a}({}),l=function(a){return a.PHASE_1="PHASE_1",a.PHASE_2="PHASE_2",a.PHASE_3="PHASE_3",a}({});let m={MAX_CUMULATIVE_DISCOUNT:.25,MIN_AMOUNT_THRESHOLD:500,ACTIVITIES_EXCLUDED:!0,COMMISSION_RATE_ACTIVITIES:.18,FAMILY_MIN_TRAVELERS:4,LONG_STAY_WEEKLY_NIGHTS:7,LONG_STAY_MONTHLY_NIGHTS:28,LONG_STAY_QUARTERLY_NIGHTS:84,ATLAS_REGIONS:["fes-meknes","draa-tafilalet","beni-mellal-khenifra"]}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33416:(a,b,c)=>{Promise.resolve().then(c.bind(c,89483))},33873:a=>{"use strict";a.exports=require("path")},40110:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.827 6.175A2.31 2.31 0 0 1 5.186 7.23c-.38.054-.757.112-1.134.175C2.999 7.58 2.25 8.507 2.25 9.574V18a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9.574c0-1.067-.75-1.994-1.802-2.169a47.865 47.865 0 0 0-1.134-.175 2.31 2.31 0 0 1-1.64-1.055l-.822-1.316a2.192 2.192 0 0 0-1.736-1.039 48.774 48.774 0 0 0-5.232 0 2.192 2.192 0 0 0-1.736 1.039l-.821 1.316Z"}),d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.5 12.75a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0ZM18.75 10.5h.008v.008h-.008V10.5Z"}))})},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},44173:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66124:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))})},74338:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"}))})},75704:(a,b,c)=>{"use strict";c.d(b,{Yq:()=>h,cn:()=>g});var d=c(79390),e=c(25442),f=c(14196);function g(...a){return(0,e.QP)((0,d.$)(a))}function h(a,b="short"){let c="string"==typeof a?(0,f.Io)(a):a;return"short"===b?new Intl.DateTimeFormat("fr-MA",{day:"numeric",month:"short",year:"numeric"}).format(c):new Intl.DateTimeFormat("fr-MA",{weekday:"long",day:"numeric",month:"long",year:"numeric"}).format(c)}},83961:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.default,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(73653),e=c(97714),f=c(85250),g=c(37587),h=c(22369),i=c(1889),j=c(96232),k=c(22841),l=c(46537),m=c(46027),n=c(78559),o=c(75928),p=c(19374),q=c(65971),r=c(261),s=c(79898),t=c(32967),u=c(26713),v=c(40139),w=c(14248),x=c(59580),y=c(57749),z=c(53123),A=c(89745),B=c(86439),C=c(96133),D=c(18283),E=c(39818),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["traveler",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,89483)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/traveler/dashboard/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,41627)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/traveler/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,56035)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,74827)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/error.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,96133)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/global-error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,72993)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,15034,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,54693,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/traveler/dashboard/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/traveler/dashboard/page",pathname:"/traveler/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",relativeProjectDir:""});async function K(a,b,d){var F;let L="/traveler/dashboard/page";"/index"===L&&(L="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await J.prepare(a,b,{srcPage:L,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(L),{isOnDemandRevalidate:ah}=O,ai=J.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(F=$.routes[ag]??$.dynamicRoutes[ag])?void 0:F.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===J.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&J.isDev&&(aB=aa),J.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...D,tree:G,pages:H,GlobalError:C.default,handler:K,routeModule:J,__next_app__:I};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:L,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=J.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:J,page:L,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),J.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!J.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===J.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await J.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await J.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!J.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&E.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:L,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},86272:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}),d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"}))})},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86654:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{fillRule:"evenodd",d:"M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z",clipRule:"evenodd"}))})},89483:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(25459).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/traveler/dashboard/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/traveler/dashboard/page.tsx","default")},96542:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))})},96968:(a,b,c)=>{Promise.resolve().then(c.bind(c,4761))}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4635,2922,4380,3743,1712,1797,4367,1956,8839],()=>b(b.s=83961));module.exports=c})();
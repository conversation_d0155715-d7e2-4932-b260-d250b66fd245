(()=>{var a={};a.id=6636,a.ids=[6636],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3013:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4768).A)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4768:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(31768),e={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let f=(a,b)=>{let c=(0,d.forwardRef)(({color:c="currentColor",size:f=24,strokeWidth:g=2,absoluteStrokeWidth:h,className:i="",children:j,...k},l)=>(0,d.createElement)("svg",{ref:l,...e,width:f,height:f,stroke:c,strokeWidth:h?24*Number(g)/Number(f):g,className:["lucide",`lucide-${a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim()}`,i].join(" "),...k},[...b.map(([a,b])=>(0,d.createElement)(a,b)),...Array.isArray(j)?j:[j]]));return c.displayName=`${a}`,c}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19290:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(25459).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/profile/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/profile/page.tsx","default")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29508:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(52661),e=c(7904),f=c(62188);let g=(0,d.vt)()((0,e.Zr)((0,f.D)((a,b)=>({user:null,token:null,refreshToken:null,isAuthenticated:!1,isLoading:!1,error:null,heritageInterests:[],activityPreferences:[],preferredLanguage:"fr",preferredCurrency:"MAD",loginToMbnb:async b=>{a(a=>{a.isLoading=!0,a.error=null});try{let c=await fetch("http://localhost:3001/api/v1/auth/login",{method:"POST",headers:{"Content-Type":"application/json","X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(b)});if(!c.ok){let a=await c.json();throw Error(a.message||"Authentification Mbnb \xe9chou\xe9e")}let d=await c.json();a(a=>{a.user=d.user,a.token=d.token,a.refreshToken=d.refreshToken||null,a.isAuthenticated=!0,a.isLoading=!1,a.preferredLanguage=d.user.profile.preferredLanguage,a.preferredCurrency=d.user.profile.preferredCurrency})}catch(c){let b=c instanceof Error?c.message:"Erreur de connexion Mbnb";throw a(a=>{a.error=b,a.isLoading=!1}),c}},registerToMbnb:async b=>{a(a=>{a.isLoading=!0,a.error=null});try{let c=await fetch("http://localhost:3001/api/v1/auth/register",{method:"POST",headers:{"Content-Type":"application/json","X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(b)});if(!c.ok){let a=await c.json();throw Error(a.message||"Inscription Mbnb \xe9chou\xe9e")}let d=await c.json();a(a=>{a.user=d.user,a.token=d.token,a.refreshToken=d.refreshToken||null,a.isAuthenticated=!0,a.isLoading=!1,a.preferredLanguage=d.user.profile.preferredLanguage,a.preferredCurrency=d.user.profile.preferredCurrency})}catch(c){let b=c instanceof Error?c.message:"Erreur d'inscription Mbnb";throw a(a=>{a.error=b,a.isLoading=!1}),c}},logoutFromMbnb:()=>{a(a=>{a.user=null,a.token=null,a.refreshToken=null,a.isAuthenticated=!1,a.error=null,a.heritageInterests=[],a.activityPreferences=[],a.preferredLanguage="fr",a.preferredCurrency="MAD"}),localStorage.removeItem("mbnb-auth-storage"),window.location.href="/"},refreshMbnbToken:async()=>{let c=b().refreshToken;if(!c)return void b().logoutFromMbnb();try{let b=await fetch("http://localhost:3001/api/v1/auth/refresh",{method:"POST",headers:{"Content-Type":"application/json","X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify({refreshToken:c})});if(!b.ok)throw Error("Refresh token Mbnb expir\xe9");let d=await b.json();a(a=>{a.token=d.token,a.refreshToken=d.refreshToken})}catch(a){b().logoutFromMbnb()}},updateMbnbProfile:async c=>{a(a=>{a.isLoading=!0,a.error=null});try{let d=await fetch("http://localhost:3001/api/v1/users/profile",{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${b().token}`,"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(c)});if(!d.ok){let a=await d.json();throw Error(a.message||"Mise \xe0 jour profil Mbnb \xe9chou\xe9e")}let e=await d.json();a(a=>{a.user=e,a.isLoading=!1,a.preferredLanguage=e.profile.preferredLanguage,a.preferredCurrency=e.profile.preferredCurrency})}catch(c){let b=c instanceof Error?c.message:"Erreur mise \xe0 jour profil Mbnb";throw a(a=>{a.error=b,a.isLoading=!1}),c}},updateMbnbPreferences:async c=>{a(a=>{a.isLoading=!0,a.error=null});try{let d=await fetch("http://localhost:3001/api/v1/users/preferences",{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${b().token}`,"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(c)});if(!d.ok){let a=await d.json();throw Error(a.message||"Mise \xe0 jour pr\xe9f\xe9rences Mbnb \xe9chou\xe9e")}a(a=>{c.heritageInterests&&(a.heritageInterests=c.heritageInterests),c.activityPreferences&&(a.activityPreferences=c.activityPreferences),c.preferredLanguage&&(a.preferredLanguage=c.preferredLanguage),c.preferredCurrency&&(a.preferredCurrency=c.preferredCurrency),a.isLoading=!1})}catch(c){let b=c instanceof Error?c.message:"Erreur mise \xe0 jour pr\xe9f\xe9rences Mbnb";throw a(a=>{a.error=b,a.isLoading=!1}),c}},verifyMbnbEmail:async c=>{let d=await fetch("http://localhost:3001/api/v1/auth/verify-email",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${b().token}`,"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify({code:c})});if(!d.ok)throw Error((await d.json()).message||"V\xe9rification email Mbnb \xe9chou\xe9e");a(a=>{a.user&&(a.user.verification.emailVerified=!0)})},verifyMbnbPhone:async c=>{let d=await fetch("http://localhost:3001/api/v1/auth/verify-phone",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${b().token}`,"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify({code:c})});if(!d.ok)throw Error((await d.json()).message||"V\xe9rification t\xe9l\xe9phone Mbnb \xe9chou\xe9e");a(a=>{a.user&&(a.user.verification.phoneVerified=!0)})},clearError:()=>a(a=>{a.error=null})})),{name:"mbnb-auth-storage",storage:(0,e.KU)(()=>localStorage),partialize:a=>({user:a.user,token:a.token,refreshToken:a.refreshToken,isAuthenticated:a.isAuthenticated,heritageInterests:a.heritageInterests,activityPreferences:a.activityPreferences,preferredLanguage:a.preferredLanguage,preferredCurrency:a.preferredCurrency})}))},32091:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.default,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(73653),e=c(97714),f=c(85250),g=c(37587),h=c(22369),i=c(1889),j=c(96232),k=c(22841),l=c(46537),m=c(46027),n=c(78559),o=c(75928),p=c(19374),q=c(65971),r=c(261),s=c(79898),t=c(32967),u=c(26713),v=c(40139),w=c(14248),x=c(59580),y=c(57749),z=c(53123),A=c(89745),B=c(86439),C=c(96133),D=c(18283),E=c(39818),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,19290)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/profile/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,56035)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,74827)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/error.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,96133)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/global-error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,72993)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,15034,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,54693,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/profile/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",relativeProjectDir:""});async function K(a,b,d){var F;let L="/profile/page";"/index"===L&&(L="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await J.prepare(a,b,{srcPage:L,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(L),{isOnDemandRevalidate:ah}=O,ai=J.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(F=$.routes[ag]??$.dynamicRoutes[ag])?void 0:F.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===J.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&J.isDev&&(aB=aa),J.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...D,tree:G,pages:H,GlobalError:C.default,handler:K,routeModule:J,__next_app__:I};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:L,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=J.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:J,page:L,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),J.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!J.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===J.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await J.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await J.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!J.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&E.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:L,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},33619:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>w});var d=c(78157),e=c(31768),f=c(32315),g=c(14380),h=c(86539),i=c(29508),j=c(4768);let k=(0,j.A)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);var l=c(42028);let m=(0,j.A)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),n=(0,j.A)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]),o=(0,j.A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),p=(0,j.A)("Pen",[["path",{d:"M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z",key:"5qss01"}]]);var q=c(98551),r=c(57465),s=c(51383),t=c(53574),u=c(3013),v=c(75704);function w(){let a=(0,f.jE)(),{user:b}=(0,i.A)(),[c,j]=(0,e.useState)(!1),[w,x]=(0,e.useState)({}),[y,z]=(0,e.useState)("profile"),{data:A,isLoading:B}=(0,g.I)({queryKey:["user-profile",b?.mbnbId],queryFn:async()=>{let a=await fetch("/api/v1/user/profile",{headers:{Authorization:`Bearer ${localStorage.getItem("mbnb-token")}`}});if(!a.ok)throw Error("Failed to fetch profile");return a.json()},enabled:!!b?.mbnbId}),C=(0,h.n)({mutationFn:async a=>{let b=await fetch("/api/v1/user/profile",{method:"PATCH",headers:{Authorization:`Bearer ${localStorage.getItem("mbnb-token")}`,"Content-Type":"application/json"},body:JSON.stringify(a)});if(!b.ok)throw Error("Failed to update profile");return b.json()},onSuccess:()=>{j(!1),a.invalidateQueries({queryKey:["user-profile"]})}}),D=(0,h.n)({mutationFn:async a=>{let b=new FormData;b.append("avatar",a);let c=await fetch("/api/v1/user/avatar",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("mbnb-token")}`},body:b});if(!c.ok)throw Error("Failed to upload avatar");return c.json()},onSuccess:()=>{a.invalidateQueries({queryKey:["user-profile"]})}}),E=a=>new Intl.NumberFormat("fr-MA",{style:"currency",currency:"MAD"}).format(a);return B||!A?(0,d.jsx)("div",{className:"min-h-screen bg-neutral-50 flex items-center justify-center",children:(0,d.jsx)("div",{className:"animate-pulse text-mbnb-coral",children:"Chargement du profil..."})}):(0,d.jsxs)("div",{className:"min-h-screen bg-neutral-50",children:[(0,d.jsxs)("div",{className:"relative h-64 bg-mbnb-navy",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,d.jsx)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full flex items-end pb-8",children:(0,d.jsxs)("div",{className:"flex items-end gap-6",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"w-32 h-32 rounded-full border-4 border-white overflow-hidden bg-white",children:A.avatar?(0,d.jsx)("img",{src:A.avatar,alt:`${A.firstName} ${A.lastName}`,className:"w-full h-full object-cover"}):(0,d.jsx)("div",{className:"w-full h-full flex items-center justify-center bg-mbnb-coral",children:(0,d.jsxs)("span",{className:"text-3xl font-bold text-white",children:[A.firstName[0],A.lastName[0]]})})}),(0,d.jsxs)("label",{className:"absolute bottom-0 right-0 p-2 bg-white rounded-full shadow-lg cursor-pointer hover:shadow-xl transition-shadow",children:[(0,d.jsx)(k,{className:"w-5 h-5 text-mbnb-coral"}),(0,d.jsx)("input",{type:"file",accept:"image/*",onChange:a=>{let b=a.target.files?.[0];b&&D.mutate(b)},className:"hidden"})]})]}),(0,d.jsxs)("div",{className:"pb-2",children:[(0,d.jsxs)("h1",{className:"text-3xl font-bold text-white flex items-center gap-2",children:[A.firstName," ",A.lastName,A.isSuperHost&&(0,d.jsxs)("span",{className:"px-2 py-1 bg-mbnb-coral text-white text-sm rounded-full flex items-center gap-1",children:[(0,d.jsx)(l.A,{className:"w-4 h-4",fill:"currentColor"}),"SuperHost"]})]}),(0,d.jsx)("p",{className:"text-white/90 mt-1",children:A.bio||"Membre Mbnb"}),(0,d.jsxs)("div",{className:"flex items-center gap-4 mt-2",children:[(0,d.jsxs)("span",{className:"text-white/80 text-sm",children:["Membre depuis ",(0,v.Yq)(A.joinedDate,"short")]}),A.location&&(0,d.jsxs)("span",{className:"text-white/80 text-sm flex items-center gap-1",children:[(0,d.jsx)(m,{className:"w-4 h-4"}),A.location]})]})]})]})})]}),(0,d.jsx)("div",{className:"bg-white border-b border-neutral-200",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsx)("div",{className:"flex gap-8",children:["profile","security","preferences","loyalty"].map(a=>(0,d.jsxs)("button",{onClick:()=>z(a),className:`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${y===a?"border-mbnb-coral text-mbnb-coral":"border-transparent text-neutral-500 hover:text-neutral-700"}`,children:["profile"===a&&"Profil","security"===a&&"S\xe9curit\xe9","preferences"===a&&"Pr\xe9f\xe9rences","loyalty"===a&&"Fid\xe9lit\xe9"]},a))})})}),(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:["profile"===y&&(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,d.jsxs)("div",{className:"lg:col-span-2",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-card p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-mbnb-navy",children:"Informations personnelles"}),c?(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("button",{onClick:()=>{C.mutate(w)},className:"px-4 py-2 bg-mbnb-coral text-white rounded-lg hover:bg-mbnb-coral-dark",children:(0,d.jsx)(n,{className:"w-4 h-4"})}),(0,d.jsx)("button",{onClick:()=>{j(!1),x({})},className:"px-4 py-2 border border-neutral-300 rounded-lg hover:bg-neutral-50",children:(0,d.jsx)(o,{className:"w-4 h-4"})})]}):(0,d.jsxs)("button",{onClick:()=>{j(!0),x(A)},className:"px-4 py-2 border border-neutral-300 rounded-lg hover:bg-neutral-50 flex items-center gap-2",children:[(0,d.jsx)(p,{className:"w-4 h-4"}),"Modifier"]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-1",children:"Pr\xe9nom"}),c?(0,d.jsx)("input",{type:"text",value:w.firstName||"",onChange:a=>x({...w,firstName:a.target.value}),className:"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral"}):(0,d.jsx)("p",{className:"text-neutral-900",children:A.firstName})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-1",children:"Nom"}),c?(0,d.jsx)("input",{type:"text",value:w.lastName||"",onChange:a=>x({...w,lastName:a.target.value}),className:"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral"}):(0,d.jsx)("p",{className:"text-neutral-900",children:A.lastName})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-1",children:"Email"}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("p",{className:"text-neutral-900",children:A.email}),A.emailVerified?(0,d.jsx)(q.A,{className:"w-4 h-4 text-mbnb-teal-dark"}):(0,d.jsx)(r.A,{className:"w-4 h-4 text-warning-500"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-1",children:"T\xe9l\xe9phone"}),c?(0,d.jsx)("input",{type:"tel",value:w.phone||"",onChange:a=>x({...w,phone:a.target.value}),className:"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral"}):(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("p",{className:"text-neutral-900",children:A.phone||"Non renseign\xe9"}),A.phone&&A.phoneVerified&&(0,d.jsx)(q.A,{className:"w-4 h-4 text-mbnb-teal-dark"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-1",children:"Localisation"}),c?(0,d.jsx)("input",{type:"text",value:w.location||"",onChange:a=>x({...w,location:a.target.value}),className:"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral"}):(0,d.jsx)("p",{className:"text-neutral-900",children:A.location||"Non renseign\xe9"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-1",children:"Langue pr\xe9f\xe9r\xe9e"}),c?(0,d.jsxs)("select",{value:w.preferredLanguage||"FR",onChange:a=>x({...w,preferredLanguage:a.target.value}),className:"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral",children:[(0,d.jsx)("option",{value:"FR",children:"Fran\xe7ais"}),(0,d.jsx)("option",{value:"AR",children:"العربية"}),(0,d.jsx)("option",{value:"AR-MA",children:"الدارجة"}),(0,d.jsx)("option",{value:"EN",children:"English"}),(0,d.jsx)("option",{value:"ES",children:"Espa\xf1ol"}),(0,d.jsx)("option",{value:"ZH",children:"中文"}),(0,d.jsx)("option",{value:"RU",children:"Русский"})]}):(0,d.jsx)("p",{className:"text-neutral-900",children:A.preferredLanguage})]}),(0,d.jsxs)("div",{className:"md:col-span-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-1",children:"Bio"}),c?(0,d.jsx)("textarea",{value:w.bio||"",onChange:a=>x({...w,bio:a.target.value}),rows:3,className:"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral"}):(0,d.jsx)("p",{className:"text-neutral-900",children:A.bio||"Parlez-nous de vous..."})]})]})]}),A.hostData&&(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-card p-6 mt-6",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-mbnb-navy mb-4",children:"Statistiques H\xf4te"}),(0,d.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-mbnb-coral",children:A.hostData.propertyCount}),(0,d.jsx)("div",{className:"text-sm text-neutral-600",children:"Propri\xe9t\xe9s"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-mbnb-navy",children:A.hostData.averageRating.toFixed(1)}),(0,d.jsx)("div",{className:"text-sm text-neutral-600",children:"Note moyenne"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsxs)("div",{className:"text-2xl font-bold text-mbnb-teal",children:[A.hostData.responseRate,"%"]}),(0,d.jsx)("div",{className:"text-sm text-neutral-600",children:"Taux r\xe9ponse"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-success-600",children:E(A.hostData.totalEarned)}),(0,d.jsx)("div",{className:"text-sm text-neutral-600",children:"Revenus totaux"})]})]})]})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-card p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-mbnb-navy mb-4",children:"V\xe9rifications"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(s.A,{className:"w-5 h-5 text-neutral-500"}),(0,d.jsx)("span",{className:"text-sm",children:"Email"})]}),A.emailVerified?(0,d.jsx)(q.A,{className:"w-5 h-5 text-mbnb-teal-dark"}):(0,d.jsx)("button",{className:"text-sm text-mbnb-coral hover:underline",children:"V\xe9rifier"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(t.A,{className:"w-5 h-5 text-neutral-500"}),(0,d.jsx)("span",{className:"text-sm",children:"T\xe9l\xe9phone"})]}),A.phoneVerified?(0,d.jsx)(q.A,{className:"w-5 h-5 text-mbnb-teal-dark"}):(0,d.jsx)("button",{className:"text-sm text-mbnb-coral hover:underline",children:"V\xe9rifier"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(u.A,{className:"w-5 h-5 text-neutral-500"}),(0,d.jsx)("span",{className:"text-sm",children:"Identit\xe9"})]}),A.identityVerified?(0,d.jsx)(q.A,{className:"w-5 h-5 text-mbnb-teal-dark"}):(0,d.jsx)("button",{className:"text-sm text-mbnb-coral hover:underline",children:"V\xe9rifier"})]})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-card p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-mbnb-navy mb-4",children:"Activit\xe9"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-neutral-600",children:"R\xe9servations"}),(0,d.jsx)("span",{className:"font-medium",children:A.totalBookings})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-neutral-600",children:"Total d\xe9pens\xe9"}),(0,d.jsx)("span",{className:"font-medium",children:E(A.totalSpent)})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-neutral-600",children:"Commission phase"}),(0,d.jsxs)("span",{className:"font-medium",children:["Phase ",A.commissionPhase]})]})]})]})]})]}),"loyalty"===y&&(0,d.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-card p-8",children:[(0,d.jsxs)("div",{className:"text-center mb-8",children:[(0,d.jsx)("div",{className:`inline-block px-6 py-3 rounded-full text-white font-bold text-xl ${(a=>{switch(a){case"LEGENDE":return"bg-gradient-to-r from-mbnb-coral to-mbnb-coral-dark";case"AMBASSADEUR":return"bg-mbnb-navy";case"AVENTURIER":return"bg-mbnb-steel";default:return"bg-gradient-to-r from-neutral-400 to-neutral-500"}})(A.loyaltyTier)}`,children:A.loyaltyTier}),(0,d.jsx)("p",{className:"text-neutral-600 mt-2",children:"Votre niveau de fid\xe9lit\xe9 actuel"})]}),(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsxs)("span",{className:"text-sm text-neutral-600",children:[A.loyaltyPoints," points"]}),(0,d.jsxs)("span",{className:"text-sm text-neutral-600",children:[A.nextTierPoints," points"]})]}),(0,d.jsx)("div",{className:"w-full bg-neutral-200 rounded-full h-3",children:(0,d.jsx)("div",{className:"bg-mbnb-coral h-3 rounded-full transition-all duration-500",style:{width:`${!A?0:Math.min(A.loyaltyPoints/A.nextTierPoints*100,100)}%`}})}),(0,d.jsxs)("p",{className:"text-center text-sm text-neutral-600 mt-2",children:["Plus que ",A.nextTierPoints-A.loyaltyPoints," points pour le prochain niveau"]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"border border-neutral-200 rounded-lg p-4",children:[(0,d.jsx)("h4",{className:"font-semibold text-mbnb-navy mb-2",children:"Avantages actuels"}),(0,d.jsxs)("ul",{className:"space-y-2 text-sm text-neutral-600",children:[(0,d.jsxs)("li",{className:"flex items-center gap-2",children:[(0,d.jsx)(q.A,{className:"w-4 h-4 text-mbnb-teal-dark"}),"R\xe9ductions exclusives"]}),(0,d.jsxs)("li",{className:"flex items-center gap-2",children:[(0,d.jsx)(q.A,{className:"w-4 h-4 text-mbnb-teal-dark"}),"Support prioritaire"]}),(0,d.jsxs)("li",{className:"flex items-center gap-2",children:[(0,d.jsx)(q.A,{className:"w-4 h-4 text-mbnb-teal-dark"}),"Acc\xe8s anticip\xe9 aux nouveaut\xe9s"]})]})]}),(0,d.jsxs)("div",{className:"border border-neutral-200 rounded-lg p-4",children:[(0,d.jsx)("h4",{className:"font-semibold text-mbnb-navy mb-2",children:"Comment gagner des points"}),(0,d.jsxs)("ul",{className:"space-y-2 text-sm text-neutral-600",children:[(0,d.jsx)("li",{children:"• 10 points par r\xe9servation"}),(0,d.jsx)("li",{children:"• 5 points par avis laiss\xe9"}),(0,d.jsx)("li",{children:"• 20 points par parrainage"}),(0,d.jsx)("li",{children:"• Points bonus \xe9v\xe9nements sp\xe9ciaux"})]})]})]})]})})]})]})}},33873:a=>{"use strict";a.exports=require("path")},35799:(a,b,c)=>{Promise.resolve().then(c.bind(c,33619))},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},42028:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4768).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},51383:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4768).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},53574:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4768).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},57465:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4768).A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75551:(a,b,c)=>{Promise.resolve().then(c.bind(c,19290))},75704:(a,b,c)=>{"use strict";c.d(b,{Yq:()=>h,cn:()=>g});var d=c(79390),e=c(25442),f=c(14196);function g(...a){return(0,e.QP)((0,d.$)(a))}function h(a,b="short"){let c="string"==typeof a?(0,f.Io)(a):a;return"short"===b?new Intl.DateTimeFormat("fr-MA",{day:"numeric",month:"short",year:"numeric"}).format(c):new Intl.DateTimeFormat("fr-MA",{weekday:"long",day:"numeric",month:"long",year:"numeric"}).format(c)}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86539:(a,b,c)=>{"use strict";c.d(b,{n:()=>k});var d=c(31768),e=c(15916),f=c(36795),g=c(83690),h=c(8306),i=class extends g.Q{#a;#b=void 0;#c;#d;constructor(a,b){super(),this.#a=a,this.setOptions(b),this.bindMethods(),this.#e()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(a){let b=this.options;this.options=this.#a.defaultMutationOptions(a),(0,h.f8)(this.options,b)||this.#a.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#c,observer:this}),b?.mutationKey&&this.options.mutationKey&&(0,h.EN)(b.mutationKey)!==(0,h.EN)(this.options.mutationKey)?this.reset():this.#c?.state.status==="pending"&&this.#c.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#c?.removeObserver(this)}onMutationUpdate(a){this.#e(),this.#f(a)}getCurrentResult(){return this.#b}reset(){this.#c?.removeObserver(this),this.#c=void 0,this.#e(),this.#f()}mutate(a,b){return this.#d=b,this.#c?.removeObserver(this),this.#c=this.#a.getMutationCache().build(this.#a,this.options),this.#c.addObserver(this),this.#c.execute(a)}#e(){let a=this.#c?.state??(0,e.$)();this.#b={...a,isPending:"pending"===a.status,isSuccess:"success"===a.status,isError:"error"===a.status,isIdle:"idle"===a.status,mutate:this.mutate,reset:this.reset}}#f(a){f.jG.batch(()=>{if(this.#d&&this.hasListeners()){let b=this.#b.variables,c=this.#b.context;a?.type==="success"?(this.#d.onSuccess?.(a.data,b,c),this.#d.onSettled?.(a.data,null,b,c)):a?.type==="error"&&(this.#d.onError?.(a.error,b,c),this.#d.onSettled?.(void 0,a.error,b,c))}this.listeners.forEach(a=>{a(this.#b)})})}},j=c(32315);function k(a,b){let c=(0,j.jE)(b),[e]=d.useState(()=>new i(c,a));d.useEffect(()=>{e.setOptions(a)},[e,a]);let g=d.useSyncExternalStore(d.useCallback(a=>e.subscribe(f.jG.batchCalls(a)),[e]),()=>e.getCurrentResult(),()=>e.getCurrentResult()),k=d.useCallback((a,b)=>{e.mutate(a,b).catch(h.lQ)},[e]);if(g.error&&(0,h.GU)(e.options.throwOnError,[g.error]))throw g.error;return{...g,mutate:k,mutateAsync:g.mutate}}},98551:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4768).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[4635,2922,4380,3743,1797,4367],()=>b(b.s=32091));module.exports=c})();
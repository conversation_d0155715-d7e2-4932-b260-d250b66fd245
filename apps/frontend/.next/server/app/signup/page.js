(()=>{var a={};a.id=879,a.ids=[879],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},2154:(a,b,c)=>{Promise.resolve().then(c.bind(c,72521))},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7229:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.default,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(73653),e=c(97714),f=c(85250),g=c(37587),h=c(22369),i=c(1889),j=c(96232),k=c(22841),l=c(46537),m=c(46027),n=c(78559),o=c(75928),p=c(19374),q=c(65971),r=c(261),s=c(79898),t=c(32967),u=c(26713),v=c(40139),w=c(14248),x=c(59580),y=c(57749),z=c(53123),A=c(89745),B=c(86439),C=c(96133),D=c(18283),E=c(39818),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,34139)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/signup/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,56035)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,74827)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/error.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,96133)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/global-error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,72993)),"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,15034,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,54693,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/signup/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/signup/page",pathname:"/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",relativeProjectDir:""});async function K(a,b,d){var F;let L="/signup/page";"/index"===L&&(L="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await J.prepare(a,b,{srcPage:L,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(L),{isOnDemandRevalidate:ah}=O,ai=J.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(F=$.routes[ag]??$.dynamicRoutes[ag])?void 0:F.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===J.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&J.isDev&&(aB=aa),J.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...D,tree:G,pages:H,GlobalError:C.default,handler:K,routeModule:J,__next_app__:I};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:L,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=J.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:J,page:L,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),J.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!J.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===J.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await J.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await J.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!J.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&E.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:L,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},8127:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))})},8282:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))})},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15417:(a,b,c)=>{"use strict";c.d(b,{MbnbImageGallery:()=>i,MbnbResponsiveImage:()=>g,useMbnbImagePreload:()=>h});var d=c(78157),e=c(31768),f=c.n(e);function g({baseName:a,folder:b,alt:c,className:e="",priority:f=!1,onClick:g}){let h=`/Storytelling/optimized/${b}/${a}`;return(0,d.jsxs)("picture",{className:e,onClick:g,children:[(0,d.jsx)("source",{srcSet:`${h}_mobile_375w.avif`,media:"(max-width: 375px)",type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_mobile_375w.webp`,media:"(max-width: 375px)",type:"image/webp"}),(0,d.jsx)("source",{srcSet:`${h}_mobile_375w.jpg`,media:"(max-width: 375px)",type:"image/jpeg"}),(0,d.jsx)("source",{srcSet:`${h}_mobile_414w.avif`,media:"(max-width: 414px)",type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_mobile_414w.webp`,media:"(max-width: 414px)",type:"image/webp"}),(0,d.jsx)("source",{srcSet:`${h}_mobile_414w.jpg`,media:"(max-width: 414px)",type:"image/jpeg"}),(0,d.jsx)("source",{srcSet:`${h}_mobile_480w.avif`,media:"(max-width: 480px)",type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_mobile_480w.webp`,media:"(max-width: 480px)",type:"image/webp"}),(0,d.jsx)("source",{srcSet:`${h}_mobile_480w.jpg`,media:"(max-width: 480px)",type:"image/jpeg"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_768w.avif`,media:"(max-width: 768px)",type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_768w.webp`,media:"(max-width: 768px)",type:"image/webp"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_768w.jpg`,media:"(max-width: 768px)",type:"image/jpeg"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_834w.avif`,media:"(max-width: 834px)",type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_834w.webp`,media:"(max-width: 834px)",type:"image/webp"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_834w.jpg`,media:"(max-width: 834px)",type:"image/jpeg"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_1024w.avif`,media:"(max-width: 1024px)",type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_1024w.webp`,media:"(max-width: 1024px)",type:"image/webp"}),(0,d.jsx)("source",{srcSet:`${h}_tablet_1024w.jpg`,media:"(max-width: 1024px)",type:"image/jpeg"}),(0,d.jsx)("source",{srcSet:`${h}_desktop_1200w.avif`,media:"(max-width: 1200px)",type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_desktop_1200w.webp`,media:"(max-width: 1200px)",type:"image/webp"}),(0,d.jsx)("source",{srcSet:`${h}_desktop_1200w.jpg`,media:"(max-width: 1200px)",type:"image/jpeg"}),(0,d.jsx)("source",{srcSet:`${h}_desktop_1440w.avif`,media:"(max-width: 1440px)",type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_desktop_1440w.webp`,media:"(max-width: 1440px)",type:"image/webp"}),(0,d.jsx)("source",{srcSet:`${h}_desktop_1440w.jpg`,media:"(max-width: 1440px)",type:"image/jpeg"}),(0,d.jsx)("source",{srcSet:`${h}_desktop_1920w.avif`,type:"image/avif"}),(0,d.jsx)("source",{srcSet:`${h}_desktop_1920w.webp`,type:"image/webp"}),(0,d.jsx)("img",{src:`${h}_desktop_1920w.jpg`,alt:c,loading:f?"eager":"lazy",className:`${e} w-full`,decoding:"async"})]})}function h(a,b){}function i({images:a,className:b=""}){let[c,e]=f().useState(null);return(0,d.jsxs)("div",{className:`grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 ${b}`,children:[a.map((a,b)=>(0,d.jsxs)("div",{className:"relative group cursor-pointer overflow-hidden rounded-lg",onClick:()=>e(b),children:[(0,d.jsx)(g,{baseName:a.baseName,folder:a.folder,alt:a.alt,className:"transition-transform duration-300 group-hover:scale-110",priority:b<4}),(0,d.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity duration-300"})]},`${a.folder}-${a.baseName}`)),null!==c&&(0,d.jsx)("div",{className:"fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center p-4",onClick:()=>e(null),children:(0,d.jsx)(g,{baseName:a[c].baseName,folder:a[c].folder,alt:a[c].alt,className:"max-w-full max-h-full object-contain",priority:!0})})]})}},16563:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34139:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(25459).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/signup/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/mbnb-v2/apps/frontend/app/signup/page.tsx","default")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},42758:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"}))})},54427:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))})},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72521:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>v});var d=c(78157),e=c(31768),f=c(87817),g=c(94496),h=c.n(g),i=c(74338),j=c(96064),k=c(90025),l=c(16563),m=c(8282),n=c(98869),o=c(54427),p=c(36209),q=c(42758),r=c(8127),s=c(95046);let t=[{icon:i.A,title:"IA Pr\xe9dictive Personnalis\xe9e",description:"Recommandations uniques bas\xe9es sur Prophet, XGBoost et StatsForecast"},{icon:j.A,title:"H\xe9bergements V\xe9rifi\xe9s",description:"Riads, kasbahs et dars authentiques s\xe9lectionn\xe9s par nos experts"},{icon:k.A,title:"TOP 100 Activit\xe9s Incluses",description:"Acc\xe8s exclusif aux meilleures exp\xe9riences touristiques marocaines"}],u=[{name:"Voyageur",price:"Gratuit",features:["Recherche illimit\xe9e d'h\xe9bergements","R\xe9servation instantan\xe9e","Support client 24/7","Acc\xe8s TOP 100 activit\xe9s","Recommandations IA basiques"],recommended:!1},{name:"Explorateur",price:"99 MAD/mois",features:["Tout du plan Voyageur","Recommandations IA avanc\xe9es","R\xe9ductions exclusives 10%","Acc\xe8s avant-premi\xe8re nouveaut\xe9s","Concierge virtuel personnalis\xe9","Annulation flexible"],recommended:!0},{name:"Ambassadeur",price:"199 MAD/mois",features:["Tout du plan Explorateur","R\xe9ductions VIP 15%","Surclassements automatiques","Concierge personnel d\xe9di\xe9","Exp\xe9riences exclusives","Programme fid\xe9lit\xe9 Premium"],recommended:!1}];function v(){let[a,b]=(0,e.useState)({firstName:"",lastName:"",email:"",phone:"",password:"",confirmPassword:"",country:"MA",preferredLanguage:"fr",userType:"traveler",plan:"free",mentorCode:"",agreeTerms:!1,agreeMarketing:!1}),[c,g]=(0,e.useState)(!1),[i,k]=(0,e.useState)(!1),[v,w]=(0,e.useState)(1),x=a=>{let{name:c,value:d,type:e}=a.target,f=a.target.checked;b(a=>({...a,[c]:"checkbox"===e?f:d}))},y=async b=>{b.preventDefault();try{if(a.mentorCode&&""!==a.mentorCode.trim()){let b=await fetch("/api/v1/mentor-program/validate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({mentorCode:a.mentorCode,email:a.email,role:"host"===a.userType?"HOST":"TRAVELER",userDetails:{firstName:a.firstName,lastName:a.lastName,password:a.password,phone:a.phone}})});if(b.ok){let a=await b.json();console.log("Inscription avec code mentor r\xe9ussie:",a),window.location.href="/login?registered=true&mentor=true"}else{let a=await b.json();console.error("Erreur validation code mentor:",a),alert(a.message||"Code mentor invalide ou programme complet")}}else{let b=await fetch("/api/v1/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:a.email,password:a.password,firstName:a.firstName,lastName:a.lastName,phone:a.phone,role:"host"===a.userType?"HOST":"TRAVELER"})});if(b.ok)console.log("Inscription normale r\xe9ussie"),window.location.href="/login?registered=true";else{let a=await b.json();console.error("Erreur inscription:",a),alert(a.message||"Erreur lors de l'inscription")}}}catch(a){console.error("Erreur soumission:",a),alert("Erreur lors de l'inscription. Veuillez r\xe9essayer.")}};return(0,d.jsxs)("main",{className:"min-h-screen bg-gray-50",children:[(0,d.jsxs)("section",{className:"relative h-[40vh] min-h-[300px] flex items-center justify-center",children:[(0,d.jsxs)("div",{className:"absolute inset-0",children:[(0,d.jsx)(f.XF,{baseName:"architecture_maroc_riad_bleu_majorelle_001",folder:"impact-eleve/ACTE_1_EVEIL",alt:"Rejoindre Mbnb",className:"absolute inset-0 w-full h-full object-cover"}),(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-black/60 to-black/40"})]}),(0,d.jsxs)("div",{className:"relative z-10 text-center text-white",children:[(0,d.jsx)("h1",{className:"text-4xl md:text-6xl font-bold mb-4",children:"Rejoignez Mbnb"}),(0,d.jsx)("p",{className:"text-xl md:text-2xl max-w-3xl mx-auto",children:"D\xe9couvrez le Maroc authentique avec l'IA pr\xe9dictive"})]})]}),(0,d.jsx)("section",{className:"py-16",children:(0,d.jsx)("div",{className:"container-mbnb",children:(0,d.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,d.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-xl",children:[(0,d.jsx)("div",{className:"flex items-center justify-between mb-8",children:[1,2,3].map(a=>(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${a<=v?"bg-mbnb-coral text-white":"bg-gray-200 text-gray-500"}`,children:a<v?(0,d.jsx)(l.A,{className:"w-5 h-5"}):a}),a<3&&(0,d.jsx)("div",{className:`w-16 h-1 mx-2 ${a<v?"bg-mbnb-coral":"bg-gray-200"}`})]},a))}),(0,d.jsxs)("form",{onSubmit:y,children:[1===v&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Informations Personnelles"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Pr\xe9nom *"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(m.A,{className:"w-5 h-5 absolute left-3 top-3 text-gray-400"}),(0,d.jsx)("input",{type:"text",name:"firstName",value:a.firstName,onChange:x,className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-mbnb-coral",placeholder:"Votre pr\xe9nom",required:!0})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nom de famille *"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(m.A,{className:"w-5 h-5 absolute left-3 top-3 text-gray-400"}),(0,d.jsx)("input",{type:"text",name:"lastName",value:a.lastName,onChange:x,className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-mbnb-coral",placeholder:"Votre nom",required:!0})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Adresse email *"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(n.A,{className:"w-5 h-5 absolute left-3 top-3 text-gray-400"}),(0,d.jsx)("input",{type:"email",name:"email",value:a.email,onChange:x,className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-mbnb-coral",placeholder:"<EMAIL>",required:!0})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"T\xe9l\xe9phone"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(o.A,{className:"w-5 h-5 absolute left-3 top-3 text-gray-400"}),(0,d.jsx)("input",{type:"tel",name:"phone",value:a.phone,onChange:x,className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-mbnb-coral",placeholder:"+212 6XX XXX XXX"})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Pays de r\xe9sidence"}),(0,d.jsxs)("select",{name:"country",value:a.country,onChange:x,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-mbnb-coral",children:[(0,d.jsx)("option",{value:"MA",children:"Maroc"}),(0,d.jsx)("option",{value:"FR",children:"France"}),(0,d.jsx)("option",{value:"ES",children:"Espagne"}),(0,d.jsx)("option",{value:"DE",children:"Allemagne"}),(0,d.jsx)("option",{value:"GB",children:"Royaume-Uni"}),(0,d.jsx)("option",{value:"US",children:"\xc9tats-Unis"}),(0,d.jsx)("option",{value:"CA",children:"Canada"}),(0,d.jsx)("option",{value:"CN",children:"Chine"}),(0,d.jsx)("option",{value:"RU",children:"Russie"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Langue pr\xe9f\xe9r\xe9e"}),(0,d.jsxs)("select",{name:"preferredLanguage",value:a.preferredLanguage,onChange:x,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-mbnb-coral",children:[(0,d.jsx)("option",{value:"fr",children:"Fran\xe7ais"}),(0,d.jsx)("option",{value:"ar",children:"العربية"}),(0,d.jsx)("option",{value:"en",children:"English"}),(0,d.jsx)("option",{value:"es",children:"Espa\xf1ol"}),(0,d.jsx)("option",{value:"zh",children:"中文"}),(0,d.jsx)("option",{value:"ru",children:"Русский"}),(0,d.jsx)("option",{value:"ar-ma",children:"الدارجة المغربية"})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Code Mentor SuperHost ",(0,d.jsx)("span",{className:"text-gray-400",children:"(optionnel)"})]}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(p.A,{className:"w-5 h-5 absolute left-3 top-3 text-gray-400"}),(0,d.jsx)("input",{type:"text",name:"mentorCode",value:a.mentorCode,onChange:x,className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-mbnb-coral ",placeholder:"Ex: MH2X9K (si vous avez \xe9t\xe9 invit\xe9)",readOnly:!1})]}),(0,d.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Si un SuperHost vous a parrain\xe9, entrez son code ici"})]})]}),2===v&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"S\xe9curit\xe9 du Compte"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Mot de passe *"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(q.A,{className:"w-5 h-5 absolute left-3 top-3 text-gray-400"}),(0,d.jsx)("input",{type:c?"text":"password",name:"password",value:a.password,onChange:x,className:"w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-mbnb-coral",placeholder:"Mot de passe s\xe9curis\xe9",required:!0}),(0,d.jsx)("button",{type:"button",onClick:()=>g(!c),className:"absolute right-3 top-3 text-gray-400 hover:text-gray-600",children:c?(0,d.jsx)(r.A,{className:"w-5 h-5"}):(0,d.jsx)(s.A,{className:"w-5 h-5"})})]}),(0,d.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Minimum 8 caract\xe8res avec majuscules, minuscules et chiffres"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirmer le mot de passe *"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(q.A,{className:"w-5 h-5 absolute left-3 top-3 text-gray-400"}),(0,d.jsx)("input",{type:i?"text":"password",name:"confirmPassword",value:a.confirmPassword,onChange:x,className:"w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-mbnb-coral",placeholder:"Confirmer le mot de passe",required:!0}),(0,d.jsx)("button",{type:"button",onClick:()=>k(!i),className:"absolute right-3 top-3 text-gray-400 hover:text-gray-600",children:i?(0,d.jsx)(r.A,{className:"w-5 h-5"}):(0,d.jsx)(s.A,{className:"w-5 h-5"})})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-4",children:"Type de compte"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("label",{className:`border-2 rounded-lg p-4 cursor-pointer transition-colors ${"traveler"===a.userType?"border-mbnb-coral bg-mbnb-coral/10":"border-gray-200 hover:border-gray-300"}`,children:[(0,d.jsx)("input",{type:"radio",name:"userType",value:"traveler",checked:"traveler"===a.userType,onChange:x,className:"sr-only"}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)(m.A,{className:"w-8 h-8 mx-auto mb-2 text-mbnb-coral"}),(0,d.jsx)("h3",{className:"font-semibold",children:"Voyageur"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Je recherche des h\xe9bergements"})]})]}),(0,d.jsxs)("label",{className:`border-2 rounded-lg p-4 cursor-pointer transition-colors ${"host"===a.userType?"border-mbnb-coral bg-mbnb-coral/10":"border-gray-200 hover:border-gray-300"}`,children:[(0,d.jsx)("input",{type:"radio",name:"userType",value:"host",checked:"host"===a.userType,onChange:x,className:"sr-only"}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)(j.A,{className:"w-8 h-8 mx-auto mb-2 text-mbnb-coral"}),(0,d.jsx)("h3",{className:"font-semibold",children:"H\xf4te"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Je propose des h\xe9bergements"})]})]})]})]})]}),3===v&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Choisir un Plan"}),(0,d.jsx)("div",{className:"space-y-4",children:u.map((b,c)=>(0,d.jsxs)("label",{className:`border-2 rounded-lg p-6 cursor-pointer transition-colors ${a.plan===b.name.toLowerCase().replace(" ","_")?"border-mbnb-coral bg-mbnb-coral/10":"border-gray-200 hover:border-gray-300"} ${b.recommended?"relative":""}`,children:[b.recommended&&(0,d.jsx)("div",{className:"absolute -top-3 left-4",children:(0,d.jsx)("span",{className:"bg-mbnb-coral text-white px-3 py-1 rounded-full text-sm font-medium",children:"Recommand\xe9"})}),(0,d.jsx)("input",{type:"radio",name:"plan",value:b.name.toLowerCase().replace(" ","_"),checked:a.plan===b.name.toLowerCase().replace(" ","_"),onChange:x,className:"sr-only"}),(0,d.jsx)("div",{className:"flex justify-between items-start mb-4",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:b.name}),(0,d.jsx)("p",{className:"text-2xl font-bold text-mbnb-coral",children:b.price})]})}),(0,d.jsx)("ul",{className:"space-y-2",children:b.features.map((a,b)=>(0,d.jsxs)("li",{className:"flex items-center text-sm text-gray-600",children:[(0,d.jsx)(l.A,{className:"w-4 h-4 mr-2 text-green-500 flex-shrink-0"}),a]},b))})]},c))}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("label",{className:"flex items-start",children:[(0,d.jsx)("input",{type:"checkbox",name:"agreeTerms",checked:a.agreeTerms,onChange:x,className:"mt-1 mr-3 w-4 h-4 text-mbnb-coral border-gray-300 rounded focus:ring-mbnb-coral",required:!0}),(0,d.jsxs)("span",{className:"text-sm text-gray-600",children:["J'accepte les"," ",(0,d.jsx)(h(),{href:"/terms",className:"text-mbnb-coral hover:underline",children:"conditions d'utilisation"})," ","et la"," ",(0,d.jsx)(h(),{href:"/privacy",className:"text-mbnb-coral hover:underline",children:"politique de confidentialit\xe9"})," ","de Mbnb *"]})]}),(0,d.jsxs)("label",{className:"flex items-start",children:[(0,d.jsx)("input",{type:"checkbox",name:"agreeMarketing",checked:a.agreeMarketing,onChange:x,className:"mt-1 mr-3 w-4 h-4 text-mbnb-coral border-gray-300 rounded focus:ring-mbnb-coral"}),(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Je souhaite recevoir les offres sp\xe9ciales et actualit\xe9s Mbnb par email"})]})]})]}),(0,d.jsxs)("div",{className:"flex justify-between pt-8",children:[(0,d.jsx)("button",{type:"button",onClick:()=>{v>1&&w(v-1)},className:`px-6 py-3 rounded-full font-semibold transition-colors ${1===v?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,disabled:1===v,children:"Pr\xe9c\xe9dent"}),v<3?(0,d.jsx)("button",{type:"button",onClick:()=>{v<3&&w(v+1)},className:"bg-mbnb-coral hover:bg-mbnb-coral/90 text-white px-8 py-3 rounded-full font-semibold transition-colors",children:"Suivant"}):(0,d.jsx)("button",{type:"submit",className:"bg-mbnb-coral hover:bg-mbnb-coral/90 text-white px-8 py-3 rounded-full font-semibold transition-colors",children:"Cr\xe9er mon compte"})]})]}),(0,d.jsx)("div",{className:"mt-8 text-center",children:(0,d.jsxs)("p",{className:"text-gray-600",children:["Vous avez d\xe9j\xe0 un compte ?"," ",(0,d.jsx)(h(),{href:"/login",className:"text-mbnb-coral hover:underline font-semibold",children:"Se connecter"})]})})]}),(0,d.jsxs)("div",{className:"space-y-8",children:[(0,d.jsxs)("div",{className:"bg-mbnb-navy rounded-2xl p-8 text-white",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold mb-6",children:"Pourquoi Mbnb ?"}),(0,d.jsx)("div",{className:"space-y-6",children:t.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)(a.icon,{className:"w-8 h-8 mr-4 flex-shrink-0 mt-1"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-semibold mb-2",children:a.title}),(0,d.jsx)("p",{className:"text-white/90",children:a.description})]})]},b))})]}),(0,d.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-xl",children:[(0,d.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-6",children:"Statistiques Mbnb"}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-mbnb-coral",children:"5,000+"}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"H\xe9bergements v\xe9rifi\xe9s"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-mbnb-coral",children:"100"}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Activit\xe9s TOP"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-mbnb-coral",children:"4.8"}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Note moyenne"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-mbnb-coral",children:"96%"}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Satisfaction"})]})]})]})]})]})})})})]})}},74338:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"}))})},84002:(a,b,c)=>{Promise.resolve().then(c.bind(c,34139))},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},87817:(a,b,c)=>{"use strict";c.d(b,{XF:()=>d.MbnbResponsiveImage});var d=c(15417)},95046:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},98869:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(31768);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))})}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[4635,2922,4367],()=>b(b.s=7229));module.exports=c})();
"use strict";(()=>{var a={};a.id=3220,a.ids=[3220],a.modules={8732:a=>{a.exports=require("react/jsx-runtime")},33873:a=>{a.exports=require("path")},40361:a=>{a.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},56472:a=>{a.exports=require("@opentelemetry/api")},82015:a=>{a.exports=require("react")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[383],()=>b(b.s=50383));module.exports=c})();
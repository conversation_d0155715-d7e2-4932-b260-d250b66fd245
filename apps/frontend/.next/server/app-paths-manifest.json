{"/api/places/autocomplete/route": "app/api/places/autocomplete/route.js", "/_not-found/page": "app/_not-found/page.js", "/robots.txt/route": "app/robots.txt/route.js", "/sitemap.xml/route": "app/sitemap.xml/route.js", "/activities/[id]/page": "app/activities/[id]/page.js", "/admin/dashboard/page": "app/admin/dashboard/page.js", "/admin/users/page": "app/admin/users/page.js", "/admin/monitoring/page": "app/admin/monitoring/page.js", "/auth/login/page": "app/auth/login/page.js", "/auth/register/page": "app/auth/register/page.js", "/contact/page": "app/contact/page.js", "/experiences/page": "app/experiences/page.js", "/gift-cards/page": "app/gift-cards/page.js", "/guest-referrals/page": "app/guest-referrals/page.js", "/guide/premier-voyage/page": "app/guide/premier-voyage/page.js", "/help/page": "app/help/page.js", "/host-stories/page": "app/host-stories/page.js", "/host/dashboard/page": "app/host/dashboard/page.js", "/host/page": "app/host/page.js", "/host/signup/page": "app/host/signup/page.js", "/instant-book/page": "app/instant-book/page.js", "/loyalty/page": "app/loyalty/page.js", "/profile/page": "app/profile/page.js", "/property/[id]/page": "app/property/[id]/page.js", "/reservation/[id]/page": "app/reservation/[id]/page.js", "/search/page": "app/search/page.js", "/signup/page": "app/signup/page.js", "/sitemap/page": "app/sitemap/page.js", "/test-api/page": "app/test-api/page.js", "/about/page": "app/about/page.js", "/accessibility/page": "app/accessibility/page.js", "/activities/page": "app/activities/page.js", "/blog/page": "app/blog/page.js", "/cancellation-options/page": "app/cancellation-options/page.js", "/careers/page": "app/careers/page.js", "/cancellation/page": "app/cancellation/page.js", "/associates/page": "app/associates/page.js", "/chat-safety/page": "app/chat-safety/page.js", "/cookies/page": "app/cookies/page.js", "/destinations/all/page": "app/destinations/all/page.js", "/destinations/agadir/page": "app/destinations/agadir/page.js", "/destinations/casablanca/page": "app/destinations/casablanca/page.js", "/destinations/chefchaouen/page": "app/destinations/chefchaouen/page.js", "/destinations/essaouira/page": "app/destinations/essaouira/page.js", "/destinations/fes/page": "app/destinations/fes/page.js", "/destinations/marrakech/page": "app/destinations/marrakech/page.js", "/destinations/page": "app/destinations/page.js", "/destinations/rabat/page": "app/destinations/rabat/page.js", "/help/account/page": "app/help/account/page.js", "/diversity/page": "app/diversity/page.js", "/destinations/tanger/page": "app/destinations/tanger/page.js", "/help/faq/page": "app/help/faq/page.js", "/help/reservations/page": "app/help/reservations/page.js", "/help/neighborhood-support/page": "app/help/neighborhood-support/page.js", "/help/payments/page": "app/help/payments/page.js", "/help/safety/page": "app/help/safety/page.js", "/help/support/page": "app/help/support/page.js", "/host/academy/page": "app/host/academy/page.js", "/host/experiences/page": "app/host/experiences/page.js", "/host/homes/page": "app/host/homes/page.js", "/host/resources/page": "app/host/resources/page.js", "/host/responsible/page": "app/host/responsible/page.js", "/host/insurance/page": "app/host/insurance/page.js", "/legal/page": "app/legal/page.js", "/how-it-works/page": "app/how-it-works/page.js", "/magazine/page": "app/magazine/page.js", "/investors/page": "app/investors/page.js", "/page": "app/page.js", "/newsroom/page": "app/newsroom/page.js", "/press/page": "app/press/page.js", "/neighborhood/page": "app/neighborhood/page.js", "/privacy/page": "app/privacy/page.js", "/property/page": "app/property/page.js", "/promotions/page": "app/promotions/page.js", "/sustainability/page": "app/sustainability/page.js", "/terms/page": "app/terms/page.js", "/report-concern/page": "app/report-concern/page.js", "/safety/page": "app/safety/page.js", "/trust-safety/page": "app/trust-safety/page.js", "/provider/dashboard/page": "app/provider/dashboard/page.js", "/provider/properties/page": "app/provider/properties/page.js", "/traveler/dashboard/page": "app/traveler/dashboard/page.js", "/provider/page": "app/provider/page.js", "/traveler/page": "app/traveler/page.js"}
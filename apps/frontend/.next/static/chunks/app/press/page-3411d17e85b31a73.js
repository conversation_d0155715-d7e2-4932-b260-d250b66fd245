(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[498,809,1599,2793,3831,3846,3874,4455,4807,5184,5982,6304,6366,7903,8337,9354,9376,9681],{28265:(e,a,t)=>{Promise.resolve().then(t.bind(t,62205)),Promise.resolve().then(t.t.bind(t,19664,23))},62205:(e,a,t)=>{"use strict";t.d(a,{MbnbImageGallery:()=>p,MbnbResponsiveImage:()=>o,useMbnbImagePreload:()=>s});var c=t(54568),i=t(7620);function o(e){let{baseName:a,folder:t,alt:i,className:o="",priority:s=!1,onClick:p}=e,m="/Storytelling/optimized/".concat(t,"/").concat(a);return(0,c.jsxs)("picture",{className:o,onClick:p,children:[(0,c.jsx)("source",{srcSet:"".concat(m,"_mobile_375w.avif"),media:"(max-width: 375px)",type:"image/avif"}),(0,c.jsx)("source",{srcSet:"".concat(m,"_mobile_375w.webp"),media:"(max-width: 375px)",type:"image/webp"}),(0,c.jsx)("source",{srcSet:"".concat(m,"_mobile_375w.jpg"),media:"(max-width: 375px)",type:"image/jpeg"}),(0,c.jsx)("source",{srcSet:"".concat(m,"_mobile_414w.avif"),media:"(max-width: 414px)",type:"image/avif"}),(0,c.jsx)("source",{srcSet:"".concat(m,"_mobile_414w.webp"),media:"(max-width: 414px)",type:"image/webp"}),(0,c.jsx)("source",{srcSet:"".concat(m,"_mobile_414w.jpg"),media:"(max-width: 414px)",type:"image/jpeg"}),(0,c.jsx)("source",{srcSet:"".concat(m,"_mobile_480w.avif"),media:"(max-width: ".concat(480,"px)"),type:"image/avif"}),(0,c.jsx)("source",{srcSet:"".concat(m,"_mobile_480w.webp"),media:"(max-width: ".concat(480,"px)"),type:"image/webp"}),(0,c.jsx)("source",{srcSet:"".concat(m,"_mobile_480w.jpg"),media:"(max-width: ".concat(480,"px)"),type:"image/jpeg"}),(0,c.jsx)("source",{srcSet:"".concat(m,"_tablet_768w.avif"),media:"(max-width: 768px)",type:"image/avif"}),(0,c.jsx)("source",{srcSet:"".concat(m,"_tablet_768w.webp"),media:"(max-width: 768px)",type:"image/webp"}),(0,c.jsx)("source",{srcSet:"".concat(m,"_tablet_768w.jpg"),media:"(max-width: 768px)",type:"image/jpeg"}),(0,c.jsx)("source",{srcSet:"".concat(m,"_tablet_834w.avif"),media:"(max-width: 834px)",type:"image/avif"}),(0,c.jsx)("source",{srcSet:"".concat(m,"_tablet_834w.webp"),media:"(max-width: 834px)",type:"image/webp"}),(0,c.jsx)("source",{srcSet:"".concat(m,"_tablet_834w.jpg"),media:"(max-width: 834px)",type:"image/jpeg"}),(0,c.jsx)("source",{srcSet:"".concat(m,"_tablet_1024w.avif"),media:"(max-width: ".concat(1024,"px)"),type:"image/avif"}),(0,c.jsx)("source",{srcSet:"".concat(m,"_tablet_1024w.webp"),media:"(max-width: ".concat(1024,"px)"),type:"image/webp"}),(0,c.jsx)("source",{srcSet:"".concat(m,"_tablet_1024w.jpg"),media:"(max-width: ".concat(1024,"px)"),type:"image/jpeg"}),(0,c.jsx)("source",{srcSet:"".concat(m,"_desktop_1200w.avif"),media:"(max-width: 1200px)",type:"image/avif"}),(0,c.jsx)("source",{srcSet:"".concat(m,"_desktop_1200w.webp"),media:"(max-width: 1200px)",type:"image/webp"}),(0,c.jsx)("source",{srcSet:"".concat(m,"_desktop_1200w.jpg"),media:"(max-width: 1200px)",type:"image/jpeg"}),(0,c.jsx)("source",{srcSet:"".concat(m,"_desktop_1440w.avif"),media:"(max-width: 1440px)",type:"image/avif"}),(0,c.jsx)("source",{srcSet:"".concat(m,"_desktop_1440w.webp"),media:"(max-width: 1440px)",type:"image/webp"}),(0,c.jsx)("source",{srcSet:"".concat(m,"_desktop_1440w.jpg"),media:"(max-width: 1440px)",type:"image/jpeg"}),(0,c.jsx)("source",{srcSet:"".concat(m,"_desktop_1920w.avif"),type:"image/avif"}),(0,c.jsx)("source",{srcSet:"".concat(m,"_desktop_1920w.webp"),type:"image/webp"}),(0,c.jsx)("img",{src:"".concat(m,"_desktop_1920w.jpg"),alt:i,loading:s?"eager":"lazy",className:"".concat(o," w-full"),decoding:"async"})]})}function s(e,a){i.useEffect(()=>{let t=window.innerWidth<=480,c=window.innerWidth>480&&window.innerWidth<=1024,i="/Storytelling/optimized/".concat(a,"/").concat(e);(t?["".concat(i,"_mobile_414w.avif"),"".concat(i,"_mobile_414w.webp")]:c?["".concat(i,"_tablet_834w.avif"),"".concat(i,"_tablet_834w.webp")]:["".concat(i,"_desktop_1440w.avif"),"".concat(i,"_desktop_1440w.webp")]).forEach(e=>{let a=document.createElement("link");a.rel="preload",a.as="image",a.href=e,document.head.appendChild(a)})},[e,a])}function p(e){let{images:a,className:t=""}=e,[s,p]=i.useState(null);return(0,c.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 ".concat(t),children:[a.map((e,a)=>(0,c.jsxs)("div",{className:"relative group cursor-pointer overflow-hidden rounded-lg",onClick:()=>p(a),children:[(0,c.jsx)(o,{baseName:e.baseName,folder:e.folder,alt:e.alt,className:"transition-transform duration-300 group-hover:scale-110",priority:a<4}),(0,c.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity duration-300"})]},"".concat(e.folder,"-").concat(e.baseName))),null!==s&&(0,c.jsx)("div",{className:"fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center p-4",onClick:()=>p(null),children:(0,c.jsx)(o,{baseName:a[s].baseName,folder:a[s].folder,alt:a[s].alt,className:"max-w-full max-h-full object-contain",priority:!0})})]})}}},e=>{e.O(0,[9664,587,18,7358],()=>e(e.s=28265)),_N_E=e.O()}]);
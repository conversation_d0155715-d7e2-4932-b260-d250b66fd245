(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1437],{826:(e,t,r)=>{"use strict";r.d(t,{Yq:()=>i,cn:()=>l});var a=r(72902),s=r(13714),n=r(58054);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"short",r="string"==typeof e?(0,n.Io)(e):e;return"short"===t?new Intl.DateTimeFormat("fr-MA",{day:"numeric",month:"short",year:"numeric"}).format(r):new Intl.DateTimeFormat("fr-MA",{weekday:"long",day:"numeric",month:"long",year:"numeric"}).format(r)}},8526:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(7620);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"}))})},13243:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var a=r(54568),s=r(7620),n=r(27541),l=r(31178),i=r(78584),o=r(81100),c=r(41568),d=r(8526),m=r(88859),h=r(46139),u=r(14950),f=r(18997),x=r(52202),g=r(25805),p=r(40790),b=r(32536),v=r(19664),w=r.n(v),y=r(826);function j(){var e,t,r,v,j,N,k;let M=(0,n.useRouter)(),{user:A,isAuthenticated:E}=(0,p.A)(),[L]=(0,s.useState)(null),[C]=(0,s.useState)([]),[P]=(0,s.useState)([]),[T]=(0,s.useState)("30 jours"),[Z,O]=(0,s.useState)(!0);(0,s.useEffect)(()=>{var e;E&&(null==A||null==(e=A.roles)?void 0:e.isActivityProvider)||M.push("/login")},[E,A,M]),(0,s.useEffect)(()=>{V()},[]);let V=async()=>{try{O(!0),(await fetch("/api/v1/activity-providers/dashboard/stats",{headers:{Authorization:"Bearer ".concat(localStorage.getItem("mbnb_token")),"Content-Type":"application/json"}})).ok||console.warn("Erreur chargement stats provider"),(await fetch("/api/v1/activity-providers/activities",{headers:{Authorization:"Bearer ".concat(localStorage.getItem("mbnb_token")),"Content-Type":"application/json"}})).ok||console.warn("Erreur chargement activit\xe9s provider")}catch(e){console.warn("Erreur chargement donn\xe9es provider:",e instanceof Error?e.message:"Erreur inconnue")}finally{O(!1)}},S=(null==L||null==(e=L.revenue)?void 0:e.total)||0,I=14*S/100,R=C.length,B=e=>new Intl.NumberFormat("fr-MA",{style:"currency",currency:"MAD"}).format(e);return Z?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-mbnb-coral"})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("header",{className:"bg-white border-b border-gray-200",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-mbnb-coral",children:"Mbnb"}),(0,a.jsx)("span",{className:"ml-3 text-gray-500",children:"Provider Dashboard"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("button",{className:"relative p-2 text-gray-400 hover:text-gray-500",children:[(0,a.jsx)(l.A,{className:"h-6 w-6"}),(0,a.jsx)("span",{className:"absolute top-1 right-1 block h-2 w-2 rounded-full bg-mbnb-coral"})]}),(0,a.jsx)(w(),{href:"/provider/settings",children:(0,a.jsx)(i.A,{className:"h-6 w-6 text-gray-400 hover:text-gray-500"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"h-8 w-8 rounded-full bg-mbnb-teal flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-white font-medium text-sm",children:[null==A||null==(r=A.profile)||null==(t=r.firstName)?void 0:t[0],null==A||null==(j=A.profile)||null==(v=j.lastName)?void 0:v[0]]})}),(0,a.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[null==A||null==(N=A.profile)?void 0:N.firstName," ",null==A||null==(k=A.profile)?void 0:k.lastName]})]})]})]})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)(o.A,{className:"h-8 w-8 text-mbnb-coral"}),(0,a.jsx)("span",{className:"text-sm text-green-600 font-medium",children:"+12%"})]}),(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-500",children:"Revenus totaux"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:B(S)}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-2",children:["Derniers ",T]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)(c.A,{className:"h-8 w-8 text-mbnb-teal"}),(0,a.jsx)("span",{className:"text-sm text-green-600 font-medium",children:"+5%"})]}),(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-500",children:"Taux d'occupation"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:[85,"%"]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-2",children:[P.length," propri\xe9t\xe9s"]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)(d.A,{className:"h-8 w-8 text-mbnb-navy"}),(0,a.jsx)(x.A,{className:"h-5 w-5 text-green-500"})]}),(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-500",children:"R\xe9servations"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:R}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-2",children:[C.filter(e=>"CONFIRMED"===e.status).length," confirm\xe9es"]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)(m.A,{className:"h-8 w-8 text-yellow-400"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[95,"% r\xe9ponse"]})]}),(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-500",children:"Note moyenne"}),(0,a.jsxs)("div",{className:"flex items-center mt-1",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:4.5}),(0,a.jsx)(m.A,{className:"h-5 w-5 text-yellow-400 ml-1"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"Sur 5.0"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200 flex justify-between items-center",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Mes propri\xe9t\xe9s"}),(0,a.jsxs)(w(),{href:"/provider/properties/new",className:"flex items-center px-3 py-2 bg-mbnb-coral text-white rounded-lg hover:bg-mbnb-coral-dark text-sm",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-1"}),"Ajouter"]})]}),(0,a.jsxs)("div",{className:"divide-y divide-gray-200 max-h-96 overflow-y-auto",children:[P.map(e=>{var t,r,s,n,l;return(0,a.jsxs)(w(),{href:"/provider/properties/".concat(e.mbnbId),className:"p-4 hover:bg-gray-50 flex items-center space-x-4",children:[(0,a.jsx)(b.default,{src:("string"==typeof(null==(r=e.media)||null==(t=r.images)?void 0:t[0])?e.media.images[0]:null==(l=e.media)||null==(n=l.images)||null==(s=n[0])?void 0:s.url)||"/placeholder-property.jpg",alt:e.title,width:60,height:60,className:"rounded-lg object-cover"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-500 mt-1",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-1"}),e.location.city]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:B(e.price||0)}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"/nuit"})]})]},e.mbnbId)}),0===P.length&&(0,a.jsxs)("div",{className:"p-8 text-center text-gray-500",children:[(0,a.jsx)(c.A,{className:"h-12 w-12 mx-auto mb-3 text-gray-300"}),(0,a.jsx)("p",{children:"Aucune propri\xe9t\xe9 ajout\xe9e"}),(0,a.jsx)(w(),{href:"/provider/properties/new",className:"mt-3 inline-block text-mbnb-coral hover:underline",children:"Ajouter votre premi\xe8re propri\xe9t\xe9"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200 flex justify-between items-center",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"R\xe9servations r\xe9centes"}),(0,a.jsx)(w(),{href:"/provider/bookings",className:"text-sm text-mbnb-coral hover:underline",children:"Voir toutes"})]}),(0,a.jsxs)("div",{className:"divide-y divide-gray-200 max-h-96 overflow-y-auto",children:[C.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"p-4 hover:bg-gray-50",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"font-medium text-gray-900",children:[e.travelerInfo.firstName," ",e.travelerInfo.lastName]}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:e.property.title})]}),(e=>{let t={confirmed:{label:"Confirm\xe9e",className:"bg-green-100 text-green-800"},pending:{label:"En attente",className:"bg-yellow-100 text-yellow-800"},cancelled:{label:"Annul\xe9e",className:"bg-red-100 text-red-800"},completed:{label:"Termin\xe9e",className:"bg-blue-100 text-blue-800"}},r=t[e]||t.pending;return(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full font-medium ".concat(r.className),children:r.label})})(e.status)]}),(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 mr-1"}),(0,a.jsxs)("span",{children:[(0,y.Yq)(e.dates.checkIn,"short")," -",(0,y.Yq)(e.dates.checkOut,"short")]})]}),(0,a.jsxs)("div",{className:"mt-2 flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:B(e.pricing.total)}),(0,a.jsx)(f.A,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[e.guests.adults+(e.guests.children||0)," voyageurs"]})]})]},e.mbnbId)),0===C.length&&(0,a.jsxs)("div",{className:"p-8 text-center text-gray-500",children:[(0,a.jsx)(d.A,{className:"h-12 w-12 mx-auto mb-3 text-gray-300"}),(0,a.jsx)("p",{children:"Aucune r\xe9servation pour le moment"})]})]})]})]}),I>0&&(0,a.jsx)("div",{className:"mt-8 bg-mbnb-coral rounded-lg shadow-lg p-6 text-white",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-bold mb-2",children:"Commission TOP 100 Activit\xe9s"}),(0,a.jsx)("p",{className:"text-sm opacity-90",children:"Vos voyageurs ont r\xe9serv\xe9 des activit\xe9s TOP 100"}),(0,a.jsx)("p",{className:"text-3xl font-bold mt-3",children:B(I)}),(0,a.jsxs)("p",{className:"text-xs opacity-75 mt-1",children:["Commission ",18,"% (",14,"% provider + ",4,"% voyageur)"]})]}),(0,a.jsx)(g.A,{className:"h-24 w-24 opacity-20"})]})})]})]})}},14950:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(7620);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"}))})},18997:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(7620);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"}))})},25805:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(7620);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{d:"M18.375 2.25c-1.035 0-1.875.84-1.875 1.875v15.75c0 1.035.84 1.875 1.875 1.875h.75c1.035 0 1.875-.84 1.875-1.875V4.125c0-1.036-.84-1.875-1.875-1.875h-.75ZM9.75 8.625c0-1.036.84-1.875 1.875-1.875h.75c1.036 0 1.875.84 1.875 1.875v11.25c0 1.035-.84 1.875-1.875 1.875h-.75a1.875 1.875 0 0 1-1.875-1.875V8.625ZM3 13.125c0-1.036.84-1.875 1.875-1.875h.75c1.036 0 1.875.84 1.875 1.875v6.75c0 1.035-.84 1.875-1.875 1.875h-.75A1.875 1.875 0 0 1 3 19.875v-6.75Z"}))})},27541:(e,t,r)=>{"use strict";var a=r(43041);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},31178:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(7620);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))})},40790:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(21367),s=r(50848),n=r(15263);let l=(0,a.vt)()((0,s.Zr)((0,n.D)((e,t)=>({user:null,token:null,refreshToken:null,isAuthenticated:!1,isLoading:!1,error:null,heritageInterests:[],activityPreferences:[],preferredLanguage:"fr",preferredCurrency:"MAD",loginToMbnb:async t=>{e(e=>{e.isLoading=!0,e.error=null});try{let r=await fetch("".concat("http://localhost:3001/api/v1","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json","X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(t)});if(!r.ok){let e=await r.json();throw Error(e.message||"Authentification Mbnb \xe9chou\xe9e")}let a=await r.json();e(e=>{e.user=a.user,e.token=a.token,e.refreshToken=a.refreshToken||null,e.isAuthenticated=!0,e.isLoading=!1,e.preferredLanguage=a.user.profile.preferredLanguage,e.preferredCurrency=a.user.profile.preferredCurrency})}catch(r){let t=r instanceof Error?r.message:"Erreur de connexion Mbnb";throw e(e=>{e.error=t,e.isLoading=!1}),r}},registerToMbnb:async t=>{e(e=>{e.isLoading=!0,e.error=null});try{let r=await fetch("".concat("http://localhost:3001/api/v1","/auth/register"),{method:"POST",headers:{"Content-Type":"application/json","X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(t)});if(!r.ok){let e=await r.json();throw Error(e.message||"Inscription Mbnb \xe9chou\xe9e")}let a=await r.json();e(e=>{e.user=a.user,e.token=a.token,e.refreshToken=a.refreshToken||null,e.isAuthenticated=!0,e.isLoading=!1,e.preferredLanguage=a.user.profile.preferredLanguage,e.preferredCurrency=a.user.profile.preferredCurrency})}catch(r){let t=r instanceof Error?r.message:"Erreur d'inscription Mbnb";throw e(e=>{e.error=t,e.isLoading=!1}),r}},logoutFromMbnb:()=>{e(e=>{e.user=null,e.token=null,e.refreshToken=null,e.isAuthenticated=!1,e.error=null,e.heritageInterests=[],e.activityPreferences=[],e.preferredLanguage="fr",e.preferredCurrency="MAD"}),localStorage.removeItem("mbnb-auth-storage"),window.location.href="/"},refreshMbnbToken:async()=>{let r=t().refreshToken;if(!r)return void t().logoutFromMbnb();try{let t=await fetch("".concat("http://localhost:3001/api/v1","/auth/refresh"),{method:"POST",headers:{"Content-Type":"application/json","X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify({refreshToken:r})});if(!t.ok)throw Error("Refresh token Mbnb expir\xe9");let a=await t.json();e(e=>{e.token=a.token,e.refreshToken=a.refreshToken})}catch(e){t().logoutFromMbnb()}},updateMbnbProfile:async r=>{e(e=>{e.isLoading=!0,e.error=null});try{let a=await fetch("".concat("http://localhost:3001/api/v1","/users/profile"),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t().token),"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(r)});if(!a.ok){let e=await a.json();throw Error(e.message||"Mise \xe0 jour profil Mbnb \xe9chou\xe9e")}let s=await a.json();e(e=>{e.user=s,e.isLoading=!1,e.preferredLanguage=s.profile.preferredLanguage,e.preferredCurrency=s.profile.preferredCurrency})}catch(r){let t=r instanceof Error?r.message:"Erreur mise \xe0 jour profil Mbnb";throw e(e=>{e.error=t,e.isLoading=!1}),r}},updateMbnbPreferences:async r=>{e(e=>{e.isLoading=!0,e.error=null});try{let a=await fetch("".concat("http://localhost:3001/api/v1","/users/preferences"),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t().token),"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(r)});if(!a.ok){let e=await a.json();throw Error(e.message||"Mise \xe0 jour pr\xe9f\xe9rences Mbnb \xe9chou\xe9e")}e(e=>{r.heritageInterests&&(e.heritageInterests=r.heritageInterests),r.activityPreferences&&(e.activityPreferences=r.activityPreferences),r.preferredLanguage&&(e.preferredLanguage=r.preferredLanguage),r.preferredCurrency&&(e.preferredCurrency=r.preferredCurrency),e.isLoading=!1})}catch(r){let t=r instanceof Error?r.message:"Erreur mise \xe0 jour pr\xe9f\xe9rences Mbnb";throw e(e=>{e.error=t,e.isLoading=!1}),r}},verifyMbnbEmail:async r=>{let a=await fetch("".concat("http://localhost:3001/api/v1","/auth/verify-email"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t().token),"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify({code:r})});if(!a.ok)throw Error((await a.json()).message||"V\xe9rification email Mbnb \xe9chou\xe9e");e(e=>{e.user&&(e.user.verification.emailVerified=!0)})},verifyMbnbPhone:async r=>{let a=await fetch("".concat("http://localhost:3001/api/v1","/auth/verify-phone"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t().token),"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify({code:r})});if(!a.ok)throw Error((await a.json()).message||"V\xe9rification t\xe9l\xe9phone Mbnb \xe9chou\xe9e");e(e=>{e.user&&(e.user.verification.phoneVerified=!0)})},clearError:()=>e(e=>{e.error=null})})),{name:"mbnb-auth-storage",storage:(0,s.KU)(()=>localStorage),partialize:e=>({user:e.user,token:e.token,refreshToken:e.refreshToken,isAuthenticated:e.isAuthenticated,heritageInterests:e.heritageInterests,activityPreferences:e.activityPreferences,preferredLanguage:e.preferredLanguage,preferredCurrency:e.preferredCurrency})}))},41568:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(7620);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"}))})},46139:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(7620);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))})},52202:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(7620);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{fillRule:"evenodd",d:"M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z",clipRule:"evenodd"}))})},58054:(e,t,r)=>{"use strict";function a(){return new Date}function s(e){return"string"==typeof e?new Date(e):e}function n(e,t){let r=new Date(e.getTime());return r.setDate(r.getDate()+t),r}r.d(t,{Io:()=>s,MZ:()=>a,fi:()=>n})},78584:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(7620);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.*************.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},81066:(e,t,r)=>{Promise.resolve().then(r.bind(r,13243))},81100:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(7620);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z"}))})},88859:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(7620);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"}))})}},e=>{e.O(0,[9664,7990,2536,9289,587,18,7358],()=>e(e.s=81066)),_N_E=e.O()}]);
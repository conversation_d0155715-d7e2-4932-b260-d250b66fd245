(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6636],{826:(e,t,s)=>{"use strict";s.d(t,{Yq:()=>l,cn:()=>i});var r=s(72902),a=s(13714),n=s(58054);function i(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"short",s="string"==typeof e?(0,n.Io)(e):e;return"short"===t?new Intl.DateTimeFormat("fr-MA",{day:"numeric",month:"short",year:"numeric"}).format(s):new Intl.DateTimeFormat("fr-MA",{weekday:"long",day:"numeric",month:"long",year:"numeric"}).format(s)}},1222:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(7620),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(e,t)=>{let s=(0,r.forwardRef)((s,n)=>{let{color:i="currentColor",size:l=24,strokeWidth:o=2,absoluteStrokeWidth:c,className:d="",children:u,...h}=s;return(0,r.createElement)("svg",{ref:n,...a,width:l,height:l,stroke:i,strokeWidth:c?24*Number(o)/Number(l):o,className:["lucide","lucide-".concat(e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim()),d].join(" "),...h},[...t.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(u)?u:[u]])});return s.displayName="".concat(e),s}},9013:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(1222).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},20564:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var r=s(54568),a=s(7620),n=s(4869),i=s(22995),l=s(81562),o=s(40790),c=s(1222);let d=(0,c.A)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);var u=s(26862);let h=(0,c.A)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),m=(0,c.A)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]),x=(0,c.A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),p=(0,c.A)("Pen",[["path",{d:"M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z",key:"5qss01"}]]);var b=s(9013),f=s(79535),g=s(56005),v=s(56178),y=s(70419),j=s(826);function N(){let e=(0,n.jE)(),{user:t}=(0,o.A)(),[s,c]=(0,a.useState)(!1),[N,w]=(0,a.useState)({}),[k,M]=(0,a.useState)("profile"),{data:C,isLoading:A}=(0,i.I)({queryKey:["user-profile",null==t?void 0:t.mbnbId],queryFn:async()=>{let e=await fetch("/api/v1/user/profile",{headers:{Authorization:"Bearer ".concat(localStorage.getItem("mbnb-token"))}});if(!e.ok)throw Error("Failed to fetch profile");return e.json()},enabled:!!(null==t?void 0:t.mbnbId)}),P=(0,l.n)({mutationFn:async e=>{let t=await fetch("/api/v1/user/profile",{method:"PATCH",headers:{Authorization:"Bearer ".concat(localStorage.getItem("mbnb-token")),"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to update profile");return t.json()},onSuccess:()=>{c(!1),e.invalidateQueries({queryKey:["user-profile"]})}}),E=(0,l.n)({mutationFn:async e=>{let t=new FormData;t.append("avatar",e);let s=await fetch("/api/v1/user/avatar",{method:"POST",headers:{Authorization:"Bearer ".concat(localStorage.getItem("mbnb-token"))},body:t});if(!s.ok)throw Error("Failed to upload avatar");return s.json()},onSuccess:()=>{e.invalidateQueries({queryKey:["user-profile"]})}}),S=e=>new Intl.NumberFormat("fr-MA",{style:"currency",currency:"MAD"}).format(e);return A||!C?(0,r.jsx)("div",{className:"min-h-screen bg-neutral-50 flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-pulse text-mbnb-coral",children:"Chargement du profil..."})}):(0,r.jsxs)("div",{className:"min-h-screen bg-neutral-50",children:[(0,r.jsxs)("div",{className:"relative h-64 bg-mbnb-navy",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,r.jsx)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full flex items-end pb-8",children:(0,r.jsxs)("div",{className:"flex items-end gap-6",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"w-32 h-32 rounded-full border-4 border-white overflow-hidden bg-white",children:C.avatar?(0,r.jsx)("img",{src:C.avatar,alt:"".concat(C.firstName," ").concat(C.lastName),className:"w-full h-full object-cover"}):(0,r.jsx)("div",{className:"w-full h-full flex items-center justify-center bg-mbnb-coral",children:(0,r.jsxs)("span",{className:"text-3xl font-bold text-white",children:[C.firstName[0],C.lastName[0]]})})}),(0,r.jsxs)("label",{className:"absolute bottom-0 right-0 p-2 bg-white rounded-full shadow-lg cursor-pointer hover:shadow-xl transition-shadow",children:[(0,r.jsx)(d,{className:"w-5 h-5 text-mbnb-coral"}),(0,r.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var t;let s=null==(t=e.target.files)?void 0:t[0];s&&E.mutate(s)},className:"hidden"})]})]}),(0,r.jsxs)("div",{className:"pb-2",children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-white flex items-center gap-2",children:[C.firstName," ",C.lastName,C.isSuperHost&&(0,r.jsxs)("span",{className:"px-2 py-1 bg-mbnb-coral text-white text-sm rounded-full flex items-center gap-1",children:[(0,r.jsx)(u.A,{className:"w-4 h-4",fill:"currentColor"}),"SuperHost"]})]}),(0,r.jsx)("p",{className:"text-white/90 mt-1",children:C.bio||"Membre Mbnb"}),(0,r.jsxs)("div",{className:"flex items-center gap-4 mt-2",children:[(0,r.jsxs)("span",{className:"text-white/80 text-sm",children:["Membre depuis ",(0,j.Yq)(C.joinedDate,"short")]}),C.location&&(0,r.jsxs)("span",{className:"text-white/80 text-sm flex items-center gap-1",children:[(0,r.jsx)(h,{className:"w-4 h-4"}),C.location]})]})]})]})})]}),(0,r.jsx)("div",{className:"bg-white border-b border-neutral-200",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsx)("div",{className:"flex gap-8",children:["profile","security","preferences","loyalty"].map(e=>(0,r.jsxs)("button",{onClick:()=>M(e),className:"py-4 px-1 border-b-2 font-medium text-sm transition-colors ".concat(k===e?"border-mbnb-coral text-mbnb-coral":"border-transparent text-neutral-500 hover:text-neutral-700"),children:["profile"===e&&"Profil","security"===e&&"S\xe9curit\xe9","preferences"===e&&"Pr\xe9f\xe9rences","loyalty"===e&&"Fid\xe9lit\xe9"]},e))})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:["profile"===k&&(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-card p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-mbnb-navy",children:"Informations personnelles"}),s?(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("button",{onClick:()=>{P.mutate(N)},className:"px-4 py-2 bg-mbnb-coral text-white rounded-lg hover:bg-mbnb-coral-dark",children:(0,r.jsx)(m,{className:"w-4 h-4"})}),(0,r.jsx)("button",{onClick:()=>{c(!1),w({})},className:"px-4 py-2 border border-neutral-300 rounded-lg hover:bg-neutral-50",children:(0,r.jsx)(x,{className:"w-4 h-4"})})]}):(0,r.jsxs)("button",{onClick:()=>{c(!0),w(C)},className:"px-4 py-2 border border-neutral-300 rounded-lg hover:bg-neutral-50 flex items-center gap-2",children:[(0,r.jsx)(p,{className:"w-4 h-4"}),"Modifier"]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-1",children:"Pr\xe9nom"}),s?(0,r.jsx)("input",{type:"text",value:N.firstName||"",onChange:e=>w({...N,firstName:e.target.value}),className:"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral"}):(0,r.jsx)("p",{className:"text-neutral-900",children:C.firstName})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-1",children:"Nom"}),s?(0,r.jsx)("input",{type:"text",value:N.lastName||"",onChange:e=>w({...N,lastName:e.target.value}),className:"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral"}):(0,r.jsx)("p",{className:"text-neutral-900",children:C.lastName})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-1",children:"Email"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("p",{className:"text-neutral-900",children:C.email}),C.emailVerified?(0,r.jsx)(b.A,{className:"w-4 h-4 text-mbnb-teal-dark"}):(0,r.jsx)(f.A,{className:"w-4 h-4 text-warning-500"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-1",children:"T\xe9l\xe9phone"}),s?(0,r.jsx)("input",{type:"tel",value:N.phone||"",onChange:e=>w({...N,phone:e.target.value}),className:"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral"}):(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("p",{className:"text-neutral-900",children:C.phone||"Non renseign\xe9"}),C.phone&&C.phoneVerified&&(0,r.jsx)(b.A,{className:"w-4 h-4 text-mbnb-teal-dark"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-1",children:"Localisation"}),s?(0,r.jsx)("input",{type:"text",value:N.location||"",onChange:e=>w({...N,location:e.target.value}),className:"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral"}):(0,r.jsx)("p",{className:"text-neutral-900",children:C.location||"Non renseign\xe9"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-1",children:"Langue pr\xe9f\xe9r\xe9e"}),s?(0,r.jsxs)("select",{value:N.preferredLanguage||"FR",onChange:e=>w({...N,preferredLanguage:e.target.value}),className:"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral",children:[(0,r.jsx)("option",{value:"FR",children:"Fran\xe7ais"}),(0,r.jsx)("option",{value:"AR",children:"العربية"}),(0,r.jsx)("option",{value:"AR-MA",children:"الدارجة"}),(0,r.jsx)("option",{value:"EN",children:"English"}),(0,r.jsx)("option",{value:"ES",children:"Espa\xf1ol"}),(0,r.jsx)("option",{value:"ZH",children:"中文"}),(0,r.jsx)("option",{value:"RU",children:"Русский"})]}):(0,r.jsx)("p",{className:"text-neutral-900",children:C.preferredLanguage})]}),(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-1",children:"Bio"}),s?(0,r.jsx)("textarea",{value:N.bio||"",onChange:e=>w({...N,bio:e.target.value}),rows:3,className:"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral"}):(0,r.jsx)("p",{className:"text-neutral-900",children:C.bio||"Parlez-nous de vous..."})]})]})]}),C.hostData&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-card p-6 mt-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-mbnb-navy mb-4",children:"Statistiques H\xf4te"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-mbnb-coral",children:C.hostData.propertyCount}),(0,r.jsx)("div",{className:"text-sm text-neutral-600",children:"Propri\xe9t\xe9s"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-mbnb-navy",children:C.hostData.averageRating.toFixed(1)}),(0,r.jsx)("div",{className:"text-sm text-neutral-600",children:"Note moyenne"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-mbnb-teal",children:[C.hostData.responseRate,"%"]}),(0,r.jsx)("div",{className:"text-sm text-neutral-600",children:"Taux r\xe9ponse"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-success-600",children:S(C.hostData.totalEarned)}),(0,r.jsx)("div",{className:"text-sm text-neutral-600",children:"Revenus totaux"})]})]})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-card p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-mbnb-navy mb-4",children:"V\xe9rifications"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(g.A,{className:"w-5 h-5 text-neutral-500"}),(0,r.jsx)("span",{className:"text-sm",children:"Email"})]}),C.emailVerified?(0,r.jsx)(b.A,{className:"w-5 h-5 text-mbnb-teal-dark"}):(0,r.jsx)("button",{className:"text-sm text-mbnb-coral hover:underline",children:"V\xe9rifier"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(v.A,{className:"w-5 h-5 text-neutral-500"}),(0,r.jsx)("span",{className:"text-sm",children:"T\xe9l\xe9phone"})]}),C.phoneVerified?(0,r.jsx)(b.A,{className:"w-5 h-5 text-mbnb-teal-dark"}):(0,r.jsx)("button",{className:"text-sm text-mbnb-coral hover:underline",children:"V\xe9rifier"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(y.A,{className:"w-5 h-5 text-neutral-500"}),(0,r.jsx)("span",{className:"text-sm",children:"Identit\xe9"})]}),C.identityVerified?(0,r.jsx)(b.A,{className:"w-5 h-5 text-mbnb-teal-dark"}):(0,r.jsx)("button",{className:"text-sm text-mbnb-coral hover:underline",children:"V\xe9rifier"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-card p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-mbnb-navy mb-4",children:"Activit\xe9"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-neutral-600",children:"R\xe9servations"}),(0,r.jsx)("span",{className:"font-medium",children:C.totalBookings})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-neutral-600",children:"Total d\xe9pens\xe9"}),(0,r.jsx)("span",{className:"font-medium",children:S(C.totalSpent)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-neutral-600",children:"Commission phase"}),(0,r.jsxs)("span",{className:"font-medium",children:["Phase ",C.commissionPhase]})]})]})]})]})]}),"loyalty"===k&&(0,r.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-card p-8",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"inline-block px-6 py-3 rounded-full text-white font-bold text-xl ".concat((e=>{switch(e){case"LEGENDE":return"bg-gradient-to-r from-mbnb-coral to-mbnb-coral-dark";case"AMBASSADEUR":return"bg-mbnb-navy";case"AVENTURIER":return"bg-mbnb-steel";default:return"bg-gradient-to-r from-neutral-400 to-neutral-500"}})(C.loyaltyTier)),children:C.loyaltyTier}),(0,r.jsx)("p",{className:"text-neutral-600 mt-2",children:"Votre niveau de fid\xe9lit\xe9 actuel"})]}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("span",{className:"text-sm text-neutral-600",children:[C.loyaltyPoints," points"]}),(0,r.jsxs)("span",{className:"text-sm text-neutral-600",children:[C.nextTierPoints," points"]})]}),(0,r.jsx)("div",{className:"w-full bg-neutral-200 rounded-full h-3",children:(0,r.jsx)("div",{className:"bg-mbnb-coral h-3 rounded-full transition-all duration-500",style:{width:"".concat(C?Math.min(C.loyaltyPoints/C.nextTierPoints*100,100):0,"%")}})}),(0,r.jsxs)("p",{className:"text-center text-sm text-neutral-600 mt-2",children:["Plus que ",C.nextTierPoints-C.loyaltyPoints," points pour le prochain niveau"]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"border border-neutral-200 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"font-semibold text-mbnb-navy mb-2",children:"Avantages actuels"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm text-neutral-600",children:[(0,r.jsxs)("li",{className:"flex items-center gap-2",children:[(0,r.jsx)(b.A,{className:"w-4 h-4 text-mbnb-teal-dark"}),"R\xe9ductions exclusives"]}),(0,r.jsxs)("li",{className:"flex items-center gap-2",children:[(0,r.jsx)(b.A,{className:"w-4 h-4 text-mbnb-teal-dark"}),"Support prioritaire"]}),(0,r.jsxs)("li",{className:"flex items-center gap-2",children:[(0,r.jsx)(b.A,{className:"w-4 h-4 text-mbnb-teal-dark"}),"Acc\xe8s anticip\xe9 aux nouveaut\xe9s"]})]})]}),(0,r.jsxs)("div",{className:"border border-neutral-200 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"font-semibold text-mbnb-navy mb-2",children:"Comment gagner des points"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm text-neutral-600",children:[(0,r.jsx)("li",{children:"• 10 points par r\xe9servation"}),(0,r.jsx)("li",{children:"• 5 points par avis laiss\xe9"}),(0,r.jsx)("li",{children:"• 20 points par parrainage"}),(0,r.jsx)("li",{children:"• Points bonus \xe9v\xe9nements sp\xe9ciaux"})]})]})]})]})})]})]})}},26862:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(1222).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},40730:(e,t,s)=>{"use strict";s.d(t,{$:()=>l,s:()=>i});var r=s(95635),a=s(68458),n=s(28306),i=class extends a.k{#e;#t;#s;constructor(e){super(),this.mutationId=e.mutationId,this.#t=e.mutationCache,this.#e=[],this.state=e.state||l(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#e.includes(e)||(this.#e.push(e),this.clearGcTimeout(),this.#t.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#e=this.#e.filter(t=>t!==e),this.scheduleGc(),this.#t.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#e.length||("pending"===this.state.status?this.scheduleGc():this.#t.remove(this))}continue(){return this.#s?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#r({type:"continue"})};this.#s=(0,n.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#r({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#r({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#t.canRun(this)});let s="pending"===this.state.status,r=!this.#s.canStart();try{if(s)t();else{this.#r({type:"pending",variables:e,isPaused:r}),await this.#t.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#r({type:"pending",context:t,variables:e,isPaused:r})}let a=await this.#s.start();return await this.#t.config.onSuccess?.(a,e,this.state.context,this),await this.options.onSuccess?.(a,e,this.state.context),await this.#t.config.onSettled?.(a,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(a,null,e,this.state.context),this.#r({type:"success",data:a}),a}catch(t){try{throw await this.#t.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#t.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#r({type:"error",error:t})}}finally{this.#t.runNext(this)}}#r(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),r.jG.batch(()=>{this.#e.forEach(t=>{t.onMutationUpdate(e)}),this.#t.notify({mutation:this,type:"updated",action:e})})}};function l(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},40790:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(21367),a=s(50848),n=s(15263);let i=(0,r.vt)()((0,a.Zr)((0,n.D)((e,t)=>({user:null,token:null,refreshToken:null,isAuthenticated:!1,isLoading:!1,error:null,heritageInterests:[],activityPreferences:[],preferredLanguage:"fr",preferredCurrency:"MAD",loginToMbnb:async t=>{e(e=>{e.isLoading=!0,e.error=null});try{let s=await fetch("".concat("http://localhost:3001/api/v1","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json","X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(t)});if(!s.ok){let e=await s.json();throw Error(e.message||"Authentification Mbnb \xe9chou\xe9e")}let r=await s.json();e(e=>{e.user=r.user,e.token=r.token,e.refreshToken=r.refreshToken||null,e.isAuthenticated=!0,e.isLoading=!1,e.preferredLanguage=r.user.profile.preferredLanguage,e.preferredCurrency=r.user.profile.preferredCurrency})}catch(s){let t=s instanceof Error?s.message:"Erreur de connexion Mbnb";throw e(e=>{e.error=t,e.isLoading=!1}),s}},registerToMbnb:async t=>{e(e=>{e.isLoading=!0,e.error=null});try{let s=await fetch("".concat("http://localhost:3001/api/v1","/auth/register"),{method:"POST",headers:{"Content-Type":"application/json","X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(t)});if(!s.ok){let e=await s.json();throw Error(e.message||"Inscription Mbnb \xe9chou\xe9e")}let r=await s.json();e(e=>{e.user=r.user,e.token=r.token,e.refreshToken=r.refreshToken||null,e.isAuthenticated=!0,e.isLoading=!1,e.preferredLanguage=r.user.profile.preferredLanguage,e.preferredCurrency=r.user.profile.preferredCurrency})}catch(s){let t=s instanceof Error?s.message:"Erreur d'inscription Mbnb";throw e(e=>{e.error=t,e.isLoading=!1}),s}},logoutFromMbnb:()=>{e(e=>{e.user=null,e.token=null,e.refreshToken=null,e.isAuthenticated=!1,e.error=null,e.heritageInterests=[],e.activityPreferences=[],e.preferredLanguage="fr",e.preferredCurrency="MAD"}),localStorage.removeItem("mbnb-auth-storage"),window.location.href="/"},refreshMbnbToken:async()=>{let s=t().refreshToken;if(!s)return void t().logoutFromMbnb();try{let t=await fetch("".concat("http://localhost:3001/api/v1","/auth/refresh"),{method:"POST",headers:{"Content-Type":"application/json","X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify({refreshToken:s})});if(!t.ok)throw Error("Refresh token Mbnb expir\xe9");let r=await t.json();e(e=>{e.token=r.token,e.refreshToken=r.refreshToken})}catch(e){t().logoutFromMbnb()}},updateMbnbProfile:async s=>{e(e=>{e.isLoading=!0,e.error=null});try{let r=await fetch("".concat("http://localhost:3001/api/v1","/users/profile"),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t().token),"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(s)});if(!r.ok){let e=await r.json();throw Error(e.message||"Mise \xe0 jour profil Mbnb \xe9chou\xe9e")}let a=await r.json();e(e=>{e.user=a,e.isLoading=!1,e.preferredLanguage=a.profile.preferredLanguage,e.preferredCurrency=a.profile.preferredCurrency})}catch(s){let t=s instanceof Error?s.message:"Erreur mise \xe0 jour profil Mbnb";throw e(e=>{e.error=t,e.isLoading=!1}),s}},updateMbnbPreferences:async s=>{e(e=>{e.isLoading=!0,e.error=null});try{let r=await fetch("".concat("http://localhost:3001/api/v1","/users/preferences"),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t().token),"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(s)});if(!r.ok){let e=await r.json();throw Error(e.message||"Mise \xe0 jour pr\xe9f\xe9rences Mbnb \xe9chou\xe9e")}e(e=>{s.heritageInterests&&(e.heritageInterests=s.heritageInterests),s.activityPreferences&&(e.activityPreferences=s.activityPreferences),s.preferredLanguage&&(e.preferredLanguage=s.preferredLanguage),s.preferredCurrency&&(e.preferredCurrency=s.preferredCurrency),e.isLoading=!1})}catch(s){let t=s instanceof Error?s.message:"Erreur mise \xe0 jour pr\xe9f\xe9rences Mbnb";throw e(e=>{e.error=t,e.isLoading=!1}),s}},verifyMbnbEmail:async s=>{let r=await fetch("".concat("http://localhost:3001/api/v1","/auth/verify-email"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t().token),"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify({code:s})});if(!r.ok)throw Error((await r.json()).message||"V\xe9rification email Mbnb \xe9chou\xe9e");e(e=>{e.user&&(e.user.verification.emailVerified=!0)})},verifyMbnbPhone:async s=>{let r=await fetch("".concat("http://localhost:3001/api/v1","/auth/verify-phone"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t().token),"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify({code:s})});if(!r.ok)throw Error((await r.json()).message||"V\xe9rification t\xe9l\xe9phone Mbnb \xe9chou\xe9e");e(e=>{e.user&&(e.user.verification.phoneVerified=!0)})},clearError:()=>e(e=>{e.error=null})})),{name:"mbnb-auth-storage",storage:(0,a.KU)(()=>localStorage),partialize:e=>({user:e.user,token:e.token,refreshToken:e.refreshToken,isAuthenticated:e.isAuthenticated,heritageInterests:e.heritageInterests,activityPreferences:e.activityPreferences,preferredLanguage:e.preferredLanguage,preferredCurrency:e.preferredCurrency})}))},56005:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(1222).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},56178:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(1222).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},58054:(e,t,s)=>{"use strict";function r(){return new Date}function a(e){return"string"==typeof e?new Date(e):e}function n(e,t){let s=new Date(e.getTime());return s.setDate(s.getDate()+t),s}s.d(t,{Io:()=>a,MZ:()=>r,fi:()=>n})},70419:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(1222).A)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},77665:(e,t,s)=>{Promise.resolve().then(s.bind(s,20564))},79535:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(1222).A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},81562:(e,t,s)=>{"use strict";s.d(t,{n:()=>d});var r=s(7620),a=s(40730),n=s(95635),i=s(22844),l=s(69950),o=class extends i.Q{#a;#n=void 0;#i;#l;constructor(e,t){super(),this.#a=e,this.setOptions(t),this.bindMethods(),this.#o()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#a.defaultMutationOptions(e),(0,l.f8)(this.options,t)||this.#a.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#i,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,l.EN)(t.mutationKey)!==(0,l.EN)(this.options.mutationKey)?this.reset():this.#i?.state.status==="pending"&&this.#i.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#i?.removeObserver(this)}onMutationUpdate(e){this.#o(),this.#c(e)}getCurrentResult(){return this.#n}reset(){this.#i?.removeObserver(this),this.#i=void 0,this.#o(),this.#c()}mutate(e,t){return this.#l=t,this.#i?.removeObserver(this),this.#i=this.#a.getMutationCache().build(this.#a,this.options),this.#i.addObserver(this),this.#i.execute(e)}#o(){let e=this.#i?.state??(0,a.$)();this.#n={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#c(e){n.jG.batch(()=>{if(this.#l&&this.hasListeners()){let t=this.#n.variables,s=this.#n.context;e?.type==="success"?(this.#l.onSuccess?.(e.data,t,s),this.#l.onSettled?.(e.data,null,t,s)):e?.type==="error"&&(this.#l.onError?.(e.error,t,s),this.#l.onSettled?.(void 0,e.error,t,s))}this.listeners.forEach(e=>{e(this.#n)})})}},c=s(4869);function d(e,t){let s=(0,c.jE)(t),[a]=r.useState(()=>new o(s,e));r.useEffect(()=>{a.setOptions(e)},[a,e]);let i=r.useSyncExternalStore(r.useCallback(e=>a.subscribe(n.jG.batchCalls(e)),[a]),()=>a.getCurrentResult(),()=>a.getCurrentResult()),d=r.useCallback((e,t)=>{a.mutate(e,t).catch(l.lQ)},[a]);if(i.error&&(0,l.GU)(a.options.throwOnError,[i.error]))throw i.error;return{...i,mutate:d,mutateAsync:i.mutate}}}},e=>{e.O(0,[6799,2995,7990,9289,587,18,7358],()=>e(e.s=77665)),_N_E=e.O()}]);
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2990],{32208:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(54568),a=t(7620);function n(){let[e,s]=(0,a.useState)("Testing..."),[t,n]=(0,a.useState)(null),[l,o]=(0,a.useState)(null);return(0,a.useEffect)(()=>{(async()=>{try{console.log("Testing API connection to:","http://localhost:3001/api/v1");let e=await fetch("http://localhost:3001/api/v1/activities/top100",{method:"GET",headers:{"Content-Type":"application/json","X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0.0","X-Mbnb-Client":"frontend-next"}});if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));let t=await e.json();n(t),s("✅ SUCCESS: Received ".concat(t.total," activities"))}catch(e){console.error("API Test failed:",e),o(e instanceof Error?e.message:"Unknown error"),s("❌ FAILED")}})()},[]),(0,r.jsx)("div",{className:"min-h-screen bg-gray-100 py-12 px-4",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mb-8",children:"API Test Page"}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Connection Status"}),(0,r.jsx)("p",{className:"text-lg",children:e}),(0,r.jsxs)("div",{className:"mt-4 p-4 bg-gray-50 rounded",children:[(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,r.jsx)("strong",{children:"API URL:"})," ","http://localhost:3001/api/v1"]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,r.jsx)("strong",{children:"Direct URL:"})," http://localhost:3001/api/v1"]})]})]}),l&&(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:[(0,r.jsx)("h3",{className:"text-red-800 font-semibold",children:"Error Details:"}),(0,r.jsx)("p",{className:"text-red-600 mt-2",children:l})]}),t&&(0,r.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"text-green-800 font-semibold",children:"Success! Data received:"}),(0,r.jsxs)("pre",{className:"mt-2 text-xs overflow-auto",children:[JSON.stringify(t,null,2).substring(0,500),"..."]})]})]})})}},65257:(e,s,t)=>{Promise.resolve().then(t.bind(t,32208))}},e=>{e.O(0,[587,18,7358],()=>e(e.s=65257)),_N_E=e.O()}]);
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2959],{21350:(e,t,s)=>{Promise.resolve().then(s.bind(s,60417))},27541:(e,t,s)=>{"use strict";var a=s(43041);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},40730:(e,t,s)=>{"use strict";s.d(t,{$:()=>c,s:()=>n});var a=s(95635),i=s(68458),r=s(28306),n=class extends i.k{#e;#t;#s;constructor(e){super(),this.mutationId=e.mutationId,this.#t=e.mutationCache,this.#e=[],this.state=e.state||c(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#e.includes(e)||(this.#e.push(e),this.clearGcTimeout(),this.#t.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#e=this.#e.filter(t=>t!==e),this.scheduleGc(),this.#t.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#e.length||("pending"===this.state.status?this.scheduleGc():this.#t.remove(this))}continue(){return this.#s?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#a({type:"continue"})};this.#s=(0,r.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#a({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#a({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#t.canRun(this)});let s="pending"===this.state.status,a=!this.#s.canStart();try{if(s)t();else{this.#a({type:"pending",variables:e,isPaused:a}),await this.#t.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#a({type:"pending",context:t,variables:e,isPaused:a})}let i=await this.#s.start();return await this.#t.config.onSuccess?.(i,e,this.state.context,this),await this.options.onSuccess?.(i,e,this.state.context),await this.#t.config.onSettled?.(i,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(i,null,e,this.state.context),this.#a({type:"success",data:i}),i}catch(t){try{throw await this.#t.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#t.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#a({type:"error",error:t})}}finally{this.#t.runNext(this)}}#a(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),a.jG.batch(()=>{this.#e.forEach(t=>{t.onMutationUpdate(e)}),this.#t.notify({mutation:this,type:"updated",action:e})})}};function c(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},55912:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(7620);let i=a.forwardRef(function(e,t){let{title:s,titleId:i,...r}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},r),s?a.createElement("title",{id:i},s):null,a.createElement("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.006 5.404.434c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.434 2.082-5.005Z",clipRule:"evenodd"}))})},60417:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var a=s(54568),i=s(7620),r=s(27541),n=s(74951);let c=i.forwardRef(function(e,t){let{title:s,titleId:a,...r}=e;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},r),s?i.createElement("title",{id:a},s):null,i.createElement("path",{fillRule:"evenodd",d:"m11.54 22.351.07.04.028.016a.76.76 0 0 0 .723 0l.028-.015.071-.041a16.975 16.975 0 0 0 1.144-.742 19.58 19.58 0 0 0 2.683-2.282c1.944-1.99 3.963-4.98 3.963-8.827a8.25 8.25 0 0 0-16.5 0c0 3.846 2.02 6.837 3.963 8.827a19.58 19.58 0 0 0 2.682 2.282 16.975 16.975 0 0 0 1.145.742ZM12 13.5a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z",clipRule:"evenodd"}))}),o=i.forwardRef(function(e,t){let{title:s,titleId:a,...r}=e;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},r),s?i.createElement("title",{id:a},s):null,i.createElement("path",{fillRule:"evenodd",d:"M6.75 2.25A.75.75 0 0 1 7.5 3v1.5h9V3A.75.75 0 0 1 18 3v1.5h.75a3 3 0 0 1 3 3v11.25a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V7.5a3 3 0 0 1 3-3H6V3a.75.75 0 0 1 .75-.75Zm13.5 9a1.5 1.5 0 0 0-1.5-1.5H5.25a1.5 1.5 0 0 0-1.5 1.5v7.5a1.5 1.5 0 0 0 1.5 1.5h13.5a1.5 1.5 0 0 0 1.5-1.5v-7.5Z",clipRule:"evenodd"}))});var l=s(69514),u=s(99666),d=s(55912),m=s(62639),h=s(6487);function p(){let e=(0,r.useSearchParams)(),[t,s]=(0,i.useState)([]),p=(0,h.Cu)(),x=e.get("location"),b=e.get("checkIn"),g=e.get("checkOut"),f=e.get("guests"),w=e.get("adults"),v=e.get("children"),j=e.get("infants"),y=e.get("type"),N={location:x||"",checkIn:b||"",checkOut:g||"",adults:parseInt(w||"2"),children:parseInt(v||"0"),infants:parseInt(j||"0"),type:y||"properties"},{data:_,isLoading:S}=(0,h.AW)(N),C=(null==_?void 0:_.properties)||[];return S?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 border-4 border-mbnb-coral border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Recherche en cours..."})]})}):(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"mb-6 p-4 bg-white rounded-xl shadow-sm border border-gray-100",children:[(0,a.jsxs)("h1",{className:"text-2xl font-bold mb-3 text-gray-900",children:[C.length," ","activities"===y?"activit\xe9s":"h\xe9bergements"," trouv\xe9s"]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-4 text-sm",children:[x&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(c,{className:"w-4 h-4 text-mbnb-coral"}),(0,a.jsx)("span",{className:"font-medium",children:"Destination:"})," ",x]}),b&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(o,{className:"w-4 h-4 text-mbnb-coral"}),(0,a.jsx)("span",{className:"font-medium",children:"Arriv\xe9e:"})," ",b]}),g&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(o,{className:"w-4 h-4 text-mbnb-coral"}),(0,a.jsx)("span",{className:"font-medium",children:"D\xe9part:"})," ",g]}),(f||w||v||j)&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(l.A,{className:"w-4 h-4 text-mbnb-coral"}),(0,a.jsx)("span",{className:"font-medium",children:"Voyageurs:"}),w||v||j?(0,a.jsxs)("span",{children:[w&&Number(w)>0&&"".concat(w," adulte").concat(Number(w)>1?"s":""),v&&Number(v)>0&&"".concat(w&&Number(w)>0?", ":"").concat(v," enfant").concat(Number(v)>1?"s":""),j&&Number(j)>0&&"".concat(w&&Number(w)>0||v&&Number(v)>0?", ":"").concat(j," b\xe9b\xe9").concat(Number(j)>1?"s":"")]}):f]})]})]}),C.length>0?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:C.map(e=>{var i,r;return(0,a.jsxs)("div",{className:"group cursor-pointer",children:[(0,a.jsxs)("div",{className:"relative aspect-[4/3] rounded-2xl overflow-hidden mb-3",children:[(0,a.jsx)(n.XF,{baseName:(null==(i=e.media.images[0])?void 0:i.baseName)||"architecture_maroc_riad_bleu_majorelle_001",folder:(null==(r=e.media.images[0])?void 0:r.folder)||"impact-eleve/ACTE_1_EVEIL",alt:e.title,className:"object-cover group-hover:scale-110 transition-transform duration-500"}),(0,a.jsx)("button",{onClick:()=>(e=>{let a=t.includes(e);p.mutate({propertyId:e,isFavorite:a},{onSuccess:()=>{s(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])}})})(e.mbnbId),className:"absolute top-3 right-3 p-2 rounded-full bg-white/90 hover:bg-white transition-colors",children:t.includes(e.mbnbId)?(0,a.jsx)(u.A,{className:"w-5 h-5 text-mbnb-coral"}):(0,a.jsx)(m.A,{className:"w-5 h-5 text-gray-700"})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-1",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 group-hover:text-mbnb-coral transition-colors line-clamp-2 flex-1",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center ml-2",children:[(0,a.jsx)(d.A,{className:"w-4 h-4 text-mbnb-coral"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900 ml-1",children:e.ratings.overall}),(0,a.jsxs)("span",{className:"text-sm text-gray-500 ml-1",children:["(",e.ratings.totalReviews,")"]})]})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:[e.location.city,", ",e.location.region]}),(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-500 mb-2",children:[(0,a.jsxs)("span",{children:[e.specifications.guests," voyageurs"]}),(0,a.jsx)("span",{className:"mx-2",children:"\xb7"}),(0,a.jsxs)("span",{children:[e.specifications.bedrooms," chambres"]}),(0,a.jsx)("span",{className:"mx-2",children:"\xb7"}),(0,a.jsxs)("span",{children:[e.specifications.bathrooms," SDB"]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-baseline",children:[(0,a.jsxs)("span",{className:"text-lg font-bold text-gray-900",children:[e.pricing.basePrice.toLocaleString()," ",e.pricing.currency]}),(0,a.jsx)("span",{className:"text-sm text-gray-500 ml-1",children:"/ nuit"})]}),(0,a.jsx)("a",{href:"/reservation/".concat(e.mbnbId,"?checkIn=").concat(b||"","&checkOut=").concat(g||"","&adults=").concat(w||"2","&children=").concat(v||"0","&infants=").concat(j||"0"),className:"bg-mbnb-coral hover:bg-mbnb-coral-dark text-white px-4 py-2 rounded-lg font-medium transition-colors",children:"R\xe9server"})]})]})]},e.mbnbId)})}):(0,a.jsxs)("div",{className:"text-center py-12 bg-white rounded-xl shadow-sm",children:[(0,a.jsx)(c,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,a.jsxs)("p",{className:"text-xl text-gray-600 mb-2",children:['Aucun r\xe9sultat trouv\xe9 pour "',x,'"']}),(0,a.jsx)("p",{className:"text-gray-500",children:"Essayez de modifier vos crit\xe8res de recherche"})]})]})})}function x(){return(0,a.jsx)(i.Suspense,{fallback:(0,a.jsx)("div",{className:"flex items-center justify-center h-screen",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 border-4 border-mbnb-coral border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Chargement des r\xe9sultats..."})]})}),children:(0,a.jsx)(p,{})})}},62205:(e,t,s)=>{"use strict";s.d(t,{MbnbImageGallery:()=>c,MbnbResponsiveImage:()=>r,useMbnbImagePreload:()=>n});var a=s(54568),i=s(7620);function r(e){let{baseName:t,folder:s,alt:i,className:r="",priority:n=!1,onClick:c}=e,o="/Storytelling/optimized/".concat(s,"/").concat(t);return(0,a.jsxs)("picture",{className:r,onClick:c,children:[(0,a.jsx)("source",{srcSet:"".concat(o,"_mobile_375w.avif"),media:"(max-width: 375px)",type:"image/avif"}),(0,a.jsx)("source",{srcSet:"".concat(o,"_mobile_375w.webp"),media:"(max-width: 375px)",type:"image/webp"}),(0,a.jsx)("source",{srcSet:"".concat(o,"_mobile_375w.jpg"),media:"(max-width: 375px)",type:"image/jpeg"}),(0,a.jsx)("source",{srcSet:"".concat(o,"_mobile_414w.avif"),media:"(max-width: 414px)",type:"image/avif"}),(0,a.jsx)("source",{srcSet:"".concat(o,"_mobile_414w.webp"),media:"(max-width: 414px)",type:"image/webp"}),(0,a.jsx)("source",{srcSet:"".concat(o,"_mobile_414w.jpg"),media:"(max-width: 414px)",type:"image/jpeg"}),(0,a.jsx)("source",{srcSet:"".concat(o,"_mobile_480w.avif"),media:"(max-width: ".concat(480,"px)"),type:"image/avif"}),(0,a.jsx)("source",{srcSet:"".concat(o,"_mobile_480w.webp"),media:"(max-width: ".concat(480,"px)"),type:"image/webp"}),(0,a.jsx)("source",{srcSet:"".concat(o,"_mobile_480w.jpg"),media:"(max-width: ".concat(480,"px)"),type:"image/jpeg"}),(0,a.jsx)("source",{srcSet:"".concat(o,"_tablet_768w.avif"),media:"(max-width: 768px)",type:"image/avif"}),(0,a.jsx)("source",{srcSet:"".concat(o,"_tablet_768w.webp"),media:"(max-width: 768px)",type:"image/webp"}),(0,a.jsx)("source",{srcSet:"".concat(o,"_tablet_768w.jpg"),media:"(max-width: 768px)",type:"image/jpeg"}),(0,a.jsx)("source",{srcSet:"".concat(o,"_tablet_834w.avif"),media:"(max-width: 834px)",type:"image/avif"}),(0,a.jsx)("source",{srcSet:"".concat(o,"_tablet_834w.webp"),media:"(max-width: 834px)",type:"image/webp"}),(0,a.jsx)("source",{srcSet:"".concat(o,"_tablet_834w.jpg"),media:"(max-width: 834px)",type:"image/jpeg"}),(0,a.jsx)("source",{srcSet:"".concat(o,"_tablet_1024w.avif"),media:"(max-width: ".concat(1024,"px)"),type:"image/avif"}),(0,a.jsx)("source",{srcSet:"".concat(o,"_tablet_1024w.webp"),media:"(max-width: ".concat(1024,"px)"),type:"image/webp"}),(0,a.jsx)("source",{srcSet:"".concat(o,"_tablet_1024w.jpg"),media:"(max-width: ".concat(1024,"px)"),type:"image/jpeg"}),(0,a.jsx)("source",{srcSet:"".concat(o,"_desktop_1200w.avif"),media:"(max-width: 1200px)",type:"image/avif"}),(0,a.jsx)("source",{srcSet:"".concat(o,"_desktop_1200w.webp"),media:"(max-width: 1200px)",type:"image/webp"}),(0,a.jsx)("source",{srcSet:"".concat(o,"_desktop_1200w.jpg"),media:"(max-width: 1200px)",type:"image/jpeg"}),(0,a.jsx)("source",{srcSet:"".concat(o,"_desktop_1440w.avif"),media:"(max-width: 1440px)",type:"image/avif"}),(0,a.jsx)("source",{srcSet:"".concat(o,"_desktop_1440w.webp"),media:"(max-width: 1440px)",type:"image/webp"}),(0,a.jsx)("source",{srcSet:"".concat(o,"_desktop_1440w.jpg"),media:"(max-width: 1440px)",type:"image/jpeg"}),(0,a.jsx)("source",{srcSet:"".concat(o,"_desktop_1920w.avif"),type:"image/avif"}),(0,a.jsx)("source",{srcSet:"".concat(o,"_desktop_1920w.webp"),type:"image/webp"}),(0,a.jsx)("img",{src:"".concat(o,"_desktop_1920w.jpg"),alt:i,loading:n?"eager":"lazy",className:"".concat(r," w-full"),decoding:"async"})]})}function n(e,t){i.useEffect(()=>{let s=window.innerWidth<=480,a=window.innerWidth>480&&window.innerWidth<=1024,i="/Storytelling/optimized/".concat(t,"/").concat(e);(s?["".concat(i,"_mobile_414w.avif"),"".concat(i,"_mobile_414w.webp")]:a?["".concat(i,"_tablet_834w.avif"),"".concat(i,"_tablet_834w.webp")]:["".concat(i,"_desktop_1440w.avif"),"".concat(i,"_desktop_1440w.webp")]).forEach(e=>{let t=document.createElement("link");t.rel="preload",t.as="image",t.href=e,document.head.appendChild(t)})},[e,t])}function c(e){let{images:t,className:s=""}=e,[n,c]=i.useState(null);return(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 ".concat(s),children:[t.map((e,t)=>(0,a.jsxs)("div",{className:"relative group cursor-pointer overflow-hidden rounded-lg",onClick:()=>c(t),children:[(0,a.jsx)(r,{baseName:e.baseName,folder:e.folder,alt:e.alt,className:"transition-transform duration-300 group-hover:scale-110",priority:t<4}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity duration-300"})]},"".concat(e.folder,"-").concat(e.baseName))),null!==n&&(0,a.jsx)("div",{className:"fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center p-4",onClick:()=>c(null),children:(0,a.jsx)(r,{baseName:t[n].baseName,folder:t[n].folder,alt:t[n].alt,className:"max-w-full max-h-full object-contain",priority:!0})})]})}},62639:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(7620);let i=a.forwardRef(function(e,t){let{title:s,titleId:i,...r}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},r),s?a.createElement("title",{id:i},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12Z"}))})},69514:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(7620);let i=a.forwardRef(function(e,t){let{title:s,titleId:i,...r}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},r),s?a.createElement("title",{id:i},s):null,a.createElement("path",{d:"M4.5 6.375a4.125 4.125 0 1 1 8.25 0 4.125 4.125 0 0 1-8.25 0ZM14.25 8.625a3.375 3.375 0 1 1 6.75 0 3.375 3.375 0 0 1-6.75 0ZM1.5 19.125a7.125 7.125 0 0 1 14.25 0v.003l-.001.119a.75.75 0 0 1-.363.63 13.067 13.067 0 0 1-6.761 1.873c-2.472 0-4.786-.684-6.76-1.873a.75.75 0 0 1-.364-.63l-.001-.122ZM17.25 19.128l-.001.144a2.25 2.25 0 0 1-.233.96 10.088 10.088 0 0 0 5.06-1.01.75.75 0 0 0 .42-.643 4.875 4.875 0 0 0-6.957-4.611 8.586 8.586 0 0 1 1.71 5.157v.003Z"}))})},74951:(e,t,s)=>{"use strict";s.d(t,{XF:()=>a.MbnbResponsiveImage});var a=s(62205)},81562:(e,t,s)=>{"use strict";s.d(t,{n:()=>u});var a=s(7620),i=s(40730),r=s(95635),n=s(22844),c=s(69950),o=class extends n.Q{#i;#r=void 0;#n;#c;constructor(e,t){super(),this.#i=e,this.setOptions(t),this.bindMethods(),this.#o()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#i.defaultMutationOptions(e),(0,c.f8)(this.options,t)||this.#i.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#n,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,c.EN)(t.mutationKey)!==(0,c.EN)(this.options.mutationKey)?this.reset():this.#n?.state.status==="pending"&&this.#n.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#n?.removeObserver(this)}onMutationUpdate(e){this.#o(),this.#l(e)}getCurrentResult(){return this.#r}reset(){this.#n?.removeObserver(this),this.#n=void 0,this.#o(),this.#l()}mutate(e,t){return this.#c=t,this.#n?.removeObserver(this),this.#n=this.#i.getMutationCache().build(this.#i,this.options),this.#n.addObserver(this),this.#n.execute(e)}#o(){let e=this.#n?.state??(0,i.$)();this.#r={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#l(e){r.jG.batch(()=>{if(this.#c&&this.hasListeners()){let t=this.#r.variables,s=this.#r.context;e?.type==="success"?(this.#c.onSuccess?.(e.data,t,s),this.#c.onSettled?.(e.data,null,t,s)):e?.type==="error"&&(this.#c.onError?.(e.error,t,s),this.#c.onSettled?.(void 0,e.error,t,s))}this.listeners.forEach(e=>{e(this.#r)})})}},l=s(4869);function u(e,t){let s=(0,l.jE)(t),[i]=a.useState(()=>new o(s,e));a.useEffect(()=>{i.setOptions(e)},[i,e]);let n=a.useSyncExternalStore(a.useCallback(e=>i.subscribe(r.jG.batchCalls(e)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),u=a.useCallback((e,t)=>{i.mutate(e,t).catch(c.lQ)},[i]);if(n.error&&(0,c.GU)(i.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:u,mutateAsync:n.mutate}}},99666:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(7620);let i=a.forwardRef(function(e,t){let{title:s,titleId:i,...r}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},r),s?a.createElement("title",{id:i},s):null,a.createElement("path",{d:"m11.645 20.91-.007-.003-.022-.012a15.247 15.247 0 0 1-.383-.218 25.18 25.18 0 0 1-4.244-3.17C4.688 15.36 2.25 12.174 2.25 8.25 2.25 5.322 4.714 3 7.688 3A5.5 5.5 0 0 1 12 5.052 5.5 5.5 0 0 1 16.313 3c2.973 0 5.437 2.322 5.437 5.25 0 3.925-2.438 7.111-4.739 9.256a25.175 25.175 0 0 1-4.244 3.17 15.247 15.247 0 0 1-.383.219l-.022.012-.007.004-.003.001a.752.752 0 0 1-.704 0l-.003-.001Z"}))})}},e=>{e.O(0,[6799,2995,3558,6487,587,18,7358],()=>e(e.s=21350)),_N_E=e.O()}]);
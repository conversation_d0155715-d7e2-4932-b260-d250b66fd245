(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4859],{59009:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>b});var t=s(54568),l=s(7620),a=s(27541),n=s(19664),o=s.n(n),i=s(40790),c=s(52669),d=s(99051),m=s(93558);function b(){let e=(0,a.useRouter)(),{loginToMbnb:r,isLoading:s,error:n,clearError:b}=(0,i.A)(),[x,u]=(0,l.useState)({email:"",password:"",rememberMe:!1}),[h,p]=(0,l.useState)(!1),v=(e,r)=>{u(s=>({...s,[e]:r})),n&&b()},f=async s=>{if(s.preventDefault(),!x.email||!x.password)return void m.oR.error("Email et mot de passe requis");try{await r(x),m.oR.success("Connexion r\xe9ussie - Bienvenue sur Mbnb!"),e.push("/")}catch(r){let e=r instanceof Error?r.message:"Erreur de connexion";m.oR.error(e)}};return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-mbnb-sky/5 via-white to-mbnb-teal/5 flex items-center justify-center px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"mx-auto h-24 w-24 rounded-full bg-mbnb-coral flex items-center justify-center shadow-lg",children:(0,t.jsx)("div",{className:"text-white text-4xl font-bold",children:"M"})}),(0,t.jsx)("h2",{className:"mt-6 text-center text-3xl font-bold text-mbnb-navy",children:"Connectez-vous \xe0 Mbnb"}),(0,t.jsx)("p",{className:"mt-2 text-center text-sm text-mbnb-steel",children:"D\xe9couvrez le patrimoine authentique du Maroc"})]}),(0,t.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:f,children:[(0,t.jsx)("div",{className:"bg-white rounded-xl shadow-card p-8 border border-gray-100",children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-mbnb-navy mb-2",children:"Adresse email"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(d.A,{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"appearance-none relative block w-full px-4 py-3 border border-gray-200 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-mbnb-coral focus:border-mbnb-coral transition-colors duration-200",placeholder:"<EMAIL>",value:x.email,onChange:e=>v("email",e.target.value),disabled:s}),(0,t.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:(0,t.jsx)("svg",{className:"h-5 w-5 text-mbnb-steel/40",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"})})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-mbnb-navy mb-2",children:"Mot de passe"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(d.A,{id:"password",name:"password",type:h?"text":"password",autoComplete:"current-password",required:!0,className:"appearance-none relative block w-full px-4 py-3 pr-12 border border-gray-200 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-mbnb-coral focus:border-mbnb-coral transition-colors duration-200",placeholder:"Votre mot de passe",value:x.password,onChange:e=>v("password",e.target.value),disabled:s}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>p(!h),children:h?(0,t.jsx)("svg",{className:"h-5 w-5 text-mbnb-steel hover:text-mbnb-coral transition-colors cursor-pointer",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464m1.414 1.414L8.464 8.464m5.656 5.656L15.536 15.536m-1.414-1.414L15.536 15.536"})}):(0,t.jsxs)("svg",{className:"h-5 w-5 text-mbnb-steel hover:text-mbnb-coral transition-colors cursor-pointer",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-mbnb-coral focus:ring-mbnb-coral border-gray-300 rounded transition-colors",checked:x.rememberMe,onChange:e=>v("rememberMe",e.target.checked)}),(0,t.jsx)("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-mbnb-steel",children:"Se souvenir de moi"})]}),(0,t.jsx)("div",{className:"text-sm",children:(0,t.jsx)(o(),{href:"/auth/forgot-password",className:"font-medium text-mbnb-coral hover:text-mbnb-teal transition-colors",children:"Mot de passe oubli\xe9?"})})]}),n&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,t.jsx)("div",{className:"ml-3",children:(0,t.jsx)("p",{className:"text-sm text-red-700",children:n})})]})}),(0,t.jsx)("div",{children:(0,t.jsx)(c.A,{type:"submit",disabled:s,className:"group relative w-full flex justify-center py-3 px-4 border border-transparent text-white font-medium rounded-lg bg-mbnb-coral hover:bg-mbnb-coral-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-mbnb-coral transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-[1.02] active:scale-[0.98]",children:s?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"animate-spin -ml-1 mr-3 h-5 w-5 border-2 border-white border-t-transparent rounded-full"}),"Connexion en cours..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("span",{className:"absolute left-0 inset-y-0 flex items-center pl-3",children:(0,t.jsx)("svg",{className:"h-5 w-5 text-white/80 group-hover:text-white transition-colors",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"})})}),"Se connecter \xe0 Mbnb"]})})})]})}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsxs)("p",{className:"text-sm text-mbnb-steel",children:["Pas encore de compte Mbnb?"," ",(0,t.jsx)(o(),{href:"/auth/register",className:"font-medium text-mbnb-coral hover:text-mbnb-teal transition-colors",children:"Cr\xe9ez votre compte gratuitement"})]})}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,t.jsx)("div",{className:"w-full border-t border-gray-200"})}),(0,t.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,t.jsx)("span",{className:"px-2 bg-gradient-to-br from-mbnb-sky/5 via-white to-mbnb-teal/5 text-mbnb-steel",children:"ou continuez avec"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-3",children:[(0,t.jsx)("button",{type:"button",className:"w-full inline-flex justify-center py-3 px-4 border border-gray-200 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 hover:border-mbnb-coral transition-colors duration-200",disabled:s,children:(0,t.jsxs)("svg",{className:"w-5 h-5 text-red-500",viewBox:"0 0 24 24",children:[(0,t.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,t.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,t.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,t.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]})}),(0,t.jsx)("button",{type:"button",className:"w-full inline-flex justify-center py-3 px-4 border border-gray-200 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 hover:border-mbnb-coral transition-colors duration-200",disabled:s,children:(0,t.jsx)("svg",{className:"w-5 h-5 text-blue-600",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})})}),(0,t.jsx)("button",{type:"button",className:"w-full inline-flex justify-center py-3 px-4 border border-gray-200 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 hover:border-mbnb-coral transition-colors duration-200",disabled:s,children:(0,t.jsx)("svg",{className:"w-5 h-5 text-gray-900",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.404-5.965 1.404-5.965s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.749.097.118.111.221.082.343-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378 0 0-.593 2.25-.738 2.805-.267 1.040-1.007 2.35-1.492 3.146C9.57 23.812 10.763 23.998 12.017 24c6.624 0 11.99-5.367 11.99-11.987C24.007 5.367 18.641.001 12.017.001z"})})})]}),(0,t.jsx)("div",{className:"mt-8 bg-mbnb-teal/5 rounded-xl p-4 border border-mbnb-teal/10",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-sm text-mbnb-steel mb-2",children:"\uD83D\uDD4C Explorez le patrimoine authentique du Maroc"}),(0,t.jsx)("p",{className:"text-xs text-mbnb-steel/80",children:"Riads • Kasbahs • Dars traditionnels • Exp\xe9riences TOP 100"})]})})]})]})})}},60640:(e,r,s)=>{Promise.resolve().then(s.bind(s,59009))}},e=>{e.O(0,[9664,3558,7990,9289,2156,587,18,7358],()=>e(e.s=60640)),_N_E=e.O()}]);
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8733],{1222:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(7620),r={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(e,t)=>{let s=(0,a.forwardRef)((s,i)=>{let{color:n="currentColor",size:l=24,strokeWidth:o=2,absoluteStrokeWidth:c,className:u="",children:d,...h}=s;return(0,a.createElement)("svg",{ref:i,...r,width:l,height:l,stroke:n,strokeWidth:c?24*Number(o)/Number(l):o,className:["lucide","lucide-".concat(e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim()),u].join(" "),...h},[...t.map(e=>{let[t,s]=e;return(0,a.createElement)(t,s)}),...Array.isArray(d)?d:[d]])});return s.displayName="".concat(e),s}},26862:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(1222).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},40730:(e,t,s)=>{"use strict";s.d(t,{$:()=>l,s:()=>n});var a=s(95635),r=s(68458),i=s(28306),n=class extends r.k{#e;#t;#s;constructor(e){super(),this.mutationId=e.mutationId,this.#t=e.mutationCache,this.#e=[],this.state=e.state||l(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#e.includes(e)||(this.#e.push(e),this.clearGcTimeout(),this.#t.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#e=this.#e.filter(t=>t!==e),this.scheduleGc(),this.#t.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#e.length||("pending"===this.state.status?this.scheduleGc():this.#t.remove(this))}continue(){return this.#s?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#a({type:"continue"})};this.#s=(0,i.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#a({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#a({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#t.canRun(this)});let s="pending"===this.state.status,a=!this.#s.canStart();try{if(s)t();else{this.#a({type:"pending",variables:e,isPaused:a}),await this.#t.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#a({type:"pending",context:t,variables:e,isPaused:a})}let r=await this.#s.start();return await this.#t.config.onSuccess?.(r,e,this.state.context,this),await this.options.onSuccess?.(r,e,this.state.context),await this.#t.config.onSettled?.(r,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(r,null,e,this.state.context),this.#a({type:"success",data:r}),r}catch(t){try{throw await this.#t.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#t.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#a({type:"error",error:t})}}finally{this.#t.runNext(this)}}#a(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),a.jG.batch(()=>{this.#e.forEach(t=>{t.onMutationUpdate(e)}),this.#t.notify({mutation:this,type:"updated",action:e})})}};function l(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},56005:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(1222).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},56178:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(1222).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},58054:(e,t,s)=>{"use strict";function a(){return new Date}function r(e){return"string"==typeof e?new Date(e):e}function i(e,t){let s=new Date(e.getTime());return s.setDate(s.getDate()+t),s}s.d(t,{Io:()=>r,MZ:()=>a,fi:()=>i})},70419:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(1222).A)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},81562:(e,t,s)=>{"use strict";s.d(t,{n:()=>u});var a=s(7620),r=s(40730),i=s(95635),n=s(22844),l=s(69950),o=class extends n.Q{#r;#i=void 0;#n;#l;constructor(e,t){super(),this.#r=e,this.setOptions(t),this.bindMethods(),this.#o()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#r.defaultMutationOptions(e),(0,l.f8)(this.options,t)||this.#r.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#n,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,l.EN)(t.mutationKey)!==(0,l.EN)(this.options.mutationKey)?this.reset():this.#n?.state.status==="pending"&&this.#n.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#n?.removeObserver(this)}onMutationUpdate(e){this.#o(),this.#c(e)}getCurrentResult(){return this.#i}reset(){this.#n?.removeObserver(this),this.#n=void 0,this.#o(),this.#c()}mutate(e,t){return this.#l=t,this.#n?.removeObserver(this),this.#n=this.#r.getMutationCache().build(this.#r,this.options),this.#n.addObserver(this),this.#n.execute(e)}#o(){let e=this.#n?.state??(0,r.$)();this.#i={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#c(e){i.jG.batch(()=>{if(this.#l&&this.hasListeners()){let t=this.#i.variables,s=this.#i.context;e?.type==="success"?(this.#l.onSuccess?.(e.data,t,s),this.#l.onSettled?.(e.data,null,t,s)):e?.type==="error"&&(this.#l.onError?.(e.error,t,s),this.#l.onSettled?.(void 0,e.error,t,s))}this.listeners.forEach(e=>{e(this.#i)})})}},c=s(4869);function u(e,t){let s=(0,c.jE)(t),[r]=a.useState(()=>new o(s,e));a.useEffect(()=>{r.setOptions(e)},[r,e]);let n=a.useSyncExternalStore(a.useCallback(e=>r.subscribe(i.jG.batchCalls(e)),[r]),()=>r.getCurrentResult(),()=>r.getCurrentResult()),u=a.useCallback((e,t)=>{r.mutate(e,t).catch(l.lQ)},[r]);if(n.error&&(0,l.GU)(r.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:u,mutateAsync:n.mutate}}},81884:(e,t,s)=>{Promise.resolve().then(s.bind(s,83491))},83491:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var a=s(54568),r=s(7620),i=s(4869),n=s(22995),l=s(81562),o=s(1222);let c=(0,o.A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]),u=(0,o.A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]),d=(0,o.A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);var h=s(26862),m=s(56005),x=s(56178),p=s(70419);let b=(0,o.A)("MoreVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]),v=(0,o.A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),y=(0,o.A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);var f=s(58054);function g(){let e=(0,i.jE)(),[t,s]=(0,r.useState)(1),[o,g]=(0,r.useState)([]),[j,w]=(0,r.useState)({sortBy:"joinedDate",sortOrder:"desc"}),[N,k]=(0,r.useState)(!1),{data:C}=(0,n.I)({queryKey:["admin-users",t,j],queryFn:async()=>{let e=new URLSearchParams({page:t.toString(),limit:"20",...Object.fromEntries(Object.entries(j).filter(e=>{let[t,s]=e;return void 0!==s}))}),s=await fetch("/api/v1/admin/users?".concat(e),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("mbnb-admin-token"))}});if(!s.ok)throw Error("Failed to fetch users");return s.json()}}),A=(0,l.n)({mutationFn:async e=>{let{userId:t,status:s}=e,a=await fetch("/api/v1/admin/users/".concat(t,"/status"),{method:"PATCH",headers:{Authorization:"Bearer ".concat(localStorage.getItem("mbnb-admin-token")),"Content-Type":"application/json"},body:JSON.stringify({status:s})});if(!a.ok)throw Error("Failed to update user status");return a.json()},onSuccess:()=>{e.invalidateQueries({queryKey:["admin-users"]})}}),S=(0,l.n)({mutationFn:async e=>{let{userId:t,verificationType:s}=e,a=await fetch("/api/v1/admin/users/".concat(t,"/verify"),{method:"POST",headers:{Authorization:"Bearer ".concat(localStorage.getItem("mbnb-admin-token")),"Content-Type":"application/json"},body:JSON.stringify({type:s})});if(!a.ok)throw Error("Failed to verify user");return a.json()},onSuccess:()=>{e.invalidateQueries({queryKey:["admin-users"]})}}),E=async()=>{let e=await fetch("/api/v1/admin/users/export",{headers:{Authorization:"Bearer ".concat(localStorage.getItem("mbnb-admin-token"))}});if(e.ok){let t=await e.blob(),s=window.URL.createObjectURL(t),a=document.createElement("a");a.href=s,a.download="mbnb-users-".concat((0,f.MZ)().toISOString().split("T")[0],".csv"),document.body.appendChild(a),a.click(),document.body.removeChild(a)}},M=async t=>{0!==o.length&&(await fetch("/api/v1/admin/users/bulk-action",{method:"POST",headers:{Authorization:"Bearer ".concat(localStorage.getItem("mbnb-admin-token")),"Content-Type":"application/json"},body:JSON.stringify({userIds:o,action:t})})).ok&&(g([]),e.invalidateQueries({queryKey:["admin-users"]}))},O=e=>new Intl.NumberFormat("fr-MA",{style:"currency",currency:"MAD"}).format(e);return(0,a.jsxs)("div",{className:"min-h-screen bg-neutral-50",children:[(0,a.jsx)("div",{className:"bg-white shadow-sm border-b border-neutral-200",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-mbnb-navy",children:"Gestion des Utilisateurs Mbnb"}),(0,a.jsxs)("p",{className:"text-sm text-neutral-600 mt-1",children:[(null==C?void 0:C.total)||0," utilisateurs totaux"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("button",{onClick:E,className:"px-4 py-2 border border-neutral-300 rounded-lg hover:bg-neutral-50 flex items-center gap-2",children:[(0,a.jsx)(c,{className:"w-4 h-4"}),"Exporter"]}),(0,a.jsxs)("button",{onClick:()=>k(!N),className:"px-4 py-2 border border-neutral-300 rounded-lg hover:bg-neutral-50 flex items-center gap-2",children:[(0,a.jsx)(u,{className:"w-4 h-4"}),"Filtres"]})]})]})})}),N&&(0,a.jsx)("div",{className:"bg-white border-b border-neutral-200 px-4 py-4",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-6 gap-4",children:[(0,a.jsx)("div",{className:"md:col-span-2",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(d,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-neutral-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Rechercher par nom, email...",value:j.search||"",onChange:e=>w({...j,search:e.target.value}),className:"pl-10 pr-4 py-2 w-full border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral focus:border-transparent"})]})}),(0,a.jsxs)("select",{value:j.role||"",onChange:e=>w({...j,role:e.target.value}),className:"px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral",children:[(0,a.jsx)("option",{value:"",children:"Tous les r\xf4les"}),(0,a.jsx)("option",{value:"TRAVELER",children:"Voyageur"}),(0,a.jsx)("option",{value:"HOST",children:"H\xf4te"}),(0,a.jsx)("option",{value:"BOTH",children:"Voyageur & H\xf4te"}),(0,a.jsx)("option",{value:"ADMIN",children:"Administrateur"})]}),(0,a.jsxs)("select",{value:j.status||"",onChange:e=>w({...j,status:e.target.value}),className:"px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral",children:[(0,a.jsx)("option",{value:"",children:"Tous les statuts"}),(0,a.jsx)("option",{value:"active",children:"Actif"}),(0,a.jsx)("option",{value:"suspended",children:"Suspendu"}),(0,a.jsx)("option",{value:"pending",children:"En attente"}),(0,a.jsx)("option",{value:"banned",children:"Banni"})]}),(0,a.jsxs)("select",{value:j.loyaltyTier||"",onChange:e=>w({...j,loyaltyTier:e.target.value}),className:"px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral",children:[(0,a.jsx)("option",{value:"",children:"Tous les niveaux"}),(0,a.jsx)("option",{value:"EXPLORATEUR",children:"Explorateur"}),(0,a.jsx)("option",{value:"AVENTURIER",children:"Aventurier"}),(0,a.jsx)("option",{value:"NOMADE",children:"Nomade"}),(0,a.jsx)("option",{value:"AMBASSADEUR",children:"Ambassadeur"}),(0,a.jsx)("option",{value:"LEGENDE",children:"L\xe9gende"})]}),(0,a.jsxs)("select",{value:j.sortBy||"joinedDate",onChange:e=>w({...j,sortBy:e.target.value}),className:"px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral",children:[(0,a.jsx)("option",{value:"joinedDate",children:"Date d'inscription"}),(0,a.jsx)("option",{value:"lastLogin",children:"Derni\xe8re connexion"}),(0,a.jsx)("option",{value:"totalSpent",children:"Total d\xe9pens\xe9"}),(0,a.jsx)("option",{value:"totalBookings",children:"Total r\xe9servations"})]})]})}),o.length>0&&(0,a.jsx)("div",{className:"bg-mbnb-coral-light px-4 py-3",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-sm font-medium text-mbnb-navy",children:[o.length," utilisateur(s) s\xe9lectionn\xe9(s)"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("button",{onClick:()=>M("activate"),className:"px-3 py-1 bg-mbnb-teal text-white rounded-md text-sm hover:bg-mbnb-teal-dark",children:"Activer"}),(0,a.jsx)("button",{onClick:()=>M("suspend"),className:"px-3 py-1 bg-warning-500 text-white rounded-md text-sm hover:bg-warning-600",children:"Suspendre"}),(0,a.jsx)("button",{onClick:()=>M("delete"),className:"px-3 py-1 bg-mbnb-coral-dark text-white rounded-md text-sm hover:bg-error-700",children:"Supprimer"})]})]})}),(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:(0,a.jsxs)("div",{className:"bg-white shadow-card rounded-lg overflow-hidden",children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-neutral-200",children:[(0,a.jsx)("thead",{className:"bg-neutral-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider",children:(0,a.jsx)("input",{type:"checkbox",onChange:e=>{e.target.checked?g((null==C?void 0:C.users.map(e=>e.id))||[]):g([])},checked:o.length===(null==C?void 0:C.users.length)&&(null==C?void 0:C.users.length)>0,className:"rounded border-neutral-300 text-mbnb-coral focus:ring-mbnb-coral"})}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider",children:"Utilisateur"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider",children:"R\xf4le"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider",children:"Statut"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider",children:"V\xe9rifications"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider",children:"Loyaut\xe9"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider",children:"Activit\xe9"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-neutral-200",children:null==C?void 0:C.users.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-neutral-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("input",{type:"checkbox",checked:o.includes(e.id),onChange:t=>{t.target.checked?g([...o,e.id]):g(o.filter(t=>t!==e.id))},className:"rounded border-neutral-300 text-mbnb-coral focus:ring-mbnb-coral"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-sm font-medium text-neutral-900",children:[e.firstName," ",e.lastName]}),(0,a.jsx)("div",{className:"text-sm text-neutral-500",children:e.email}),e.phone&&(0,a.jsx)("div",{className:"text-xs text-neutral-400",children:e.phone})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat((e=>{switch(e){case"ADMIN":return"bg-mbnb-coral text-white";case"HOST":return"bg-mbnb-navy text-white";case"BOTH":return"bg-mbnb-teal text-white";default:return"bg-mbnb-sky text-mbnb-navy"}})(e.role)),children:e.role}),e.isSuperHost&&(0,a.jsxs)("div",{className:"flex items-center gap-1 mt-1",children:[(0,a.jsx)(h.A,{className:"w-3 h-3 text-mbnb-coral",fill:"currentColor"}),(0,a.jsx)("span",{className:"text-xs text-mbnb-coral",children:"SuperHost"})]})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"font-medium ".concat((e=>{switch(e){case"active":return"text-mbnb-teal-dark";case"suspended":return"text-warning-600";case"banned":return"text-mbnb-coral-dark";default:return"text-neutral-500"}})(e.status)),children:e.status})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[e.emailVerified?(0,a.jsx)(m.A,{className:"w-4 h-4 text-mbnb-teal-dark"}):(0,a.jsx)(m.A,{className:"w-4 h-4 text-neutral-300"}),e.phoneVerified?(0,a.jsx)(x.A,{className:"w-4 h-4 text-mbnb-teal-dark"}):(0,a.jsx)(x.A,{className:"w-4 h-4 text-neutral-300"}),e.identityVerified?(0,a.jsx)(p.A,{className:"w-4 h-4 text-mbnb-teal-dark"}):(0,a.jsx)(p.A,{className:"w-4 h-4 text-neutral-300"})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:(e=>{switch(e){case"LEGENDE":return"text-mbnb-coral font-bold";case"AMBASSADEUR":return"text-mbnb-navy font-semibold";case"AVENTURIER":return"text-mbnb-steel";case"EXPLORATEUR":return"text-mbnb-teal";default:return"text-neutral-600"}})(e.loyaltyTier),children:e.loyaltyTier}),(0,a.jsxs)("div",{className:"text-xs text-neutral-500",children:[e.loyaltyPoints," pts • Phase ",e.commissionPhase]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-neutral-500",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{children:[e.totalBookings," r\xe9servations"]}),(0,a.jsx)("div",{children:O(e.totalSpent)}),e.totalEarned&&(0,a.jsxs)("div",{className:"text-xs",children:["Gagn\xe9: ",O(e.totalEarned)]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("button",{className:"text-neutral-400 hover:text-neutral-600",children:(0,a.jsx)(b,{className:"w-5 h-5"})}),(0,a.jsx)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 hidden group-hover:block",children:(0,a.jsxs)("div",{className:"py-1",children:[(0,a.jsx)("button",{onClick:()=>window.location.href="/admin/users/".concat(e.id),className:"block px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 w-full text-left",children:"Voir d\xe9tails"}),!e.emailVerified&&(0,a.jsx)("button",{onClick:()=>S.mutate({userId:e.id,verificationType:"email"}),className:"block px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 w-full text-left",children:"V\xe9rifier email"}),!e.identityVerified&&(0,a.jsx)("button",{onClick:()=>S.mutate({userId:e.id,verificationType:"identity"}),className:"block px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 w-full text-left",children:"V\xe9rifier identit\xe9"}),"active"===e.status?(0,a.jsx)("button",{onClick:()=>A.mutate({userId:e.id,status:"suspended"}),className:"block px-4 py-2 text-sm text-warning-600 hover:bg-warning-50 w-full text-left",children:"Suspendre"}):(0,a.jsx)("button",{onClick:()=>A.mutate({userId:e.id,status:"active"}),className:"block px-4 py-2 text-sm text-mbnb-teal-dark hover:bg-mbnb-teal-light/10 w-full text-left",children:"Activer"}),(0,a.jsx)("button",{onClick:()=>A.mutate({userId:e.id,status:"banned"}),className:"block px-4 py-2 text-sm text-mbnb-coral-dark hover:bg-mbnb-coral-light/10 w-full text-left",children:"Bannir"})]})})]})})]},e.id))})]})}),(0,a.jsxs)("div",{className:"bg-white px-4 py-3 flex items-center justify-between border-t border-neutral-200 sm:px-6",children:[(0,a.jsxs)("div",{className:"flex-1 flex justify-between sm:hidden",children:[(0,a.jsx)("button",{onClick:()=>s(Math.max(1,t-1)),disabled:1===t,className:"relative inline-flex items-center px-4 py-2 border border-neutral-300 text-sm font-medium rounded-md text-neutral-700 bg-white hover:bg-neutral-50 disabled:opacity-50",children:"Pr\xe9c\xe9dent"}),(0,a.jsx)("button",{onClick:()=>s(Math.min((null==C?void 0:C.pages)||1,t+1)),disabled:t===(null==C?void 0:C.pages),className:"ml-3 relative inline-flex items-center px-4 py-2 border border-neutral-300 text-sm font-medium rounded-md text-neutral-700 bg-white hover:bg-neutral-50 disabled:opacity-50",children:"Suivant"})]}),(0,a.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("p",{className:"text-sm text-neutral-700",children:["Affichage de"," ",(0,a.jsx)("span",{className:"font-medium",children:(t-1)*20+1})," \xe0"," ",(0,a.jsx)("span",{className:"font-medium",children:Math.min(20*t,(null==C?void 0:C.total)||0)})," ","sur ",(0,a.jsx)("span",{className:"font-medium",children:(null==C?void 0:C.total)||0})," r\xe9sultats"]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px",children:[(0,a.jsx)("button",{onClick:()=>s(Math.max(1,t-1)),disabled:1===t,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-neutral-300 bg-white text-sm font-medium text-neutral-500 hover:bg-neutral-50 disabled:opacity-50",children:(0,a.jsx)(v,{className:"h-5 w-5"})}),[...Array(Math.min(5,(null==C?void 0:C.pages)||1))].map((e,r)=>{let i=r+1;return(0,a.jsx)("button",{onClick:()=>s(i),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium ".concat(t===i?"z-10 bg-mbnb-coral border-mbnb-coral text-white":"bg-white border-neutral-300 text-neutral-500 hover:bg-neutral-50"),children:i},i)}),(0,a.jsx)("button",{onClick:()=>s(Math.min((null==C?void 0:C.pages)||1,t+1)),disabled:t===(null==C?void 0:C.pages),className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-neutral-300 bg-white text-sm font-medium text-neutral-500 hover:bg-neutral-50 disabled:opacity-50",children:(0,a.jsx)(y,{className:"h-5 w-5"})})]})})]})]})]})})]})}}},e=>{e.O(0,[6799,2995,587,18,7358],()=>e(e.s=81884)),_N_E=e.O()}]);
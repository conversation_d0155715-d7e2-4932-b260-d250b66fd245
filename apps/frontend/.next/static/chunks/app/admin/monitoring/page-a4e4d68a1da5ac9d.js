(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[25],{23634:(e,t,r)=>{Promise.resolve().then(r.bind(r,69400))},26095:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(7620);let a=s.forwardRef(function(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},27541:(e,t,r)=>{"use strict";var s=r(43041);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},32503:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(7620);let a=s.forwardRef(function(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"}))})},40730:(e,t,r)=>{"use strict";r.d(t,{$:()=>o,s:()=>i});var s=r(95635),a=r(68458),n=r(28306),i=class extends a.k{#e;#t;#r;constructor(e){super(),this.mutationId=e.mutationId,this.#t=e.mutationCache,this.#e=[],this.state=e.state||o(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#e.includes(e)||(this.#e.push(e),this.clearGcTimeout(),this.#t.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#e=this.#e.filter(t=>t!==e),this.scheduleGc(),this.#t.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#e.length||("pending"===this.state.status?this.scheduleGc():this.#t.remove(this))}continue(){return this.#r?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#s({type:"continue"})};this.#r=(0,n.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#s({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#s({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#t.canRun(this)});let r="pending"===this.state.status,s=!this.#r.canStart();try{if(r)t();else{this.#s({type:"pending",variables:e,isPaused:s}),await this.#t.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#s({type:"pending",context:t,variables:e,isPaused:s})}let a=await this.#r.start();return await this.#t.config.onSuccess?.(a,e,this.state.context,this),await this.options.onSuccess?.(a,e,this.state.context),await this.#t.config.onSettled?.(a,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(a,null,e,this.state.context),this.#s({type:"success",data:a}),a}catch(t){try{throw await this.#t.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#t.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#s({type:"error",error:t})}}finally{this.#t.runNext(this)}}#s(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),s.jG.batch(()=>{this.#e.forEach(t=>{t.onMutationUpdate(e)}),this.#t.notify({mutation:this,type:"updated",action:e})})}};function o(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},40790:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(21367),a=r(50848),n=r(15263);let i=(0,s.vt)()((0,a.Zr)((0,n.D)((e,t)=>({user:null,token:null,refreshToken:null,isAuthenticated:!1,isLoading:!1,error:null,heritageInterests:[],activityPreferences:[],preferredLanguage:"fr",preferredCurrency:"MAD",loginToMbnb:async t=>{e(e=>{e.isLoading=!0,e.error=null});try{let r=await fetch("".concat("http://localhost:3001/api/v1","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json","X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(t)});if(!r.ok){let e=await r.json();throw Error(e.message||"Authentification Mbnb \xe9chou\xe9e")}let s=await r.json();e(e=>{e.user=s.user,e.token=s.token,e.refreshToken=s.refreshToken||null,e.isAuthenticated=!0,e.isLoading=!1,e.preferredLanguage=s.user.profile.preferredLanguage,e.preferredCurrency=s.user.profile.preferredCurrency})}catch(r){let t=r instanceof Error?r.message:"Erreur de connexion Mbnb";throw e(e=>{e.error=t,e.isLoading=!1}),r}},registerToMbnb:async t=>{e(e=>{e.isLoading=!0,e.error=null});try{let r=await fetch("".concat("http://localhost:3001/api/v1","/auth/register"),{method:"POST",headers:{"Content-Type":"application/json","X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(t)});if(!r.ok){let e=await r.json();throw Error(e.message||"Inscription Mbnb \xe9chou\xe9e")}let s=await r.json();e(e=>{e.user=s.user,e.token=s.token,e.refreshToken=s.refreshToken||null,e.isAuthenticated=!0,e.isLoading=!1,e.preferredLanguage=s.user.profile.preferredLanguage,e.preferredCurrency=s.user.profile.preferredCurrency})}catch(r){let t=r instanceof Error?r.message:"Erreur d'inscription Mbnb";throw e(e=>{e.error=t,e.isLoading=!1}),r}},logoutFromMbnb:()=>{e(e=>{e.user=null,e.token=null,e.refreshToken=null,e.isAuthenticated=!1,e.error=null,e.heritageInterests=[],e.activityPreferences=[],e.preferredLanguage="fr",e.preferredCurrency="MAD"}),localStorage.removeItem("mbnb-auth-storage"),window.location.href="/"},refreshMbnbToken:async()=>{let r=t().refreshToken;if(!r)return void t().logoutFromMbnb();try{let t=await fetch("".concat("http://localhost:3001/api/v1","/auth/refresh"),{method:"POST",headers:{"Content-Type":"application/json","X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify({refreshToken:r})});if(!t.ok)throw Error("Refresh token Mbnb expir\xe9");let s=await t.json();e(e=>{e.token=s.token,e.refreshToken=s.refreshToken})}catch(e){t().logoutFromMbnb()}},updateMbnbProfile:async r=>{e(e=>{e.isLoading=!0,e.error=null});try{let s=await fetch("".concat("http://localhost:3001/api/v1","/users/profile"),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t().token),"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(r)});if(!s.ok){let e=await s.json();throw Error(e.message||"Mise \xe0 jour profil Mbnb \xe9chou\xe9e")}let a=await s.json();e(e=>{e.user=a,e.isLoading=!1,e.preferredLanguage=a.profile.preferredLanguage,e.preferredCurrency=a.profile.preferredCurrency})}catch(r){let t=r instanceof Error?r.message:"Erreur mise \xe0 jour profil Mbnb";throw e(e=>{e.error=t,e.isLoading=!1}),r}},updateMbnbPreferences:async r=>{e(e=>{e.isLoading=!0,e.error=null});try{let s=await fetch("".concat("http://localhost:3001/api/v1","/users/preferences"),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t().token),"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify(r)});if(!s.ok){let e=await s.json();throw Error(e.message||"Mise \xe0 jour pr\xe9f\xe9rences Mbnb \xe9chou\xe9e")}e(e=>{r.heritageInterests&&(e.heritageInterests=r.heritageInterests),r.activityPreferences&&(e.activityPreferences=r.activityPreferences),r.preferredLanguage&&(e.preferredLanguage=r.preferredLanguage),r.preferredCurrency&&(e.preferredCurrency=r.preferredCurrency),e.isLoading=!1})}catch(r){let t=r instanceof Error?r.message:"Erreur mise \xe0 jour pr\xe9f\xe9rences Mbnb";throw e(e=>{e.error=t,e.isLoading=!1}),r}},verifyMbnbEmail:async r=>{let s=await fetch("".concat("http://localhost:3001/api/v1","/auth/verify-email"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t().token),"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify({code:r})});if(!s.ok)throw Error((await s.json()).message||"V\xe9rification email Mbnb \xe9chou\xe9e");e(e=>{e.user&&(e.user.verification.emailVerified=!0)})},verifyMbnbPhone:async r=>{let s=await fetch("".concat("http://localhost:3001/api/v1","/auth/verify-phone"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t().token),"X-Mbnb-Platform":"web","X-Mbnb-Version":"1.0"},body:JSON.stringify({code:r})});if(!s.ok)throw Error((await s.json()).message||"V\xe9rification t\xe9l\xe9phone Mbnb \xe9chou\xe9e");e(e=>{e.user&&(e.user.verification.phoneVerified=!0)})},clearError:()=>e(e=>{e.error=null})})),{name:"mbnb-auth-storage",storage:(0,a.KU)(()=>localStorage),partialize:e=>({user:e.user,token:e.token,refreshToken:e.refreshToken,isAuthenticated:e.isAuthenticated,heritageInterests:e.heritageInterests,activityPreferences:e.activityPreferences,preferredLanguage:e.preferredLanguage,preferredCurrency:e.preferredCurrency})}))},58054:(e,t,r)=>{"use strict";function s(){return new Date}function a(e){return"string"==typeof e?new Date(e):e}function n(e,t){let r=new Date(e.getTime());return r.setDate(r.getDate()+t),r}r.d(t,{Io:()=>a,MZ:()=>s,fi:()=>n})},59551:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(7620);let a=s.forwardRef(function(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},61284:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(7620);let a=s.forwardRef(function(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m3.75 13.5 10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75Z"}))})},64682:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(7620);let a=s.forwardRef(function(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))})},69400:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>A});var s=r(54568),a=r(7620),n=r(27541),i=r(58054),o=r(26095),c=r(64682),l=r(59551),u=r(88985),d=r(61284),h=r(91004),m=r(32503);let p=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{fillRule:"evenodd",d:"M11.47 2.47a.75.75 0 0 1 1.06 0l7.5 7.5a.75.75 0 1 1-1.06 1.06l-6.22-6.22V21a.75.75 0 0 1-1.5 0V4.81l-6.22 6.22a.75.75 0 1 1-1.06-1.06l7.5-7.5Z",clipRule:"evenodd"}))}),g=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{fillRule:"evenodd",d:"M12 2.25a.75.75 0 0 1 .75.75v16.19l6.22-6.22a.75.75 0 1 1 1.06 1.06l-7.5 7.5a.75.75 0 0 1-1.06 0l-7.5-7.5a.75.75 0 1 1 1.06-1.06l6.22 6.22V3a.75.75 0 0 1 .75-.75Z",clipRule:"evenodd"}))});var f=r(40790),x=r(21367),y=r(50848);let b={MAD:{code:"MAD",name:"Dirham Marocain",nameAr:"درهم مغربي",symbol:"DH",flag:"\uD83C\uDDF2\uD83C\uDDE6",rate:1,decimals:2,locale:"ar-MA",region:["MA","North Africa"],popular:!0},EUR:{code:"EUR",name:"Euro",nameAr:"يورو",symbol:"€",flag:"\uD83C\uDDEA\uD83C\uDDFA",rate:.094,decimals:2,locale:"fr-FR",region:["EU","Europe"],popular:!0},USD:{code:"USD",name:"Dollar Am\xe9ricain",nameAr:"دولار أمريكي",symbol:"$",flag:"\uD83C\uDDFA\uD83C\uDDF8",rate:.1,decimals:2,locale:"en-US",region:["US","Americas"],popular:!0},GBP:{code:"GBP",name:"Livre Sterling",nameAr:"جنيه إسترليني",symbol:"\xa3",flag:"\uD83C\uDDEC\uD83C\uDDE7",rate:.081,decimals:2,locale:"en-GB",region:["GB","Europe"],popular:!0},CAD:{code:"CAD",name:"Dollar Canadien",nameAr:"دولار كندي",symbol:"CA$",flag:"\uD83C\uDDE8\uD83C\uDDE6",rate:.135,decimals:2,locale:"en-CA",region:["CA","Americas"],popular:!0},AUD:{code:"AUD",name:"Dollar Australien",nameAr:"دولار أسترالي",symbol:"AU$",flag:"\uD83C\uDDE6\uD83C\uDDFA",rate:.15,decimals:2,locale:"en-AU",region:["AU","Oceania"],popular:!0},CNY:{code:"CNY",name:"Yuan Chinois",nameAr:"يوان صيني",symbol:"\xa5",flag:"\uD83C\uDDE8\uD83C\uDDF3",rate:.72,decimals:2,locale:"zh-CN",region:["CN","Asia"],popular:!0},RUB:{code:"RUB",name:"Rouble Russe",nameAr:"روبل روسي",symbol:"₽",flag:"\uD83C\uDDF7\uD83C\uDDFA",rate:9.2,decimals:2,locale:"ru-RU",region:["RU","Europe","Asia"],popular:!0}},v={Morocco:["MAD"],Europe:["EUR","GBP","RUB"],Americas:["USD","CAD"],Asia:["CNY","RUB"],Oceania:["AUD"],Africa:["MAD"]},w=["MAD","EUR","USD","GBP","CAD","AUD","CNY","RUB"];class j{static convert(e,t,r){let s=this.rates.rates[t],a=this.rates.rates[r];if(!s||!a)throw Error("Devise non support\xe9e: ".concat(t," ou ").concat(r));let n=e/s*a;return{from:t,to:r,amount:e,convertedAmount:Math.round(100*n)/100,rate:Math.round(a/s*1e6)/1e6,formatted:this.formatAmount(n,r)}}static formatAmount(e,t){let r=!(arguments.length>2)||void 0===arguments[2]||arguments[2],s=b[t];if(!s)throw Error("Devise non support\xe9e: ".concat(t));let a=new Intl.NumberFormat(s.locale,{style:"decimal",minimumFractionDigits:s.decimals,maximumFractionDigits:s.decimals}).format(e);return r?"".concat(a," ").concat(s.symbol):a}static formatCurrency(e,t){let r=b[t];if(!r)throw Error("Devise non support\xe9e: ".concat(t));return new Intl.NumberFormat(r.locale,{style:"currency",currency:t,minimumFractionDigits:r.decimals,maximumFractionDigits:r.decimals}).format(e)}static getCurrencyInfo(e){let t=b[e];if(!t)throw Error("Devise non support\xe9e: ".concat(e));return t}static getAllCurrencies(){return Object.values(b)}static getPopularCurrencies(){return w.map(e=>b[e])}static getCurrenciesByRegion(e){return(v[e]||[]).map(e=>b[e])}static detectCurrencyByLocation(e){let t={MA:"MAD",FR:"EUR",ES:"EUR",IT:"EUR",DE:"EUR",NL:"EUR",BE:"EUR",US:"USD",GB:"GBP",CA:"CAD",AU:"AUD",CN:"CNY",RU:"RUB"};if(e&&t[e])return t[e];let r=Intl.DateTimeFormat().resolvedOptions().timeZone;if(r.includes("Casablanca")||r.includes("Africa"));else if(r.includes("Europe"))return"EUR";else if(r.includes("America/New_York")||r.includes("America/Los_Angeles"))return"USD";else if(r.includes("Asia/Shanghai")||r.includes("Asia/Hong_Kong"))return"CNY";else if(r.includes("Europe/Moscow"))return"RUB";return"MAD"}static async updateRates(){this.rates.lastUpdated=(0,i.MZ)(),Object.keys(this.rates.rates).forEach(e=>{if("MAD"!==e){let t=b[e].rate,r=(Math.random()-.5)*.02;this.rates.rates[e]=t*(1+r)}})}static getRates(){return{...this.rates}}static convertBulk(e){return e.map(e=>{let{amount:t,from:r,to:s}=e;return this.convert(t,r,s)})}static getExchangeRate(e,t){let r=this.rates.rates[e],s=this.rates.rates[t];if(!r||!s)throw Error("Devise non support\xe9e: ".concat(e," ou ").concat(t));return s/r}static isValidCurrency(e){return e in b}}j.rates={base:"MAD",rates:Object.fromEntries(Object.entries(b).map(e=>{let[t,r]=e;return[t,r.rate]})),lastUpdated:(0,i.MZ)()};let N=(0,x.vt)()((0,y.Zr)((e,t)=>({selectedCurrency:(()=>{try{return j.detectCurrencyByLocation()}catch(e){return"MAD"}})(),defaultCurrency:"MAD",rates:j.getRates(),isLoading:!1,lastUpdated:null,autoDetectLocation:!0,setCurrency:t=>{if(!j.isValidCurrency(t))return void console.warn("[Currency Store] Devise invalide: ".concat(t));e({selectedCurrency:t}),window.gtag&&window.gtag("event","currency_change",{event_category:"user_interaction",event_label:t})},setDefaultCurrency:t=>{if(!j.isValidCurrency(t))return void console.warn("[Currency Store] Devise par d\xe9faut invalide: ".concat(t));e({defaultCurrency:t})},updateRates:async()=>{e({isLoading:!0});try{await j.updateRates();let t=j.getRates();e({rates:t,lastUpdated:(0,i.MZ)(),isLoading:!1}),console.log("[Currency Store] Taux de change mis \xe0 jour:",t.lastUpdated)}catch(t){console.error("[Currency Store] Erreur lors de la mise \xe0 jour des taux:",t),e({isLoading:!1})}},detectCurrencyByLocation:async()=>{if(t().autoDetectLocation)try{if(navigator.geolocation)navigator.geolocation.getCurrentPosition(async()=>{try{let r=j.detectCurrencyByLocation();r!==t().selectedCurrency&&(e({selectedCurrency:r}),console.log("[Currency Store] Devise d\xe9tect\xe9e: ".concat(r)))}catch(e){console.warn("[Currency Store] Erreur lors de la d\xe9tection g\xe9ographique:",e)}},r=>{console.warn("[Currency Store] G\xe9olocalisation refus\xe9e:",r.message);let s=j.detectCurrencyByLocation();s!==t().selectedCurrency&&e({selectedCurrency:s})});else{let r=j.detectCurrencyByLocation();r!==t().selectedCurrency&&e({selectedCurrency:r})}}catch(e){console.error("[Currency Store] Erreur lors de la d\xe9tection:",e)}},resetToDefault:()=>{let{defaultCurrency:r}=t();e({selectedCurrency:r})},toggleAutoDetect:()=>{let r=!t().autoDetectLocation;e({autoDetectLocation:r}),r&&t().detectCurrencyByLocation()},convert:(e,r,s)=>{let{selectedCurrency:a}=t(),n=r||a,i=s||a;return j.convert(e,n,i)},formatAmount:function(e,r){let s=!(arguments.length>2)||void 0===arguments[2]||arguments[2],{selectedCurrency:a}=t();return j.formatAmount(e,r||a,s)},formatCurrency:(e,r)=>{let{selectedCurrency:s}=t();return j.formatCurrency(e,r||s)},getExchangeRate:(e,t)=>j.getExchangeRate(e,t)}),{name:"mbnb-currency-store",version:1,migrate:(e,t)=>0===t?{...e,autoDetectLocation:!0,lastUpdated:null}:e,partialize:e=>({selectedCurrency:e.selectedCurrency,defaultCurrency:e.defaultCurrency,autoDetectLocation:e.autoDetectLocation})}));var C=r(6487);function A(){let e=(0,n.useRouter)(),{user:t,isAuthenticated:r}=(0,f.A)(),{formatCurrency:x}=(()=>{let e=N(e=>e.convert),t=N(e=>e.formatAmount),r=N(e=>e.formatCurrency),s=N(e=>e.selectedCurrency);return{convert:e,formatAmount:t,formatCurrency:r,selectedCurrency:s,convertToSelected:(t,r)=>e(t,r,s),formatInSelected:(r,a)=>t(e(r,a,s).convertedAmount),convertFromMAD:t=>e(t,"MAD",s)}})(),{data:y,isLoading:b,error:v}=(0,C.Pi)(),{data:w,isLoading:j,error:A}=(0,C._)(),{data:M,isLoading:E,error:k}=(0,C.As)(),[R,L]=(0,a.useState)(!0);if((0,a.useEffect)(()=>{var s;r&&(null==t||null==(s=t.roles)?void 0:s.isAdmin)||e.push("/login")},[r,t,e]),v||A||k)return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 p-6",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"text-red-800 font-semibold",children:"Erreur de chargement des donn\xe9es monitoring"}),(0,s.jsx)("p",{className:"text-red-600 text-sm mt-1",children:(null==v?void 0:v.message)||(null==A?void 0:A.message)||(null==k?void 0:k.message)})]})})});let D=e=>{switch(e){case"healthy":case"good":return"text-green-600 bg-green-100";case"warning":case"degraded":return"text-yellow-600 bg-yellow-100";case"critical":case"down":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}},S=e=>{switch(e){case"healthy":case"good":return o.A;case"warning":case"degraded":return c.A;case"critical":case"down":return l.A;default:return u.A}};return b||j||E?(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-mbnb-coral"})}):(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,s.jsx)("div",{className:"px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"py-6",children:(0,s.jsxs)("div",{className:"md:flex md:items-center md:justify-between",children:[(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate",children:"Monitoring Dashboard"}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Surveillance en temps r\xe9el de la plateforme Mbnb"})]}),(0,s.jsxs)("div",{className:"mt-4 flex md:mt-0 md:ml-4 space-x-3",children:[(0,s.jsxs)("button",{onClick:()=>L(!R),className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium transition-colors ".concat(R?"text-white bg-mbnb-coral border-mbnb-coral":"text-gray-700 bg-white hover:bg-gray-50"),children:[(0,s.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Auto-refresh"]}),(0,s.jsxs)("div",{className:"text-sm text-gray-500 flex items-center",children:["Derni\xe8re mise \xe0 jour: ",(0,i.MZ)().toLocaleTimeString("fr-FR")]})]})]})})})}),(0,s.jsxs)("div",{className:"px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"\xc9tat du syst\xe8me"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4",children:null==y?void 0:y.map(e=>{let t=S(e.status);return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,s.jsx)(d.A,{className:"h-6 w-6 text-gray-400"}),(0,s.jsx)(t,{className:"h-5 w-5 ".concat(D(e.status).split(" ")[0])})]}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:e.name}),(0,s.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.value.toLocaleString()," ",e.unit]}),(0,s.jsxs)("div",{className:"flex items-center text-xs",children:[e.change>0?(0,s.jsx)(p,{className:"h-3 w-3 text-green-500 mr-1"}):(0,s.jsx)(g,{className:"h-3 w-3 text-red-500 mr-1"}),(0,s.jsx)("span",{className:e.change>0?"text-green-500":"text-red-500",children:Math.abs(e.change)}),(0,s.jsx)("span",{className:"text-gray-500 ml-1",children:"vs hier"})]})]})]},e.name)})})]}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Performance des APIs"}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Endpoint"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Temps de r\xe9ponse"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Requ\xeates (24h)"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Taux d'erreur"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Statut"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:null==w?void 0:w.map((e,t)=>{let r=S(e.status);return(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.endpoint})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"text-sm text-gray-900",children:[e.avgResponseTime,"ms"]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("div",{className:"text-sm text-gray-900",children:e.requestCount.toLocaleString()})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"text-sm text-gray-900",children:[e.errorRate.toFixed(2),"%"]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(D(e.status)),children:[(0,s.jsx)(r,{className:"h-3 w-3 mr-1"}),e.status]})})]},t)})})]})})]}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"M\xe9triques Business"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:null==M?void 0:M.map((e,t)=>(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-500",children:e.label}),(0,s.jsx)("span",{className:"text-xs text-gray-400",children:e.period})]}),(0,s.jsxs)("div",{className:"flex items-baseline justify-between",children:[(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:((e,t)=>{switch(t){case"currency":return x(e,"MAD");case"percentage":return"".concat(e,"%");default:return e.toLocaleString()}})(e.value,e.format)}),(0,s.jsxs)("div",{className:"flex items-center text-sm",children:[e.change>0?(0,s.jsx)(p,{className:"h-4 w-4 text-green-500 mr-1"}):(0,s.jsx)(g,{className:"h-4 w-4 text-red-500 mr-1"}),(0,s.jsxs)("span",{className:e.change>0?"text-green-500":"text-red-500",children:[Math.abs(e.change),"%"]})]})]})]},t))})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,s.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,s.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 flex items-center",children:[(0,s.jsx)(h.A,{className:"h-6 w-6 text-mbnb-coral mr-2"}),"S\xe9curit\xe9"]})}),(0,s.jsxs)("div",{className:"p-6 space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Certificat SSL"}),(0,s.jsx)(o.A,{className:"h-5 w-5 text-green-500"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"PCI-DSS Compliance"}),(0,s.jsx)(o.A,{className:"h-5 w-5 text-green-500"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"WAF Protection"}),(0,s.jsx)(o.A,{className:"h-5 w-5 text-green-500"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Rate Limiting"}),(0,s.jsx)(o.A,{className:"h-5 w-5 text-green-500"})]})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,s.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,s.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 flex items-center",children:[(0,s.jsx)(m.A,{className:"h-6 w-6 text-mbnb-teal mr-2"}),"Statistiques globales"]})}),(0,s.jsxs)("div",{className:"p-6 space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Propri\xe9t\xe9s actives"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"2,847"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Utilisateurs enregistr\xe9s"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"15,632"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Activit\xe9s TOP 100"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"100"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Devises support\xe9es"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"8"})]})]})]})]})]})]})}},81562:(e,t,r)=>{"use strict";r.d(t,{n:()=>u});var s=r(7620),a=r(40730),n=r(95635),i=r(22844),o=r(69950),c=class extends i.Q{#a;#n=void 0;#i;#o;constructor(e,t){super(),this.#a=e,this.setOptions(t),this.bindMethods(),this.#c()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#a.defaultMutationOptions(e),(0,o.f8)(this.options,t)||this.#a.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#i,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,o.EN)(t.mutationKey)!==(0,o.EN)(this.options.mutationKey)?this.reset():this.#i?.state.status==="pending"&&this.#i.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#i?.removeObserver(this)}onMutationUpdate(e){this.#c(),this.#l(e)}getCurrentResult(){return this.#n}reset(){this.#i?.removeObserver(this),this.#i=void 0,this.#c(),this.#l()}mutate(e,t){return this.#o=t,this.#i?.removeObserver(this),this.#i=this.#a.getMutationCache().build(this.#a,this.options),this.#i.addObserver(this),this.#i.execute(e)}#c(){let e=this.#i?.state??(0,a.$)();this.#n={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#l(e){n.jG.batch(()=>{if(this.#o&&this.hasListeners()){let t=this.#n.variables,r=this.#n.context;e?.type==="success"?(this.#o.onSuccess?.(e.data,t,r),this.#o.onSettled?.(e.data,null,t,r)):e?.type==="error"&&(this.#o.onError?.(e.error,t,r),this.#o.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach(e=>{e(this.#n)})})}},l=r(4869);function u(e,t){let r=(0,l.jE)(t),[a]=s.useState(()=>new c(r,e));s.useEffect(()=>{a.setOptions(e)},[a,e]);let i=s.useSyncExternalStore(s.useCallback(e=>a.subscribe(n.jG.batchCalls(e)),[a]),()=>a.getCurrentResult(),()=>a.getCurrentResult()),u=s.useCallback((e,t)=>{a.mutate(e,t).catch(o.lQ)},[a]);if(i.error&&(0,o.GU)(a.options.throwOnError,[i.error]))throw i.error;return{...i,mutate:u,mutateAsync:i.mutate}}},88985:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(7620);let a=s.forwardRef(function(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},91004:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(7620);let a=s.forwardRef(function(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))})}},e=>{e.O(0,[6799,2995,3558,7990,6487,587,18,7358],()=>e(e.s=23634)),_N_E=e.O()}]);
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5957],{826:(e,t,s)=>{"use strict";s.d(t,{Yq:()=>i,cn:()=>l});var a=s(72902),n=s(13714),r=s(58054);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,n.QP)((0,a.$)(t))}function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"short",s="string"==typeof e?(0,r.Io)(e):e;return"short"===t?new Intl.DateTimeFormat("fr-MA",{day:"numeric",month:"short",year:"numeric"}).format(s):new Intl.DateTimeFormat("fr-MA",{weekday:"long",day:"numeric",month:"long",year:"numeric"}).format(s)}},1222:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(7620),n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let r=(e,t)=>{let s=(0,a.forwardRef)((s,r)=>{let{color:l="currentColor",size:i=24,strokeWidth:c=2,absoluteStrokeWidth:d,className:o="",children:m,...x}=s;return(0,a.createElement)("svg",{ref:r,...n,width:i,height:i,stroke:l,strokeWidth:d?24*Number(c)/Number(i):c,className:["lucide","lucide-".concat(e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim()),o].join(" "),...x},[...t.map(e=>{let[t,s]=e;return(0,a.createElement)(t,s)}),...Array.isArray(m)?m:[m]])});return s.displayName="".concat(e),s}},9013:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(1222).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},12584:(e,t,s)=>{Promise.resolve().then(s.bind(s,71297))},58054:(e,t,s)=>{"use strict";function a(){return new Date}function n(e){return"string"==typeof e?new Date(e):e}function r(e,t){let s=new Date(e.getTime());return s.setDate(s.getDate()+t),s}s.d(t,{Io:()=>n,MZ:()=>a,fi:()=>r})},70419:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(1222).A)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},71297:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var a=s(54568),n=s(7620),r=s(22995);class l{getAdminToken(){return localStorage.getItem("mbnb-admin-token")}async request(e,t,s,a){let n=this.getAdminToken(),r=await fetch("".concat(this.config.baseURL).concat(t),{method:e,headers:{"Content-Type":"application/json",Authorization:n?"Bearer ".concat(n):"","X-Mbnb-Platform":"admin","X-Mbnb-Client":"frontend-admin",...null==a?void 0:a.headers},body:s?JSON.stringify(s):void 0,...a});if(!r.ok)throw Error("Admin API Error: ".concat(r.status," ").concat(r.statusText));return r.json()}async get(e,t){return this.request("GET",e,void 0,t)}async post(e,t,s){return this.request("POST",e,t,s)}async put(e,t,s){return this.request("PUT",e,t,s)}async delete(e,t){return this.request("DELETE",e,void 0,t)}async patch(e,t,s){return this.request("PATCH",e,t,s)}async getMetrics(e){return this.get("/api/v1/admin/metrics?period=".concat(e))}async getSystemHealth(){return this.get("/api/v1/admin/health")}async getRecentActivities(){return this.get("/api/v1/admin/activities/recent")}constructor(){this.config={baseURL:"http://localhost:3001/api/v1",timeout:3e4,retries:3}}}let i=new l;var c=s(826),d=s(1222);let o=(0,d.A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),m=(0,d.A)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]),x=(0,d.A)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]),h=(0,d.A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);var u=s(9013),p=s(79535);let b=(0,d.A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]),v=(0,d.A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var j=s(70419);let g=(0,d.A)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);function y(){let[e,t]=(0,n.useState)("month"),[s,l]=(0,n.useState)(3e4),{data:d,isLoading:y}=(0,r.I)({queryKey:["admin-metrics",e],queryFn:()=>i.getMetrics(e),refetchInterval:s}),{data:N,isLoading:f}=(0,r.I)({queryKey:["system-health"],queryFn:()=>i.getSystemHealth(),refetchInterval:1e4}),{data:w}=(0,r.I)({queryKey:["recent-activities"],queryFn:()=>i.getRecentActivities(),refetchInterval:s}),k=e=>new Intl.NumberFormat("fr-MA",{style:"currency",currency:"MAD"}).format(e),A=e=>new Intl.NumberFormat("fr-MA").format(e),M=e=>{switch(e){case"operational":case"healthy":return"text-mbnb-teal-dark";case"degraded":return"text-warning-500";case"down":case"critical":return"text-mbnb-coral-dark";default:return"text-neutral-500"}};return y||f?(0,a.jsx)("div",{className:"min-h-screen bg-neutral-50 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-pulse text-mbnb-coral text-lg",children:"Chargement du tableau de bord administrateur Mbnb..."})}):(0,a.jsxs)("div",{className:"min-h-screen bg-neutral-50",children:[(0,a.jsx)("div",{className:"bg-white shadow-sm border-b border-neutral-200",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-mbnb-navy",children:"Tableau de Bord Administrateur Mbnb"}),(0,a.jsx)("p",{className:"text-sm text-neutral-600 mt-1",children:"Supervision compl\xe8te de la plateforme"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("select",{value:e,onChange:e=>t(e.target.value),className:"px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral focus:border-transparent",children:[(0,a.jsx)("option",{value:"today",children:"Aujourd'hui"}),(0,a.jsx)("option",{value:"week",children:"Cette semaine"}),(0,a.jsx)("option",{value:"month",children:"Ce mois"}),(0,a.jsx)("option",{value:"year",children:"Cette ann\xe9e"})]}),(0,a.jsxs)("select",{value:s,onChange:e=>l(Number(e.target.value)),className:"px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-mbnb-coral focus:border-transparent",children:[(0,a.jsx)("option",{value:"10000",children:"10s"}),(0,a.jsx)("option",{value:"30000",children:"30s"}),(0,a.jsx)("option",{value:"60000",children:"1min"}),(0,a.jsx)("option",{value:"300000",children:"5min"})]})]})]})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[N&&"operational"!==N.api.status&&(0,a.jsx)("div",{className:"mb-6 p-4 bg-warning-50 border border-warning-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(p.A,{className:"w-5 h-5 text-warning-600"}),(0,a.jsx)("span",{className:"font-medium text-warning-900",children:"Attention: Certains services sont d\xe9grad\xe9s"})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-card p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)(o,{className:"w-8 h-8 text-mbnb-teal"}),(0,a.jsxs)("span",{className:"text-sm text-neutral-500",children:[(null==d?void 0:d.activeUsers)||0," actifs"]})]}),(0,a.jsx)("div",{className:"text-2xl font-bold text-mbnb-navy",children:A((null==d?void 0:d.totalUsers)||0)}),(0,a.jsx)("div",{className:"text-sm text-neutral-600 mt-1",children:"Utilisateurs totaux"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-card p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)(m,{className:"w-8 h-8 text-mbnb-navy"}),(0,a.jsxs)("span",{className:"text-sm text-neutral-500",children:[(null==d?void 0:d.verifiedProperties)||0," v\xe9rifi\xe9es"]})]}),(0,a.jsx)("div",{className:"text-2xl font-bold text-mbnb-navy",children:A((null==d?void 0:d.totalProperties)||0)}),(0,a.jsx)("div",{className:"text-sm text-neutral-600 mt-1",children:"Propri\xe9t\xe9s totales"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-card p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)(h,{className:"w-8 h-8 text-success-600"}),(0,a.jsx)("span",{className:"text-sm text-neutral-500",children:e})]}),(0,a.jsx)("div",{className:"text-2xl font-bold text-mbnb-navy",children:k((null==d?void 0:d.totalRevenue)||0)}),(0,a.jsx)("div",{className:"text-sm text-neutral-600 mt-1",children:"Revenus totaux"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-card p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)(b,{className:"w-8 h-8 text-mbnb-coral"}),(0,a.jsx)("span",{className:"text-sm text-neutral-500",children:"14-15% model"})]}),(0,a.jsx)("div",{className:"text-2xl font-bold text-mbnb-navy",children:k((null==d?void 0:d.commissionEarned)||0)}),(0,a.jsx)("div",{className:"text-sm text-neutral-600 mt-1",children:"Commissions Mbnb"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-card p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-mbnb-navy mb-4",children:"Sant\xe9 du Syst\xe8me"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(v,{className:"w-5 h-5 text-neutral-500"}),(0,a.jsx)("span",{className:"text-neutral-700",children:"API Backend"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"font-medium ".concat(M((null==N?void 0:N.api.status)||"operational")),children:(null==N?void 0:N.api.status)||"operational"}),(0,a.jsxs)("span",{className:"text-sm text-neutral-500",children:[(null==N?void 0:N.api.latency)||0,"ms"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(j.A,{className:"w-5 h-5 text-neutral-500"}),(0,a.jsx)("span",{className:"text-neutral-700",children:"Database"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"font-medium ".concat(M((null==N?void 0:N.database.status)||"operational")),children:(null==N?void 0:N.database.status)||"operational"}),(0,a.jsxs)("span",{className:"text-sm text-neutral-500",children:[(null==N?void 0:N.database.connections)||0," conn"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(x,{className:"w-5 h-5 text-neutral-500"}),(0,a.jsx)("span",{className:"text-neutral-700",children:"Cache Redis"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"font-medium ".concat(M((null==N?void 0:N.redis.status)||"operational")),children:(null==N?void 0:N.redis.status)||"operational"}),(0,a.jsxs)("span",{className:"text-sm text-neutral-500",children:[(null==N?void 0:N.redis.memory)||0,"MB"]})]})]}),(0,a.jsxs)("div",{className:"pt-4 border-t border-neutral-200",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-neutral-700 mb-2",children:"Int\xe9grations Tierces"}),(0,a.jsx)("div",{className:"grid grid-cols-3 gap-2",children:(null==N?void 0:N.integrations)&&Object.entries(N.integrations).map(e=>{let[t,s]=e;return(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[s?(0,a.jsx)(u.A,{className:"w-4 h-4 text-mbnb-teal-dark"}):(0,a.jsx)(p.A,{className:"w-4 h-4 text-mbnb-coral-dark"}),(0,a.jsx)("span",{className:"text-xs text-neutral-600 capitalize",children:t})]},t)})})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-card p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-mbnb-navy mb-4",children:"Activit\xe9s R\xe9centes"}),(0,a.jsxs)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:[null==w?void 0:w.map(e=>(0,a.jsxs)("div",{className:"flex items-start gap-3 pb-3 border-b border-neutral-100 last:border-0",children:[(e=>{switch(e){case"user_signup":return(0,a.jsx)(o,{className:"w-4 h-4 text-mbnb-teal"});case"property_listed":return(0,a.jsx)(m,{className:"w-4 h-4 text-mbnb-navy"});case"booking_created":return(0,a.jsx)(x,{className:"w-4 h-4 text-mbnb-coral"});case"payment_processed":return(0,a.jsx)(h,{className:"w-4 h-4 text-success-600"});case"review_posted":return(0,a.jsx)(u.A,{className:"w-4 h-4 text-mbnb-sky"});default:return(0,a.jsx)(p.A,{className:"w-4 h-4 text-neutral-400"})}})(e.type),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm text-neutral-800 truncate",children:e.description}),(0,a.jsx)("p",{className:"text-xs text-neutral-500 mt-1",children:(0,c.Yq)(e.timestamp)})]})]},e.id)),(!w||0===w.length)&&(0,a.jsx)("p",{className:"text-center text-neutral-500 py-8",children:"Aucune activit\xe9 r\xe9cente"})]})]})]}),(0,a.jsxs)("div",{className:"mt-8 grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("button",{onClick:()=>window.location.href="/admin/users",className:"p-4 bg-white rounded-lg shadow-card hover:shadow-elevated transition-shadow flex items-center justify-between group",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(o,{className:"w-6 h-6 text-mbnb-teal"}),(0,a.jsx)("span",{className:"font-medium text-neutral-700",children:"G\xe9rer les utilisateurs"})]}),(0,a.jsx)("span",{className:"text-mbnb-coral group-hover:translate-x-1 transition-transform",children:"→"})]}),(0,a.jsxs)("button",{onClick:()=>window.location.href="/admin/properties",className:"p-4 bg-white rounded-lg shadow-card hover:shadow-elevated transition-shadow flex items-center justify-between group",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(m,{className:"w-6 h-6 text-mbnb-navy"}),(0,a.jsx)("span",{className:"font-medium text-neutral-700",children:"G\xe9rer les propri\xe9t\xe9s"})]}),(0,a.jsx)("span",{className:"text-mbnb-coral group-hover:translate-x-1 transition-transform",children:"→"})]}),(0,a.jsxs)("button",{onClick:()=>window.location.href="/admin/reports",className:"p-4 bg-white rounded-lg shadow-card hover:shadow-elevated transition-shadow flex items-center justify-between group",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(g,{className:"w-6 h-6 text-mbnb-coral"}),(0,a.jsx)("span",{className:"font-medium text-neutral-700",children:"Rapports d\xe9taill\xe9s"})]}),(0,a.jsx)("span",{className:"text-mbnb-coral group-hover:translate-x-1 transition-transform",children:"→"})]})]})]})]})}},79535:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(1222).A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}},e=>{e.O(0,[6799,2995,9289,587,18,7358],()=>e(e.s=12584)),_N_E=e.O()}]);
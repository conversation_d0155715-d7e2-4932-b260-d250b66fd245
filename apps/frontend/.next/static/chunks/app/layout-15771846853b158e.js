(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{13210:()=>{},31201:(e,t,n)=>{"use strict";n.d(t,{Providers:()=>o});var s=n(54568),a=n(7620),r=n(91981),i=n(4869);function o(e){let{children:t}=e,[n]=(0,a.useState)(()=>new r.E({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1,retry:(e,t)=>(null==t||!t.status||!(t.status>=400)||!(t.status<500))&&e<3},mutations:{retry:(e,t)=>(null==t||!t.status||!(t.status>=400)||!(t.status<500))&&e<2}}}));return(0,s.jsxs)(i.Ht,{client:n,children:[t,!1]})}},58054:(e,t,n)=>{"use strict";function s(){return new Date}function a(e){return"string"==typeof e?new Date(e):e}function r(e,t){let n=new Date(e.getTime());return n.setDate(n.getDate()+t),n}n.d(t,{Io:()=>a,MZ:()=>s,fi:()=>r})},86726:(e,t,n)=>{Promise.resolve().then(n.bind(n,31201)),Promise.resolve().then(n.bind(n,92576)),Promise.resolve().then(n.bind(n,41470)),Promise.resolve().then(n.t.bind(n,19664,23)),Promise.resolve().then(n.t.bind(n,90995,23)),Promise.resolve().then(n.t.bind(n,3965,23)),Promise.resolve().then(n.t.bind(n,9774,23)),Promise.resolve().then(n.t.bind(n,13210,23)),Promise.resolve().then(n.bind(n,93558))},92576:(e,t,n)=>{"use strict";n.d(t,{Analytics:()=>o});var s=n(54568),a=n(23792),r=n(58054),i=n(40459);function o(){let e=i.env.NEXT_PUBLIC_GA_MEASUREMENT_ID,t=i.env.NEXT_PUBLIC_GTM_ID,n=i.env.NEXT_PUBLIC_FB_PIXEL_ID;return(0,s.jsxs)(s.Fragment,{children:[e&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.default,{src:"https://www.googletagmanager.com/gtag/js?id=".concat(e),strategy:"afterInteractive"}),(0,s.jsx)(a.default,{id:"google-analytics",strategy:"afterInteractive",children:"\n              window.dataLayer = window.dataLayer || [];\n              function gtag(){dataLayer.push(arguments);}\n              gtag('js', ".concat(JSON.stringify((0,r.MZ)()),");\n              gtag('config', '").concat(e,"', {\n                page_title: document.title,\n                page_location: window.location.href,\n              });\n            ")})]}),t&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.default,{id:"google-tag-manager",strategy:"afterInteractive",children:"\n              (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':\n              ".concat((0,r.MZ)().getTime(),",event:'gtm.js'});var f=d.getElementsByTagName(s)[0],\n              j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=\n              'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);\n              })(window,document,'script','dataLayer','").concat(t,"');\n            ")}),(0,s.jsx)("noscript",{dangerouslySetInnerHTML:{__html:'<iframe src="https://www.googletagmanager.com/ns.html?id='.concat(t,'" height="0" width="0" style="display:none;visibility:hidden"></iframe>')}})]}),n&&(0,s.jsx)(a.default,{id:"facebook-pixel",strategy:"afterInteractive",children:"\n            !function(f,b,e,v,n,t,s)\n            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?\n            n.callMethod.apply(n,arguments):n.queue.push(arguments)};\n            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';\n            n.queue=[];t=b.createElement(e);t.async=!0;\n            t.src=v;s=b.getElementsByTagName(e)[0];\n            s.parentNode.insertBefore(t,s)}(window, document,'script',\n            'https://connect.facebook.net/en_US/fbevents.js');\n            fbq('init', '".concat(n,"');\n            fbq('track', 'PageView');\n          ")})]})}}},e=>{e.O(0,[1272,9857,9249,9664,6799,3558,7638,8509,1470,587,18,7358],()=>e(e.s=86726)),_N_E=e.O()}]);
{"config": {"configFile": "/Users/<USER>/Desktop/mbnb-copy/apps/frontend/playwright.config.ts", "rootDir": "/Users/<USER>/Desktop/mbnb-copy/apps/frontend/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report"}], ["json", {"outputFile": "test-results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Desktop/mbnb-copy/apps/frontend/tests/results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Desktop/mbnb-copy/apps/frontend/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/mbnb-copy/apps/frontend/tests/results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Desktop/mbnb-copy/apps/frontend/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/mbnb-copy/apps/frontend/tests/results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Desktop/mbnb-copy/apps/frontend/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/mbnb-copy/apps/frontend/tests/results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "mobile-chrome", "name": "mobile-chrome", "testDir": "/Users/<USER>/Desktop/mbnb-copy/apps/frontend/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/mbnb-copy/apps/frontend/tests/results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "mobile-safari", "name": "mobile-safari", "testDir": "/Users/<USER>/Desktop/mbnb-copy/apps/frontend/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/mbnb-copy/apps/frontend/tests/results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "tablet-ipad", "name": "tablet-ipad", "testDir": "/Users/<USER>/Desktop/mbnb-copy/apps/frontend/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/mbnb-copy/apps/frontend/tests/results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "arabic-rtl", "name": "arabic-rtl", "testDir": "/Users/<USER>/Desktop/mbnb-copy/apps/frontend/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/mbnb-copy/apps/frontend/tests/results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "accessibility", "name": "accessibility", "testDir": "/Users/<USER>/Desktop/mbnb-copy/apps/frontend/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.55.0", "workers": 6, "webServer": {"command": "npm run dev", "url": "http://localhost:3010", "reuseExistingServer": true, "timeout": 120000}}, "suites": [], "errors": [{"message": "Error: No tests found", "stack": "Error: No tests found"}], "stats": {"startTime": "2025-09-22T13:20:12.328Z", "duration": 31763.505, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import LanguageSelector from './LanguageSelector';

// Mock i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: {
      changeLanguage: vi.fn(),
      language: 'fr'
    }
  })
}));

describe('LanguageSelector', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render language selector button', () => {
    render(<LanguageSelector />);
    const button = screen.getByRole('button');
    expect(button).toBeDefined();
  });

  it('should display current language', () => {
    render(<LanguageSelector />);
    const button = screen.getByRole('button');
    expect(button.textContent).toContain('FR');
  });

  it('should show language options when clicked', () => {
    render(<LanguageSelector />);
    const button = screen.getByRole('button');

    fireEvent.click(button);

    // Should show all language options
    expect(screen.getByText('Français')).toBeDefined();
    expect(screen.getByText('English')).toBeDefined();
    expect(screen.getByText('العربية')).toBeDefined();
    expect(screen.getByText('Español')).toBeDefined();
  });

  it('should change language when option is selected', () => {
    render(<LanguageSelector />);
    const button = screen.getByRole('button');

    fireEvent.click(button);

    const englishOption = screen.getByText('English');
    fireEvent.click(englishOption);

    // Should close the dropdown after selection
    expect(screen.queryByText('Español')).toBeNull();
  });

  it('should display the correct flag for each language', () => {
    render(<LanguageSelector />);
    const button = screen.getByRole('button');

    // Check for French flag (current language)
    expect(button.innerHTML).toContain('🇫🇷');

    fireEvent.click(button);

    // Check for other flags in dropdown
    const dropdownContent = screen.getByRole('menu', { hidden: true });
    expect(dropdownContent.innerHTML).toContain('🇬🇧'); // English
    expect(dropdownContent.innerHTML).toContain('🇲🇦'); // Arabic
    expect(dropdownContent.innerHTML).toContain('🇪🇸'); // Spanish
  });

  it('should support all required Mbnb languages', () => {
    render(<LanguageSelector />);
    const button = screen.getByRole('button');

    fireEvent.click(button);

    // Test all 7 languages as per Mbnb requirements
    const languages = [
      'Français',    // FR
      'العربية',     // AR
      'English',     // EN
      'Español',     // ES
      '中文',        // ZH
      'Русский',     // RU
      'الدارجة'      // Darija (AR-MA)
    ];

    languages.forEach(lang => {
      if (lang !== 'الدارجة') { // Darija might be optional in UI
        expect(screen.getByText(lang)).toBeDefined();
      }
    });
  });

  it('should handle keyboard navigation', () => {
    render(<LanguageSelector />);
    const button = screen.getByRole('button');

    // Open dropdown with Enter key
    fireEvent.keyDown(button, { key: 'Enter' });
    expect(screen.getByText('English')).toBeDefined();

    // Close dropdown with Escape key
    fireEvent.keyDown(document, { key: 'Escape' });
    expect(screen.queryByText('English')).toBeNull();
  });

  it('should be accessible with ARIA attributes', () => {
    render(<LanguageSelector />);
    const button = screen.getByRole('button');

    expect(button.getAttribute('aria-label')).toBeTruthy();
    expect(button.getAttribute('aria-expanded')).toBe('false');

    fireEvent.click(button);
    expect(button.getAttribute('aria-expanded')).toBe('true');
  });

  it('should persist language selection', () => {
    const mockLocalStorage = {
      getItem: vi.fn(),
      setItem: vi.fn(),
      clear: vi.fn()
    };

    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true
    });

    render(<LanguageSelector />);
    const button = screen.getByRole('button');

    fireEvent.click(button);
    const englishOption = screen.getByText('English');
    fireEvent.click(englishOption);

    expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
      'mbnb-language',
      'en'
    );
  });
});
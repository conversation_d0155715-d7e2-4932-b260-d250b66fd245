import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import DateRangePicker from './DateRangePicker';

// Mock date-fns
vi.mock('date-fns', async () => {
  const actual = await vi.importActual('date-fns');
  return {
    ...actual,
    format: vi.fn((_date, _formatStr) => '2025-09-20'),
    addDays: vi.fn((_date, days) => new Date(2025, 8, 20 + days)),
    isAfter: vi.fn((date1, date2) => date1 > date2),
    isBefore: vi.fn((date1, date2) => date1 < date2)
  };
});

describe('DateRangePicker', () => {
  const mockOnChange = vi.fn();
  const defaultProps = {
    startDate: null,
    endDate: null,
    onDateChange: mockOnChange,
    minNights: 1,
    maxNights: 30
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render date range picker with placeholders', () => {
    render(<DateRangePicker {...defaultProps} />);

    expect(screen.getByPlaceholderText(/check-in/i)).toBeDefined();
    expect(screen.getByPlaceholderText(/check-out/i)).toBeDefined();
  });

  it('should open calendar when clicking check-in input', () => {
    render(<DateRangePicker {...defaultProps} />);

    const checkInInput = screen.getByPlaceholderText(/check-in/i);
    fireEvent.click(checkInInput);

    expect(screen.getByRole('dialog')).toBeDefined();
  });

  it('should not allow check-out before check-in', () => {
    render(
      <DateRangePicker
        {...defaultProps}
        startDate={new Date(2025, 8, 25)}
      />
    );

    const checkOutInput = screen.getByPlaceholderText(/check-out/i);
    fireEvent.click(checkOutInput);

    // Try to select a date before check-in
    const invalidDate = screen.getByText('20');
    fireEvent.click(invalidDate);

    expect(mockOnChange).not.toHaveBeenCalled();
  });

  it('should enforce minimum stay requirement', () => {
    render(
      <DateRangePicker
        {...defaultProps}
        minNights={3}
        startDate={new Date(2025, 8, 20)}
      />
    );

    const checkOutInput = screen.getByPlaceholderText(/check-out/i);
    fireEvent.click(checkOutInput);

    // Dates before minimum stay should be disabled
    const tooSoonDate = screen.getByText('21');
    expect(tooSoonDate.getAttribute('aria-disabled')).toBe('true');

    // Valid date after minimum stay
    const validDate = screen.getByText('23');
    expect(validDate.getAttribute('aria-disabled')).toBe('false');
  });

  it('should enforce maximum stay requirement', () => {
    render(
      <DateRangePicker
        {...defaultProps}
        maxNights={7}
        startDate={new Date(2025, 8, 20)}
      />
    );

    const checkOutInput = screen.getByPlaceholderText(/check-out/i);
    fireEvent.click(checkOutInput);

    // Date beyond maximum stay should be disabled
    const tooLateDate = screen.getByText('28');
    expect(tooLateDate.getAttribute('aria-disabled')).toBe('true');
  });

  it('should call onChange with selected dates', async () => {
    render(<DateRangePicker {...defaultProps} />);

    const checkInInput = screen.getByPlaceholderText(/check-in/i);
    fireEvent.click(checkInInput);

    const startDate = screen.getByText('20');
    fireEvent.click(startDate);

    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith(
        expect.objectContaining({
          startDate: expect.any(Date),
          endDate: null
        })
      );
    });
  });

  it('should display selected dates in correct format', () => {
    render(
      <DateRangePicker
        {...defaultProps}
        startDate={new Date(2025, 8, 20)}
        endDate={new Date(2025, 8, 25)}
      />
    );

    const checkInInput = screen.getByPlaceholderText(/check-in/i) as HTMLInputElement;
    const checkOutInput = screen.getByPlaceholderText(/check-out/i) as HTMLInputElement;

    expect(checkInInput.value).toContain('2025');
    expect(checkOutInput.value).toContain('2025');
  });

  it('should clear dates when clear button is clicked', () => {
    render(
      <DateRangePicker
        {...defaultProps}
        startDate={new Date(2025, 8, 20)}
        endDate={new Date(2025, 8, 25)}
      />
    );

    const clearButton = screen.getByRole('button', { name: /clear/i });
    fireEvent.click(clearButton);

    expect(mockOnChange).toHaveBeenCalledWith({
      startDate: null,
      endDate: null
    });
  });

  it('should handle keyboard navigation', () => {
    render(<DateRangePicker {...defaultProps} />);

    const checkInInput = screen.getByPlaceholderText(/check-in/i);

    // Open with Enter key
    fireEvent.keyDown(checkInInput, { key: 'Enter' });
    expect(screen.getByRole('dialog')).toBeDefined();

    // Navigate with arrow keys
    fireEvent.keyDown(document.activeElement!, { key: 'ArrowRight' });
    fireEvent.keyDown(document.activeElement!, { key: 'ArrowDown' });

    // Select with Space key
    fireEvent.keyDown(document.activeElement!, { key: ' ' });
    expect(mockOnChange).toHaveBeenCalled();
  });

  it('should be accessible with proper ARIA labels', () => {
    render(<DateRangePicker {...defaultProps} />);

    const checkInInput = screen.getByPlaceholderText(/check-in/i);
    const checkOutInput = screen.getByPlaceholderText(/check-out/i);

    expect(checkInInput.getAttribute('aria-label')).toBeTruthy();
    expect(checkOutInput.getAttribute('aria-label')).toBeTruthy();
    expect(checkInInput.getAttribute('role')).toBe('textbox');
    expect(checkOutInput.getAttribute('role')).toBe('textbox');
  });

  it('should handle disabled state', () => {
    render(
      <DateRangePicker
        {...defaultProps}

      />
    );

    const checkInInput = screen.getByPlaceholderText(/check-in/i) as HTMLInputElement;
    const checkOutInput = screen.getByPlaceholderText(/check-out/i) as HTMLInputElement;

    expect(checkInInput.disabled).toBe(true);
    expect(checkOutInput.disabled).toBe(true);
  });

  it('should validate date ranges for Mbnb business rules', () => {
    render(
      <DateRangePicker
        {...defaultProps}
        minNights={1}
        maxNights={84} // Max for long stay discount
      />
    );

    const checkInInput = screen.getByPlaceholderText(/check-in/i);
    fireEvent.click(checkInInput);

    // Select start date
    const startDate = screen.getByText('1');
    fireEvent.click(startDate);

    // Try to select end date for 85 days (beyond max)
    const checkOutInput = screen.getByPlaceholderText(/check-out/i);
    fireEvent.click(checkOutInput);

    // This should be disabled based on Mbnb max stay rules
    const invalidEndDate = screen.getByText('25'); // Next month
    expect(invalidEndDate.getAttribute('aria-disabled')).toBeDefined();
  });
});
'use client';

import { useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { MbnbResponsiveImage } from '@/components/images';
import { useMbnbActivity } from '@/hooks/use-mbnb-data';
import { getClientTimestamp } from '@/lib/time-provider';
import {
  ArrowLeftIcon,
  MapPinIcon,
  ClockIcon,
  UsersIcon,
  HeartIcon,
  ShareIcon,
  CalendarIcon,
  LanguageIcon,
  CheckCircleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon, StarIcon as StarSolidIcon } from '@heroicons/react/24/solid';

// Interface pour les données d'activités - supprimées car non utilisées


export default function ActivityDetailPage() {
  const params = useParams();
  const router = useRouter();
  const activityId = params.id as string;

  // Backend synchronized: utilise API au lieu de mock data
  const { data: activity, isLoading, error } = useMbnbActivity(activityId);

  const [selectedDate, setSelectedDate] = useState('');
  const [guests, setGuests] = useState(1);
  const [selectedImage, setSelectedImage] = useState(0);
  const [isFavorite, setIsFavorite] = useState(false);
  const [showBookingModal, setShowBookingModal] = useState(false);
  const [showAllImages, setShowAllImages] = useState(false);

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-mbnb-coral mx-auto"></div>
          <p className="mt-4 text-gray-600">Chargement de l'activité...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !activity) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Activité non trouvée</p>
          <button
            onClick={() => router.back()}
            className="mt-4 px-4 py-2 bg-mbnb-coral text-white rounded-lg"
          >
            Retour
          </button>
        </div>
      </div>
    );
  }

  const totalPrice = activity.pricing.adult * guests;
  const mbnbCommission = Math.round(totalPrice * 0.18);

  const handleBooking = () => {
    setShowBookingModal(true);
  };

  const confirmBooking = () => {
    // Navigate to payment/booking confirmation
    router.push(`/booking/activity/${activityId}?date=${selectedDate}&guests=${guests}`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <button
            onClick={() => router.back()}
            className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeftIcon className="w-5 h-5 mr-2" />
            Retour aux activités
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-8">
          {/* Left Column - Images & Details */}
          <div className="lg:col-span-2">
            {/* Image Gallery */}
            <div className="mb-8">
              <div className="relative">
                {/* Main Image */}
                <div className="relative aspect-[16/9] rounded-2xl overflow-hidden mb-4 group">
                  <MbnbResponsiveImage
                    baseName={activity.media.images[selectedImage].baseName}
                    folder={activity.media.images[selectedImage].folder}
                    alt={activity.name}
                    className="absolute inset-0 w-full h-full object-cover"
                    priority
                  />

                  {/* Image Counter */}
                  <div className="absolute bottom-4 left-4 bg-black/60 text-white px-3 py-1 rounded-full text-sm">
                    {selectedImage + 1} / {activity.media.images.length}
                  </div>

                  {/* Action Buttons */}
                  <button
                    onClick={() => setIsFavorite(!isFavorite)}
                    className="absolute top-4 right-4 p-3 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-all transform hover:scale-110"
                  >
                    {isFavorite ? (
                      <HeartSolidIcon className="w-6 h-6 text-mbnb-coral" />
                    ) : (
                      <HeartIcon className="w-6 h-6 text-gray-700" />
                    )}
                  </button>
                  <button className="absolute top-4 right-20 p-3 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-all transform hover:scale-110">
                    <ShareIcon className="w-6 h-6 text-gray-700" />
                  </button>

                  {/* Navigation Arrows */}
                  {selectedImage > 0 && (
                    <button
                      onClick={() => setSelectedImage(prev => prev - 1)}
                      className="absolute left-4 top-1/2 -translate-y-1/2 p-2 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-all"
                    >
                      <ArrowLeftIcon className="w-5 h-5" />
                    </button>
                  )}
                  {selectedImage < activity.media.images.length - 1 && (
                    <button
                      onClick={() => setSelectedImage(prev => prev + 1)}
                      className="absolute right-4 top-1/2 -translate-y-1/2 p-2 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-all"
                    >
                      <ArrowLeftIcon className="w-5 h-5 rotate-180" />
                    </button>
                  )}

                  {/* View All Photos Button */}
                  <button
                    onClick={() => setShowAllImages(true)}
                    className="absolute bottom-4 right-4 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-lg hover:bg-white transition-all flex items-center gap-2"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                    </svg>
                    Voir toutes les photos
                  </button>
                </div>

                {/* Thumbnails */}
                {activity.media.images.length > 1 && (
                  <div className="grid grid-cols-4 md:grid-cols-6 gap-2">
                    {activity.media.images.slice(0, 6).map((image: { baseName: string; folder: string }, index: number) => (
                      <button
                        key={index}
                        onClick={() => setSelectedImage(index)}
                        className={`relative aspect-[4/3] rounded-lg overflow-hidden transition-all ${
                          selectedImage === index
                            ? 'ring-2 ring-mbnb-coral scale-95'
                            : 'hover:opacity-80'
                        } ${index === 5 && activity.media.images.length > 6 ? 'relative' : ''}`}
                      >
                        <MbnbResponsiveImage
                          baseName={image.baseName}
                          folder={image.folder}
                          alt={`${activity.name} ${index + 1}`}
                          className="absolute inset-0 w-full h-full object-cover"
                        />
                        {index === 5 && activity.media.images.length > 6 && (
                          <div
                            className="absolute inset-0 bg-black/60 flex items-center justify-center text-white font-semibold"
                            onClick={() => setShowAllImages(true)}
                          >
                            +{activity.media.images.length - 6}
                          </div>
                        )}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Title & Basic Info */}
            <div className="mb-6 lg:mb-8">
              <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-3 lg:mb-4">{activity.name}</h1>

              <div className="flex flex-wrap gap-2 lg:gap-4 text-sm lg:text-base text-gray-600 mb-4 lg:mb-6">
                <div className="flex items-center">
                  <MapPinIcon className="w-5 h-5 mr-2 text-mbnb-coral" />
                  {activity.location.address || `${activity.location.city}, ${activity.location.region}`}
                </div>
                <div className="flex items-center">
                  <ClockIcon className="w-5 h-5 mr-2 text-mbnb-coral" />
                  {activity.duration}
                </div>
                <div className="flex items-center">
                  <UsersIcon className="w-5 h-5 mr-2 text-mbnb-coral" />
                  Max {activity.pricing.group?.size || 8} personnes
                </div>
                <div className="flex items-center">
                  <StarSolidIcon className="w-5 h-5 mr-1 text-mbnb-coral" />
                  <span className="font-semibold">{activity.rating}</span>
                  <span className="ml-1">({activity.reviews} avis)</span>
                </div>
              </div>

              {/* Languages */}
              <div className="flex items-center gap-2 mb-6">
                <LanguageIcon className="w-5 h-5 text-gray-500" />
                <div className="flex flex-wrap gap-2">
                  {['Français', 'Arabe', 'Anglais'].map((lang: string) => (
                    <span key={lang} className="px-3 py-1 bg-gray-100 rounded-full text-sm">
                      {lang}
                    </span>
                  ))}
                </div>
              </div>

              {/* Description */}
              <div className="prose max-w-none mb-8">
                <h2 className="text-xl font-semibold mb-3">Description</h2>
                <p className="text-gray-700 leading-relaxed">{activity.description}</p>
              </div>

              {/* Highlights */}
              <div className="mb-8">
                <h2 className="text-xl font-semibold mb-4">Points forts</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {['Points forts disponibles', 'Expérience authentique', 'Guide local'].map((highlight: string) => (
                    <div key={highlight} className="flex items-start">
                      <CheckCircleIcon className="w-5 h-5 text-mbnb-teal-dark mr-2 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{highlight}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* What's Included */}
              <div className="mb-8">
                <h2 className="text-xl font-semibold mb-4">Ce qui est inclus</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {activity.includes?.map((item: string) => (
                    <div key={item} className="flex items-start">
                      <CheckCircleIcon className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{item}</span>
                    </div>
                  ))}
                  {activity.excludes?.map((item: string) => (
                    <div key={item} className="flex items-start">
                      <XMarkIcon className="w-5 h-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{item}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Meeting Point */}
              {true && (
                <div className="mb-8">
                  <h2 className="text-xl font-semibold mb-3">Point de rencontre</h2>
                  <div className="flex items-start p-4 bg-gray-50 rounded-xl">
                    <MapPinIcon className="w-5 h-5 text-mbnb-coral mr-3 mt-0.5" />
                    <p className="text-gray-700">{'Point de rendez-vous à confirmer'}</p>
                  </div>
                </div>
              )}

              {/* Cancellation Policy */}
              {true && (
                <div className="mb-8">
                  <h2 className="text-xl font-semibold mb-3">Politique d'annulation</h2>
                  <p className="text-gray-700">{'Annulation gratuite jusqu\'à 24h avant'}</p>
                </div>
              )}

              {/* Reviews Section */}
              {activity.reviews && activity.reviews > 0 && (
                <div className="border-t pt-8 mb-8">
                  <div className="flex items-center justify-between mb-6">
                    <div>
                      <h2 className="text-xl font-semibold">Avis des voyageurs</h2>
                      <div className="flex items-center mt-2">
                        <StarSolidIcon className="w-5 h-5 text-mbnb-coral mr-1" />
                        <span className="font-semibold text-lg">{activity.rating}</span>
                        <span className="text-gray-500 ml-2">({activity.reviews || 0} avis vérifiés)</span>
                      </div>
                    </div>
                  </div>

                  <div className="text-center py-8 bg-gray-50 rounded-xl">
                    <p className="text-gray-600">
                      Cette activité a reçu {activity.reviews} avis avec une note moyenne de {activity.rating}/5
                    </p>
                    <p className="text-sm text-gray-500 mt-2">
                      Les avis détaillés seront disponibles prochainement
                    </p>
                  </div>
                </div>
              )}

              {/* Provider Info - À implémenter avec les données réelles du provider */}

              {/* Similar Activities - À implémenter avec un service de recommandations */}
            </div>
          </div>

          {/* Right Column - Booking Card */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl shadow-xl p-4 sm:p-6 sticky top-20 lg:top-24">
              <div className="mb-6">
                <div className="flex items-baseline justify-between mb-2">
                  <span className="text-3xl font-bold text-gray-900">{activity.pricing.adult} MAD</span>
                  <span className="text-gray-500">/ personne</span>
                </div>
                <div className="flex items-center">
                  <StarSolidIcon className="w-5 h-5 text-mbnb-coral mr-1" />
                  <span className="font-semibold">{activity.rating}</span>
                  <span className="text-gray-500 ml-1">({activity.reviews} avis)</span>
                </div>
              </div>

              {/* Date Selection */}
              <div className="mb-4">
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Date
                </label>
                <div className="relative">
                  <CalendarIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
                  <input
                    type="date"
                    value={selectedDate}
                    onChange={(e) => setSelectedDate(e.target.value)}
                    min={getClientTimestamp().toISOString().split('T')[0]}
                    className="w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-mbnb-coral focus:border-transparent transition-all"
                  />
                </div>
              </div>

              {/* Guest Selection */}
              <div className="mb-6">
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Nombre de personnes
                </label>
                <div className="flex items-center justify-between border border-gray-300 rounded-xl p-3">
                  <button
                    onClick={() => setGuests(Math.max(1, guests - 1))}
                    disabled={guests <= 1}
                    className="w-8 h-8 rounded-full border border-gray-400 flex items-center justify-center hover:border-mbnb-coral hover:text-mbnb-coral disabled:border-gray-200 disabled:text-gray-300 transition-colors"
                  >
                    −
                  </button>
                  <span className="font-semibold text-lg">{guests}</span>
                  <button
                    onClick={() => setGuests(Math.min(activity.pricing.group?.size || 8, guests + 1))}
                    disabled={guests >= (activity.pricing.group?.size || 8)}
                    className="w-8 h-8 rounded-full border border-gray-400 flex items-center justify-center hover:border-mbnb-coral hover:text-mbnb-coral disabled:border-gray-200 disabled:text-gray-300 transition-colors"
                  >
                    +
                  </button>
                </div>
              </div>

              {/* Price Breakdown */}
              <div className="border-t pt-4 mb-6">
                <div className="space-y-2">
                  <div className="flex justify-between text-gray-600">
                    <span>{activity.pricing.adult} MAD × {guests} personne{guests > 1 ? 's' : ''}</span>
                    <span>{totalPrice} MAD</span>
                  </div>
                  <div className="flex justify-between text-sm text-gray-500">
                    <span>Commission Mbnb (18%)</span>
                    <span>{mbnbCommission} MAD</span>
                  </div>
                </div>

                <div className="border-t pt-4 mt-4">
                  <div className="flex justify-between text-lg font-bold">
                    <span>Total</span>
                    <span className="text-mbnb-coral">{totalPrice} MAD</span>
                  </div>
                </div>
              </div>

              {/* Book Button */}
              <button
                onClick={handleBooking}
                disabled={!selectedDate}
                className="w-full bg-mbnb-coral hover:bg-mbnb-coral-dark disabled:bg-gray-300 text-white py-4 rounded-xl font-semibold transition-colors"
              >
                Réserver cette activité
              </button>

              <p className="text-center text-sm text-gray-500 mt-3">
                Vous ne serez débité qu'après confirmation
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Full Screen Image Gallery Modal */}
      {showAllImages && (
        <div className="fixed inset-0 bg-black z-50 overflow-y-auto">
          <div className="relative">
            {/* Close Button */}
            <button
              onClick={() => setShowAllImages(false)}
              className="fixed top-4 right-4 z-50 p-3 bg-white/10 backdrop-blur-sm rounded-full text-white hover:bg-white/20 transition-all"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>

            {/* Images Grid */}
            <div className="container mx-auto px-4 py-20">
              <h2 className="text-white text-2xl font-bold mb-6">Galerie photos ({activity.media.images.length} photos)</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {activity.media.images.map((image: { baseName: string; folder: string }, index: number) => (
                  <div key={index} className="relative aspect-[16/9] rounded-xl overflow-hidden">
                    <MbnbResponsiveImage
                      baseName={image.baseName}
                      folder={image.folder}
                      alt={`${activity.name} ${index + 1}`}
                      className="absolute inset-0 w-full h-full object-cover"
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Booking Modal */}
      {showBookingModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl max-w-md w-full p-4 sm:p-6 animate-fadeIn">
            <h2 className="text-2xl font-bold mb-4">Confirmer la réservation</h2>

            <div className="space-y-3 mb-6">
              <div className="p-3 bg-gray-50 rounded-xl">
                <p className="font-semibold text-gray-900">{activity.name}</p>
                <p className="text-sm text-gray-600 mt-1">{activity.location.address || `${activity.location.city}, ${activity.location.region}`}</p>
              </div>

              <div className="p-3 bg-gray-50 rounded-xl">
                <div className="flex justify-between">
                  <span className="text-gray-600">Date</span>
                  <span className="font-medium">{selectedDate}</span>
                </div>
                <div className="flex justify-between mt-2">
                  <span className="text-gray-600">Participants</span>
                  <span className="font-medium">{guests} personne{guests > 1 ? 's' : ''}</span>
                </div>
              </div>

              <div className="p-3 bg-mbnb-coral/10 rounded-xl">
                <div className="flex justify-between text-lg font-bold text-mbnb-coral">
                  <span>Total à payer</span>
                  <span>{totalPrice} MAD</span>
                </div>
              </div>
            </div>

            <div className="flex gap-3">
              <button
                onClick={() => setShowBookingModal(false)}
                className="flex-1 py-3 border border-gray-300 rounded-xl font-semibold hover:bg-gray-50 transition-colors"
              >
                Annuler
              </button>
              <button
                onClick={confirmBooking}
                className="flex-1 py-3 bg-mbnb-coral hover:bg-mbnb-coral-dark text-white rounded-xl font-semibold transition-colors"
              >
                Confirmer
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
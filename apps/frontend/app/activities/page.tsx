import { Metadata } from 'next';
import { MbnbResponsiveImage } from '@/components/images';
import Link from 'next/link';
import { getClientTimestamp } from '@/lib/time-provider';
import {
  MapPinIcon,
  ClockIcon,
  UserGroupIcon,
  StarIcon,
  TagIcon,
  TrophyIcon,
  SparklesIcon,
  GlobeAltIcon,
  ArrowRightIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';

// Import Domain Types (MbnbActivity supprimé - interface locale utilisée)

// Types synchronisés avec backend supprimés (inutilisés)

// Types supprimés - utilisation des types backend via @mbnb/types

export const metadata: Metadata = {
  title: 'TOP 100 Activités Mbnb - Expériences authentiques au Maroc | Guides locaux certifiés',
  description: 'Découvrez le TOP 100 des activités authentiques au Maroc avec Mbnb. Guides locaux certifiés, expériences uniques, réservation instantanée avec commission intégrée.',
};

// UI interface indépendante synchronisée avec backend (FRONTEND ESCLAVE)
interface MbnbActivityUI {
  // Backend fields from MbnbActivity
  id: string
  providerId: string
  title: string
  titleAr?: string
  description: string
  descriptionAr?: string
  pricePerPerson: number
  pricePerGroup?: number
  category: ActivityCategory
  difficulty: ActivityDifficulty
  status: ActivityStatus
  duration: number  // in minutes
  maxGroupSize: number
  minGroupSize: number
  location: string
  latitude?: number
  longitude?: number
  languages?: string[]
  included?: string[]
  excluded?: string[]
  requirements?: string[]
  cancellationPolicy?: string
  instantBooking: boolean
  commission: number
  rating?: number
  reviewCount: number
  featured: boolean
  rank?: number
  createdAt: Date
  updatedAt: Date
  // UI-specific extensions
  image: { baseName: string; folder: string }
  guide?: string
  originalPrice?: number
  currency: string
}

const topActivities: MbnbActivityUI[] = [
  {
    // Backend required fields
    id: 'act-sahara-001',
    providerId: 'prov-hassan-001',
    title: 'Excursion chameau Sahara & nuit étoiles Merzouga',
    description: 'Aventure authentique dans les dunes du Sahara avec guide berbère expert et nuit sous les étoiles.',
    pricePerPerson: 450,
    category: ActivityCategory.ADVENTURE,
    difficulty: ActivityDifficulty.MODERATE,
    status: 'ACTIVE',
    duration: 2880, // 48 heures en minutes
    maxGroupSize: 8,
    minGroupSize: 2,
    location: 'Merzouga',
    languages: ['Français', 'Arabe', 'Anglais'],
    included: ['Transport 4x4', 'Repas traditionnels', 'Tente berbère', 'Guide expert'],
    instantBooking: true,
    commission: 18,
    rating: 4.9,
    reviewCount: 847,
    featured: true,
    rank: 1,
    createdAt: getClientTimestamp(),
    updatedAt: getClientTimestamp(),
    // UI extensions
    image: { baseName: 'experience_taghazout_surf_vague_001', folder: 'impact-modere/ACTE_2_EXPLORATION' },
    guide: 'Hassan Berbère',
    originalPrice: 650,
    currency: 'MAD'
  },
  {
    // Backend required fields
    id: 'act-fes-002',
    providerId: 'prov-abdelali-002',
    title: 'Visite guidée médina Fès avec artisan zelligeur',
    description: 'Immersion dans les secrets de la médina de Fès avec maître artisan et atelier zellige privé.',
    pricePerPerson: 280,
    category: ActivityCategory.CULTURAL,
    difficulty: ActivityDifficulty.EASY,
    status: 'ACTIVE',
    duration: 360, // 6 heures en minutes
    maxGroupSize: 6,
    minGroupSize: 1,
    location: 'Fès',
    languages: ['Français', 'Arabe'],
    included: ['Guide expert', 'Atelier zellige', 'Thé à la menthe', 'Certificat artisanal'],
    instantBooking: true,
    commission: 18,
    rating: 4.8,
    reviewCount: 623,
    featured: false,
    rank: 2,
    createdAt: getClientTimestamp(),
    updatedAt: getClientTimestamp(),
    // UI extensions
    image: { baseName: 'tourisme_casablanca_corniche_palmiers_ocean_001', folder: 'impact-modere/ACTE_2_EXPLORATION' },
    guide: 'Maître Abdelali',
    originalPrice: 320,
    currency: 'MAD'
  },
  {
    // Backend required fields
    id: 'act-essaouira-003',
    providerId: 'prov-surf-003',
    title: 'Surf & Yoga Essaouira - Retraite océanique',
    description: 'Session surf matinale suivie de yoga face à l\'océan Atlantique avec instructeurs certifiés.',
    pricePerPerson: 320,
    category: ActivityCategory.ADVENTURE,
    difficulty: ActivityDifficulty.EASY,
    status: 'ACTIVE',
    duration: 240, // 4 heures en minutes
    maxGroupSize: 10,
    minGroupSize: 3,
    location: 'Essaouira',
    languages: ['Français', 'Anglais'],
    included: ['Équipement surf', 'Tapis yoga', 'Collation bio', 'Photos souvenirs'],
    instantBooking: true,
    commission: 18,
    rating: 4.7,
    reviewCount: 456,
    featured: false,
    rank: 3,
    createdAt: getClientTimestamp(),
    updatedAt: getClientTimestamp(),
    // UI extensions
    image: { baseName: 'tourisme_essaouira_remparts_skala_001', folder: 'impact-eleve/ACTE_2_EXPLORATION' },
    guide: 'Équipe Surf Essaouira',
    originalPrice: 380,
    currency: 'MAD'
  },
  {
    // Backend required fields
    id: 'act-atlas-004',
    providerId: 'prov-mohammed-004',
    title: 'Randonnée Atlas & villages berbères authentiques',
    description: 'Trek guidé dans les montagnes de l\'Atlas avec nuit chez l\'habitant et cuisine traditionnelle.',
    pricePerPerson: 520,
    category: ActivityCategory.NATURE,
    difficulty: ActivityDifficulty.CHALLENGING,
    status: 'ACTIVE',
    duration: 4320, // 72 heures en minutes
    maxGroupSize: 12,
    minGroupSize: 4,
    location: 'Haut Atlas',
    languages: ['Français', 'Tamazight', 'Arabe'],
    included: ['Guide montagne', 'Hébergement famille', 'Tous repas', 'Transport mulets'],
    instantBooking: true,
    commission: 18,
    rating: 4.9,
    reviewCount: 312,
    featured: false,
    rank: 4,
    createdAt: getClientTimestamp(),
    updatedAt: getClientTimestamp(),
    // UI extensions
    image: { baseName: 'architecture_maroc_detail_mauresque_003', folder: 'impact-eleve/ACTE_1_EVEIL' },
    guide: 'Mohammed Montagnard',
    originalPrice: 680,
    currency: 'MAD'
  },
  {
    // Backend required fields
    id: 'act-marrakech-005',
    providerId: 'prov-fatima-005',
    title: 'Atelier cuisine marocaine & visite souk Marrakech',
    description: 'Cours de cuisine avec chef local suivi d\'une exploration guidée des souks authentiques.',
    pricePerPerson: 190,
    category: ActivityCategory.CULINARY,
    difficulty: ActivityDifficulty.EASY,
    status: 'ACTIVE',
    duration: 300, // 5 heures en minutes
    maxGroupSize: 8,
    minGroupSize: 2,
    location: 'Marrakech',
    languages: ['Français', 'Arabe', 'Anglais'],
    included: ['Cours cuisine', 'Ingrédients', 'Visite souk', 'Recettes papier'],
    instantBooking: true,
    commission: 18,
    rating: 4.6,
    reviewCount: 789,
    featured: false,
    rank: 5,
    createdAt: getClientTimestamp(),
    updatedAt: getClientTimestamp(),
    // UI extensions
    image: { baseName: 'tourisme_chefchaouen_exploration_iconique_002', folder: 'impact-eleve/ACTE_2_EXPLORATION' },
    guide: 'Chef Fatima',
    originalPrice: 240,
    currency: 'MAD'
  }
];

const categories = [
  { name: 'Aventure', count: 24, color: 'bg-mbnb-coral', icon: '🏜️' },
  { name: 'Culture', count: 28, color: 'bg-mbnb-teal', icon: '🕌' },
  { name: 'Nature', count: 18, color: 'bg-mbnb-navy', icon: '🏔️' },
  { name: 'Sport', count: 12, color: 'bg-mbnb-coral', icon: '🏄' },
  { name: 'Gastronomie', count: 15, color: 'bg-mbnb-teal', icon: '🍽️' },
  { name: 'Artisanat', count: 13, color: 'bg-mbnb-navy', icon: '🎨' }
];

const regions = [
  { name: 'Marrakech', activities: 25 },
  { name: 'Fès', activities: 18 },
  { name: 'Sahara', activities: 15 },
  { name: 'Essaouira', activities: 12 },
  { name: 'Atlas', activities: 10 },
  { name: 'Chefchaouen', activities: 8 },
  { name: 'Casablanca', activities: 7 },
  { name: 'Tanger', activities: 5 }
];

export default function ActivitiesPage() {
  return (
    <main className="min-h-screen">
      {/* Hero Section */}
      <section className="relative h-[60vh] min-h-[500px] flex items-center justify-center">
        <div className="absolute inset-0">
          <MbnbResponsiveImage
            baseName="desert_kasbah_reflet_palmiers_006"
            folder="impact-eleve/ACTE_2_EXPLORATION"
            alt="TOP 100 Activités Mbnb Maroc"
            className="absolute inset-0 w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/70 to-black/50" />
        </div>
        <div className="relative z-10 text-center text-white">
          <div className="flex items-center justify-center mb-4">
            <TrophyIcon className="w-12 h-12 text-mbnb-coral mr-4" />
            <h1 className="text-5xl md:text-7xl font-bold">TOP 100</h1>
          </div>
          <p className="text-xl md:text-2xl max-w-4xl mx-auto mb-8">
            Activités authentiques au Maroc - Guides locaux certifiés - Commission 18% intégrée
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="#activities"
              className="bg-mbnb-coral hover:bg-mbnb-coral/90 text-white px-8 py-4 rounded-full font-semibold transition-colors text-lg"
            >
              Explorer les activités
            </Link>
            <Link
              href="#guides"
              className="border-2 border-white hover:bg-white hover:text-gray-900 text-white px-8 py-4 rounded-full font-semibold transition-colors text-lg"
            >
              Guides certifiés
            </Link>
          </div>
        </div>
      </section>

      {/* Stats & Commission Info */}
      <section className="py-16 bg-mbnb-navy">
        <div className="container-mbnb">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center text-white">
            <div>
              <div className="text-4xl font-bold mb-2">100</div>
              <div className="text-mbnb-sky">Activités Sélectionnées</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">18%</div>
              <div className="text-mbnb-sky">Commission Intégrée</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">450+</div>
              <div className="text-mbnb-sky">Guides Certifiés</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">4.8</div>
              <div className="text-mbnb-sky">Note Moyenne</div>
            </div>
          </div>
        </div>
      </section>

      {/* Search & Filters */}
      <section className="py-8 bg-gray-50">
        <div className="container-mbnb">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-3 text-gray-400" />
              <input
                type="text"
                placeholder="Rechercher une activité..."
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-mbnb-coral"
              />
            </div>
            <select className="px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-mbnb-coral">
              <option>Toutes les régions</option>
              {regions.map((region) => (
                <option key={region.name}>{region.name} ({region.activities})</option>
              ))}
            </select>
            <select className="px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-mbnb-coral">
              <option>Toutes les catégories</option>
              {categories.map((category) => (
                <option key={category.name}>{category.name} ({category.count})</option>
              ))}
            </select>
          </div>
        </div>
      </section>

      {/* Featured Activity */}
      <section id="activities" className="py-20">
        <div className="container-mbnb">
          <div className="mb-16">
            <h2 className="heading-2 text-gray-900 mb-8 flex items-center">
              <SparklesIcon className="w-8 h-8 mr-3 text-mbnb-coral" />
              Activité #1 du TOP 100
            </h2>
            <div className="bg-white rounded-3xl overflow-hidden shadow-2xl border-2 border-mbnb-coral">
              <div className="grid grid-cols-1 lg:grid-cols-2">
                <div className="relative h-96 lg:h-full">
                  <MbnbResponsiveImage
                    baseName={topActivities[0].image.baseName}
                    folder={topActivities[0].image.folder}
                    alt={topActivities[0].title}
                    className="object-cover"
                  />
                  <div className="absolute top-6 left-6">
                    <span className="bg-mbnb-coral text-white px-4 py-2 rounded-full font-bold text-lg">
                      #{topActivities[0].rank} TOP 100
                    </span>
                  </div>
                  <div className="absolute top-6 right-6">
                    <div className="bg-white/90 backdrop-blur-sm text-gray-900 px-3 py-2 rounded-lg flex items-center">
                      <StarIcon className="w-4 h-4 mr-1 text-yellow-400" />
                      <span className="font-semibold">{topActivities[0].rating}</span>
                      <span className="text-sm text-gray-600 ml-1">({topActivities[0].reviewCount})</span>
                    </div>
                  </div>
                  <div className="absolute bottom-6 left-6">
                    <div className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                      Commission {topActivities[0].commission}% incluse
                    </div>
                  </div>
                </div>
                <div className="p-8 lg:p-12">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="flex items-center text-sm text-mbnb-coral">
                      <MapPinIcon className="w-4 h-4 mr-1" />
                      <span className="font-medium">{topActivities[0].location}</span>
                    </div>
                    <span className="bg-mbnb-teal/10 text-mbnb-teal px-3 py-1 rounded-full text-sm font-medium">
                      {topActivities[0].category}
                    </span>
                  </div>

                  <h3 className="text-3xl font-bold text-gray-900 mb-4">
                    {topActivities[0].title}
                  </h3>
                  <p className="text-gray-600 mb-6 text-lg">
                    {topActivities[0].description}
                  </p>

                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="flex items-center text-sm text-gray-600">
                      <ClockIcon className="w-4 h-4 mr-2" />
                      {Math.floor(topActivities[0].duration / 60) >= 24
                        ? `${Math.floor(topActivities[0].duration / 1440)} jour${Math.floor(topActivities[0].duration / 1440) > 1 ? 's' : ''}`
                        : `${Math.floor(topActivities[0].duration / 60)} heure${Math.floor(topActivities[0].duration / 60) > 1 ? 's' : ''}`}
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <UserGroupIcon className="w-4 h-4 mr-2" />
                      {`${topActivities[0].minGroupSize}-${topActivities[0].maxGroupSize} personnes`}
                    </div>
                  </div>

                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-900 mb-3">Inclus dans l'expérience :</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {topActivities[0]?.included?.map((item, index) => (
                        <div key={index} className="flex items-center text-sm text-gray-600">
                          <span className="w-2 h-2 bg-mbnb-coral rounded-full mr-2"></span>
                          {item}
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex items-center gap-4 mb-8">
                    <span className="text-3xl font-bold text-mbnb-coral">
                      {topActivities[0]?.pricePerPerson} {topActivities[0]?.currency}
                    </span>
                    {topActivities[0]?.originalPrice && (
                      <span className="text-xl text-gray-500 line-through">
                        {topActivities[0]?.originalPrice} {topActivities[0]?.currency}
                      </span>
                    )}
                  </div>

                  <Link
                    href={`/activities/${topActivities[0].id}`}
                    className="inline-flex items-center bg-mbnb-coral hover:bg-mbnb-coral/90 text-white px-8 py-4 rounded-full font-semibold transition-colors text-lg"
                  >
                    Réserver maintenant
                    <ArrowRightIcon className="w-5 h-5 ml-2" />
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Activities Grid */}
      <section className="pb-20">
        <div className="container-mbnb">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Activities List */}
            <div className="lg:col-span-3">
              <h2 className="heading-2 text-gray-900 mb-8">TOP 100 Activités Authentiques</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {topActivities.slice(1).map((activity) => (
                  <div key={activity.id} className="bg-white rounded-2xl overflow-hidden shadow-xl hover:shadow-2xl transition-shadow">
                    <div className="relative h-64">
                      <MbnbResponsiveImage
                        baseName={activity.image.baseName}
                        folder={activity.image.folder}
                        alt={activity.title}
                        className="object-cover"
                      />
                      <div className="absolute top-4 left-4">
                        <span className="bg-mbnb-teal text-white px-3 py-1 rounded-full font-bold text-sm">
                          #{activity.rank}
                        </span>
                      </div>
                      <div className="absolute top-4 right-4">
                        <div className="bg-white/90 backdrop-blur-sm text-gray-900 px-2 py-1 rounded-lg flex items-center text-sm">
                          <StarIcon className="w-4 h-4 mr-1 text-yellow-400" />
                          <span className="font-semibold">{activity.rating}</span>
                        </div>
                      </div>
                      <div className="absolute bottom-4 left-4">
                        <span className={`px-3 py-1 rounded-full text-sm font-medium text-white ${
                          activity.difficulty === ActivityDifficulty.EASY ? 'bg-green-500' :
                          activity.difficulty === ActivityDifficulty.MODERATE ? 'bg-yellow-500' :
                          activity.difficulty === ActivityDifficulty.CHALLENGING ? 'bg-red-500' :
                          'bg-gray-500'
                        }`}>
                          {activity.difficulty === ActivityDifficulty.EASY ? 'Facile' :
                           activity.difficulty === ActivityDifficulty.MODERATE ? 'Modéré' :
                           activity.difficulty === ActivityDifficulty.CHALLENGING ? 'Difficile' :
                           activity.difficulty === ActivityDifficulty.EXPERT ? 'Expert' :
                           'Tous niveaux'}
                        </span>
                      </div>
                    </div>
                    <div className="p-6">
                      <div className="flex items-center gap-2 mb-3">
                        <MapPinIcon className="w-4 h-4 text-mbnb-coral" />
                        <span className="text-sm text-mbnb-coral font-medium">{activity.location}</span>
                        <span className="bg-mbnb-teal/10 text-mbnb-teal px-2 py-1 rounded-full text-xs">
                          {activity.category}
                        </span>
                      </div>

                      <h3 className="text-lg font-bold text-gray-900 mb-3 line-clamp-2">
                        {activity.title}
                      </h3>

                      <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
                        <div className="flex items-center">
                          <ClockIcon className="w-4 h-4 mr-1" />
                          {Math.floor(activity.duration / 60) >= 24
                            ? `${Math.floor(activity.duration / 1440)}j`
                            : `${Math.floor(activity.duration / 60)}h`}
                        </div>
                        <div className="flex items-center">
                          <UserGroupIcon className="w-4 h-4 mr-1" />
                          {`${activity.minGroupSize}-${activity.maxGroupSize}`}
                        </div>
                      </div>

                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-2">
                          <span className="text-2xl font-bold text-mbnb-coral">
                            {activity.pricePerPerson} {activity.currency}
                          </span>
                          {activity.originalPrice && (
                            <span className="text-sm text-gray-500 line-through">
                              {activity.originalPrice}
                            </span>
                          )}
                        </div>
                        <div className="text-xs text-green-600 font-medium">
                          +{activity.commission}% commission
                        </div>
                      </div>

                      <Link
                        href={`/activities/${activity.id}`}
                        className="w-full bg-mbnb-coral hover:bg-mbnb-coral/90 text-white px-4 py-3 rounded-full font-semibold transition-colors text-center block"
                      >
                        Réserver
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-8 space-y-6">
                {/* Categories */}
                <div className="bg-white rounded-2xl p-6 shadow-xl">
                  <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                    <TagIcon className="w-5 h-5 mr-2 text-mbnb-coral" />
                    Catégories
                  </h3>
                  <div className="space-y-3">
                    {categories.map((category) => (
                      <Link
                        key={category.name}
                        href={`/activities/category/${category.name.toLowerCase()}`}
                        className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-center">
                          <span className="mr-3 text-lg">{category.icon}</span>
                          <span className="font-medium text-gray-700">{category.name}</span>
                        </div>
                        <span className={`${category.color} text-white px-2 py-1 rounded-full text-xs font-medium`}>
                          {category.count}
                        </span>
                      </Link>
                    ))}
                  </div>
                </div>

                {/* Regions */}
                <div className="bg-white rounded-2xl p-6 shadow-xl">
                  <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                    <GlobeAltIcon className="w-5 h-5 mr-2 text-mbnb-coral" />
                    Régions
                  </h3>
                  <div className="space-y-2">
                    {regions.map((region) => (
                      <Link
                        key={region.name}
                        href={`/activities/region/${region.name.toLowerCase()}`}
                        className="flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors"
                      >
                        <span className="text-gray-700">{region.name}</span>
                        <span className="text-sm text-gray-500">{region.activities}</span>
                      </Link>
                    ))}
                  </div>
                </div>

                {/* Commission Info */}
                <div className="bg-mbnb-coral rounded-2xl p-6 text-white">
                  <h3 className="text-xl font-bold mb-4">Système Commission</h3>
                  <div className="space-y-4">
                    <div className="border-b border-white/20 pb-3">
                      <div className="flex justify-between items-center mb-1">
                        <span>Commission standard</span>
                        <span className="font-bold">18%</span>
                      </div>
                      <p className="text-sm text-white/80">Intégrée automatiquement</p>
                    </div>
                    <div>
                      <div className="flex justify-between items-center mb-1">
                        <span>Guides certifiés</span>
                        <span className="font-bold">100%</span>
                      </div>
                      <p className="text-sm text-white/80">Qualité garantie</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
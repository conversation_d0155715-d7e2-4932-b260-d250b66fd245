/**
 * Activities Feature - Barrel Export
 * Centralise tous les exports de la feature activities
 */

// Components
export { default as MbnbActivityCard } from '../../components/activities/MbnbActivityCard';
export { default as MbnbCommissionBadge } from '../../components/activities/MbnbCommissionBadge';

// Types (re-export depuis shared-types)
export { ActivityCategory, ActivityDifficulty } from '@mbnb/shared-types';

// Hooks (à créer si nécessaire)
// export { useActivities } from './hooks/useActivities';

// Utils (à créer si nécessaire)
// export { formatActivityPrice } from './utils/activityUtils';

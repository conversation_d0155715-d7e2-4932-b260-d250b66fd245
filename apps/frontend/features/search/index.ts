/**
 * Search Feature - Barrel Export
 * Centralise tous les exports de la feature search
 */

// Components
export { default as DateRangePicker } from '../../components/search/DateRangePicker';
export { default as GuestSelector } from '../../components/search/GuestSelector';
export { default as LocationAutocomplete } from '../../components/search/LocationAutocomplete';
export { default as SearchFilters } from '../../components/search/SearchFilters';
export { default as PropertyGrid } from '../../components/search/PropertyGrid';
export { default as MapView } from '../../components/search/MapView';
export { default as PriceRangeSlider } from '../../components/search/PriceRangeSlider';
export { default as AmenitiesFilters } from '../../components/search/AmenitiesFilters';
export { default as PropertyTypeFilters } from '../../components/search/PropertyTypeFilters';
export { default as InstantBookingToggle } from '../../components/search/InstantBookingToggle';
export { default as SaveSearch } from '../../components/search/SaveSearch';
export { default as SearchHeader } from '../../components/search/SearchHeader';

// Hooks (à créer si nécessaire)
// export { useSearch } from './hooks/useSearch';

// Utils (à créer si nécessaire)
// export { buildSearchQuery } from './utils/searchUtils';

/**
 * Payment Feature - Barrel Export
 * Centralise tous les exports de la feature payment
 */

// Components
export { default as MbnbPaymentFlow } from '../../components/payment/MbnbPaymentFlow';
export { default as PaymentMethod } from '../../components/reservation/PaymentMethod';

// Hooks (à créer si nécessaire)
// export { usePayment } from './hooks/usePayment';

// Utils (à créer si nécessaire)
// export { processPayment } from './utils/paymentUtils';

/**
 * Property Feature - Barrel Export
 * Centralise tous les exports de la feature property
 */

// Components
export { default as PropertyGallery } from '../../components/property/PropertyGallery';
export { default as PropertyInfo } from '../../components/property/PropertyInfo';
export { default as PropertyLocation } from '../../components/property/PropertyLocation';
export { default as PropertyAmenities } from '../../components/property/PropertyAmenities';
export { default as PropertyReviews } from '../../components/property/PropertyReviews';
export { default as PropertyRules } from '../../components/property/PropertyRules';
export { default as ReservationCard } from '../../components/property/ReservationCard';
export { default as SimilarProperties } from '../../components/property/SimilarProperties';
export { default as MbnbPropertyGallery } from '../../components/property/MbnbPropertyGallery';
export { default as MbnbPropertyMediaManager } from '../../components/property/MbnbPropertyMediaManager';

// Hooks (à créer si nécessaire)
// export { useProperty } from './hooks/useProperty';

// Utils (à créer si nécessaire)
// export { calculatePropertyPrice } from './utils/propertyUtils';

{"name": "mbnb-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev -p 3010", "build": "next build", "start": "next start", "lint": "eslint 'app/**/*.{ts,tsx}' 'components/**/*.{ts,tsx}' 'lib/**/*.{ts,tsx}' 'hooks/**/*.{ts,tsx}' 'stores/**/*.{ts,tsx}' --fix", "lint:check": "eslint 'app/**/*.{ts,tsx}' 'components/**/*.{ts,tsx}' 'lib/**/*.{ts,tsx}' 'hooks/**/*.{ts,tsx}' 'stores/**/*.{ts,tsx}'", "type-check": "tsc --noEmit", "test": "vitest", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:e2e:headed": "playwright test --headed", "test:e2e:chromium": "playwright test --project=chromium", "test:e2e:mobile": "playwright test --project=mobile-chrome --project=mobile-safari", "test:e2e:a11y": "playwright test tests/e2e/accessibility", "test:e2e:visual": "playwright test tests/e2e/visual", "test:e2e:update-snapshots": "playwright test tests/e2e/visual --update-snapshots", "playwright:install": "playwright install", "playwright:report": "playwright show-report"}, "dependencies": {"@mbnb/shared-types": "file:../../packages/shared-types", "@headlessui/react": "^2.2.8", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.3.4", "@sentry/nextjs": "^10.12.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.17.9", "@tanstack/react-query-devtools": "^5.17.9", "axios": "^1.6.5", "clsx": "^2.1.0", "critters": "^0.0.23", "date-fns": "^3.2.0", "date-fns-tz": "^2.0.0", "framer-motion": "^10.18.0", "i18next": "^25.3.6", "i18next-browser-languagedetector": "^8.2.0", "immer": "^10.1.3", "lru-cache": "^11.2.1", "lucide-react": "^0.294.0", "next": "^15.4.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.49.2", "react-hot-toast": "^2.4.1", "react-i18next": "^14.1.3", "react-icons": "^5.0.1", "recharts": "^2.15.4", "sharp": "^0.33.2", "tailwind-merge": "^2.2.0", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@axe-core/playwright": "^4.10.2", "@eslint/eslintrc": "^3.3.1", "@playwright/experimental-ct-react": "^1.55.0", "@playwright/test": "^1.55.0", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@types/node": "^20.11.0", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@types/recharts": "^1.8.29", "@typescript-eslint/eslint-plugin": "^8.44.0", "@typescript-eslint/parser": "^8.44.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "eslint": "8.56.0", "eslint-config-next": "14.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.4.0", "playwright-core": "^1.55.0", "postcss": "^8.4.33", "prettier": "^3.2.4", "tailwindcss": "^3.4.17", "typescript": "^5.9.2", "vitest": "^2.0.0"}}